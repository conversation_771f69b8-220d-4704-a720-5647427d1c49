/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* CSS Variables - 优化颜色对比度 */
:root {
    --bg-start: #667eea;
    --bg-end: #764ba2;
    --card-radius: 12px;
    --card-height: 112px;
    --card-shadow: 0 8px 18px rgba(0,0,0,0.12);
    --text-primary: #1f2937; /* 提升对比度 */
    --text-secondary: #4b5563; /* 提升对比度 */
    --text-light: #ffffff;
    --container-padding: 20px;
    --grid-gap: 16px;
    --border-radius-sm: 8px;
    --border-radius-md: 12px;
    --border-radius-lg: 16px;
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --focus-color: #2563eb;
    --focus-shadow: 0 0 0 3px rgba(37, 99, 235, 0.3);
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    :root {
        --bg-start: #000000;
        --bg-end: #1a1a1a;
        --text-primary: #ffffff;
        --text-secondary: #e5e7eb;
        --card-shadow: 0 8px 18px rgba(255,255,255,0.2);
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Base Typography */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: linear-gradient(135deg, var(--bg-start) 0%, var(--bg-end) 100%);
    min-height: 100vh;
    color: var(--text-primary);
    line-height: 1.6;
}

/* Skip Link for Accessibility */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--focus-color);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
    font-weight: 600;
}

.skip-link:focus {
    top: 6px;
}

/* Screen Reader Only */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Container */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--container-padding);
    width: 100%;
}

/* 超宽屏优化 */
@media (min-width: 1400px) {
    .container {
        max-width: 90%;
        padding: 0 40px;
    }
}

@media (min-width: 1600px) {
    .container {
        max-width: 85%;
        padding: 0 60px;
    }
}

@media (min-width: 1920px) {
    .container {
        max-width: 80%;
        padding: 0 80px;
    }
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 30px;
    color: var(--text-light);
    padding-top: 40px;
}

.header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 8px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.header p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 25px;
}

/* Progress Navigation */
.progress-nav {
    background: rgba(255,255,255,0.95);
    border-radius: var(--border-radius-lg);
    padding: 12px 16px;
    margin-bottom: 30px;
    backdrop-filter: blur(10px);
    box-shadow: 0 6px 25px rgba(0,0,0,0.1);
    position: sticky;
    top: 10px;
    z-index: 50;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.progress-nav::-webkit-scrollbar {
    display: none;
}

.progress-steps {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 16px;
    min-width: max-content;
    padding: 0 8px;
}

.progress-step {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 20px;
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-normal);
    font-size: 0.85rem;
    white-space: nowrap;
    flex-shrink: 0;
    min-height: 44px; /* 触摸目标优化 */
    touch-action: manipulation;
    color: var(--text-secondary);
}

/* 焦点样式增强 */
.progress-step:focus {
    outline: 2px solid var(--focus-color);
    outline-offset: 2px;
    box-shadow: var(--focus-shadow);
}

.step-content {
    display: flex;
    align-items: center;
    gap: 6px;
}

.step-emoji {
    color: lawngreen;
    font-size: 16px;
    line-height: 1;
}

.step-title {
    color: var(--text-secondary);
    white-space: nowrap;
}

.progress-step.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 3px 12px rgba(102, 126, 234, 0.4);
}

.progress-step.active .step-title {
    color: white;
}

.step-number {
    color: var(--text-secondary);
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.8rem;
}

.progress-step.active .step-number {
    background: rgba(255,255,255,0.2);
    color: white;
}

/* Search and Filter */
.search-filter {
    background: rgba(255,255,255,0.95);
    border-radius: var(--border-radius-lg);
    padding: 20px;
    margin-bottom: 30px;
    backdrop-filter: blur(10px);
    box-shadow: 0 6px 25px rgba(0,0,0,0.1);
}

.search-box {
    position: relative;
    margin-bottom: 16px;
}

.search-input {
    width: 100%;
    padding: 14px 40px 14px 16px;
    border: 2px solid #e5e7eb; /* 提升边框对比度 */
    border-radius: var(--border-radius-md);
    font-size: 0.9rem;
    transition: var(--transition-normal);
    background: #fff;
    min-height: 48px; /* 触摸目标优化 */
    box-sizing: border-box;
    color: var(--text-primary);
}

.search-input:focus {
    outline: none;
    border-color: var(--focus-color);
    box-shadow: var(--focus-shadow);
}

.search-input::placeholder {
    color: #9ca3af;
}

.search-icon {
    position: absolute;
    right: 40px;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
    font-size: 1rem;
    pointer-events: none;
}

.clear-search {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    font-size: 14px;
    padding: 4px;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: none;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.clear-search:hover {
    background: #f3f4f6;
    color: var(--text-secondary);
}

.clear-search:focus {
    outline: 2px solid var(--focus-color);
    outline-offset: 2px;
}

.clear-search.show {
    display: flex;
}

.filter-tags {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 12px;
    flex-wrap: wrap;
}

.filter-label {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    white-space: nowrap;
}

.filter-tag-list {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.filter-tag {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    border: 1px solid rgba(102, 126, 234, 0.2);
    padding: 6px 12px; /* 增加内边距 */
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: lowercase;
    min-height: 32px; /* 触摸目标优化 */
    display: flex;
    align-items: center;
}

.filter-tag:hover {
    background: rgba(102, 126, 234, 0.2);
    transform: translateY(-1px);
}

.filter-tag:focus {
    outline: 2px solid var(--focus-color);
    outline-offset: 2px;
}

.filter-tag.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-color: transparent;
}

/* Main Content */
.main-content {
    background: rgba(255,255,255,0.95);
    border-radius: 16px;
    padding: 25px;
    backdrop-filter: blur(10px);
    box-shadow: 0 6px 25px rgba(0,0,0,0.1);
}

.phase-section {
    margin-bottom: 40px;
}

.phase-title {
    font-size: 1.6rem;
    font-weight: 700;
    margin-bottom: 8px;
    color: var(--text-primary);
}

.phase-description {
    color: var(--text-secondary);
    margin-bottom: 25px;
    font-size: 1rem;
    line-height: 1.6;
}

.category-section {
    margin-bottom: 30px;
}

.category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid #f3f4f6;
}

.category-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.category-count {
    color: var(--text-secondary);
    font-size: 0.8rem;
}

/* Tool Grid - 修复响应式设计 */
.tool-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--grid-gap);
    align-items: start;
}

/* 响应式网格优化 - 更合理的尺寸分配 */
@media (max-width: 640px) {
    .tool-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }
}

@media (min-width: 641px) and (max-width: 768px) {
    .tool-grid {
        grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
        gap: 14px;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .tool-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 16px;
    }
}

@media (min-width: 1025px) and (max-width: 1200px) {
    .tool-grid {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 18px;
    }
}

/* 宽屏优化 - 保持合理的卡片尺寸 */
@media (min-width: 1201px) and (max-width: 1400px) {
    .tool-grid {
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        gap: 20px;
    }
}

@media (min-width: 1401px) and (max-width: 1600px) {
    .tool-grid {
        grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
        gap: 22px;
    }
}

@media (min-width: 1601px) and (max-width: 1920px) {
    .tool-grid {
        grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
        gap: 24px;
    }
}

@media (min-width: 1921px) and (max-width: 2560px) {
    .tool-grid {
        grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
        gap: 26px;
    }
}

@media (min-width: 2561px) {
    .tool-grid {
        grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
        gap: 28px;
    }
}

/* Enhanced Tool Card */
.tool-card {
    background: white;
    border-radius: var(--card-radius);
    overflow: hidden;
    text-decoration: none;
    color: inherit;
    transition: var(--transition-normal);
    box-shadow: var(--card-shadow);
    border: 1px solid rgba(0,0,0,0.06);
    position: relative;
    height: var(--card-height);
    display: flex;
    flex-direction: column;
    cursor: pointer;
    touch-action: manipulation;
    will-change: transform;
}

.tool-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 28px rgba(0,0,0,0.15);
}

.tool-card:active {
    transform: translateY(0);
    transition: var(--transition-fast);
}

.tool-card:focus {
    outline: 2px solid var(--focus-color);
    outline-offset: 2px;
    box-shadow: var(--focus-shadow);
}

@media (hover: none) {
    .tool-card:hover {
        transform: none;
        box-shadow: var(--card-shadow);
    }
}

.tool-card-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-size: cover;
    background-position: center;
    z-index: 1;
    transition: transform .4s ease, filter .3s ease;
    will-change: transform;
    filter: saturate(1.05) brightness(0.95);
}

.tool-card-bg::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(92, 55, 177, 0.6) 0%, rgba(92, 55, 177, 0.9) 100%);
    z-index: 1;
}

.tool-card:hover .tool-card-bg {
    transform: scale(1.05);
    filter: saturate(1.1) brightness(0.98);
}

.tool-card-content {
    position: relative;
    z-index: 3;
    padding: 16px; /* 增加内边距 */
    padding-top: 48px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    color: var(--text-light);
    text-shadow: 0 1px 2px rgba(0,0,0,0.35);
}

.tool-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 0;
    position: absolute;
    top: 8px;
    left: 8px;
    right: 40px;
}

.tool-logo {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    flex-shrink: 0;
    box-shadow: 0 3px 8px rgba(102, 126, 234, 0.35);
    background-color: rgba(255, 255, 255, 0.90);
}

.tool-info {
    flex: 1;
}

.tool-title {
    font-size: 0.95rem;
    font-weight: 700;
    margin-bottom: 2px;
    color: var(--text-light);
    line-height: 1.3;
}

.tool-description {
    color: rgba(255,255,255,0.92);
    font-size: 0.82rem;
    line-height: 1.4; /* 提升行高 */
    margin-bottom: 0;
    flex: 0 0 auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.tool-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: 8px;
}

.tag {
    background: rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 0.9);
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: 500;
    text-transform: lowercase;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.tag.tag-highlighted {
    background: rgba(255, 255, 0, 0.3);
    border-color: rgba(255, 255, 0, 0.5);
    transform: scale(1.05);
    box-shadow: 0 0 8px rgba(255, 255, 0, 0.4);
}

.info-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 4;
    width: 26px;
    height: 26px;
    border-radius: 50%;
    background: rgba(0,0,0,0.2);
    border: 1px solid rgba(255,255,255,0.25);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: var(--text-light);
    transition: all 0.2s ease;
    backdrop-filter: blur(4px);
    text-decoration: none;
}

.info-btn:hover {
    background: rgba(0,0,0,0.55);
    transform: scale(1.08);
}

.info-btn:focus {
    outline: 2px solid var(--text-light);
    outline-offset: 2px;
}

/* Modal */
.modal-overlay {
    position: fixed;
    inset: 0;
    background: rgba(0,0,0,0.45);
    display: none;
    align-items: center;
    justify-content: center;
    padding: 20px;
    z-index: 200;
    backdrop-filter: blur(4px);
}

.modal-overlay.open {
    display: flex;
}

.modal {
    width: min(720px, 95vw);
    max-height: 90vh;
    background: #fff;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: 0 20px 50px rgba(0,0,0,0.25);
    position: relative;
    display: flex;
    flex-direction: column;
}

.modal-hero {
    height: 180px;
    background-size: cover;
    background-position: center;
    position: relative;
}

.modal-hero::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0.15), rgba(0,0,0,0.55));
}

.modal-content {
    padding: 16px 18px 20px;
    overflow-y: auto;
    flex: 1;
}

.modal-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-top: -24px;
}

.modal-logo {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: var(--text-light);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    box-shadow: 0 6px 18px rgba(102,126,234,.5);
}

.modal h3 {
    margin: 0;
    font-size: 1.1rem;
    color: var(--text-primary);
}

.modal-desc {
    color: var(--text-secondary);
    margin: 10px 0 12px;
    line-height: 1.6;
}

.modal-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.modal-tags .chip {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 999px;
    background: #f3f4f6;
    color: var(--text-primary);
    border: 1px solid #e5e7eb;
}

.modal-actions {
    margin-top: 16px;
    display: flex;
    gap: 10px;
}

.btn-primary {
    padding: 8px 14px;
    border-radius: 10px;
    color: var(--text-light);
    background: linear-gradient(135deg, #667eea, #764ba2);
    text-decoration: none;
    box-shadow: 0 6px 16px rgba(102,126,234,.4);
    transition: var(--transition-fast);
    font-weight: 500;
}

.btn-primary:hover {
    filter: brightness(1.05);
    transform: translateY(-1px);
}

.btn-primary:focus {
    outline: 2px solid var(--focus-color);
    outline-offset: 2px;
}

.modal-close {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: none;
    background: rgba(0,0,0,0.5);
    color: var(--text-light);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    line-height: 1;
    transition: var(--transition-fast);
}

.modal-close:hover {
    background: rgba(0,0,0,0.65);
}

.modal-close:focus {
    outline: 2px solid var(--text-light);
    outline-offset: 2px;
}

/* Loading Animation */
.loading {
    display: none;
    text-align: center;
    padding: 30px;
    color: var(--text-secondary);
}

.spinner {
    width: 30px;
    height: 30px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 错误状态样式 */
.error-state {
    display: none;
    text-align: center;
    padding: 40px;
    color: var(--text-secondary);
}

.error-icon {
    font-size: 48px;
    color: #ef4444;
    margin-bottom: 16px;
}

.error-message {
    font-size: 18px;
    margin-bottom: 8px;
    color: var(--text-primary);
    font-weight: 600;
}

.error-description {
    font-size: 14px;
    margin-bottom: 24px;
    line-height: 1.5;
}

.retry-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    font-size: 14px;
    transition: var(--transition-fast);
    font-weight: 500;
}

.retry-btn:hover {
    filter: brightness(1.05);
    transform: translateY(-1px);
}

.retry-btn:focus {
    outline: 2px solid var(--focus-color);
    outline-offset: 2px;
}

/* 空状态样式 */
.empty-state {
    display: none;
    text-align: center;
    padding: 60px 20px;
    color: var(--text-secondary);
}

.empty-icon {
    font-size: 64px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-title {
    font-size: 20px;
    margin-bottom: 8px;
    color: var(--text-primary);
    font-weight: 600;
}

.empty-description {
    font-size: 14px;
    max-width: 400px;
    margin: 0 auto;
    line-height: 1.5;
}

/* 响应式设计优化 */
@media (max-width: 480px) {
    :root {
        --container-padding: 12px;
        --grid-gap: 12px;
    }

    .header h1 {
        font-size: 1.8rem;
        margin-bottom: 6px;
    }

    .header p {
        font-size: 1rem;
        margin-bottom: 20px;
    }

    .progress-nav {
        padding: 8px 12px;
        margin-bottom: 20px;
    }

    .progress-steps {
        gap: 12px;
        padding: 0 4px;
    }

    .progress-step {
        padding: 6px 10px;
        font-size: 0.8rem;
        min-height: 40px;
    }

    .search-filter {
        padding: 16px;
        margin-bottom: 20px;
    }

    .search-input {
        padding: 12px 36px 12px 14px;
        font-size: 16px; /* 防止iOS缩放 */
        min-height: 44px;
    }

    .main-content {
        padding: 20px;
    }

    .phase-title {
        font-size: 1.4rem;
    }

    .category-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .filter-tag-list {
        justify-content: flex-start;
    }

    .filter-tag {
        font-size: 12px;
        padding: 6px 10px;
    }

    .modal {
        width: 95vw;
        max-height: 85vh;
        border-radius: var(--border-radius-md);
    }

    .modal-content {
        padding: 14px 16px 18px;
    }

    .modal-hero {
        height: 140px;
    }
}

@media (min-width: 481px) and (max-width: 768px) {
    :root {
        --container-padding: 16px;
    }

    .header h1 {
        font-size: 2.2rem;
    }

    .progress-steps {
        justify-content: center;
        flex-wrap: wrap;
        gap: 12px;
    }

    .search-filter {
        padding: 18px;
    }

    .main-content {
        padding: 22px;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .progress-steps {
        justify-content: center;
        gap: 20px;
    }
}

@media (min-width: 1025px) {
    .progress-steps {
        justify-content: center;
        gap: 24px;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .progress-step,
    .filter-tag,
    .clear-search,
    .info-btn {
        min-height: 44px;
        min-width: 44px;
    }

    .tool-card {
        transition: var(--transition-fast);
    }
}

/* 高DPI显示器优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .tool-logo {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* 打印样式 */
@media print {
    .header,
    .progress-nav,
    .search-filter,
    .modal-overlay,
    .loading {
        display: none !important;
    }

    .tool-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }

    body {
        background: white;
        color: black;
    }
}

/* 密度控制 */
body.density-compact { --card-height: 112px; }
body.density-comfortable { --card-height: 150px; }
body.density-cozy { --card-height: 180px; }
