<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- SEO优化 -->
    <title>AIPO上站工具箱 - 网站开发全流程工具集合</title>
    <meta name="description" content="AIPO上站工具箱提供网站开发全流程工具集合，从选词到运营的完整解决方案，包含前端开发、设计、SEO、分析等专业工具。">
    <meta name="keywords" content="网站开发,前端工具,设计工具,SEO工具,网站分析,开发工具箱,AIPO">
    <meta name="author" content="AIPO">
    
    <!-- Open Graph -->
    <meta property="og:title" content="AIPO上站工具箱 - 网站开发全流程工具集合">
    <meta property="og:description" content="网站开发全流程工具集合，从选词到运营的完整解决方案">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://aipo-tools.com">
    <meta property="og:image" content="./assets/og-image.jpg">
    <meta property="og:site_name" content="AIPO上站工具箱">
    
    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="AIPO上站工具箱">
    <meta name="twitter:description" content="网站开发全流程工具集合">
    <meta name="twitter:image" content="./assets/og-image.jpg">
    
    <!-- 安全性 -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self';">
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    
    <!-- 预加载关键资源 -->
    <link rel="preload" href="./css/main.css" as="style">
    <link rel="preload" href="./js/app.js" as="script">
    <link rel="preload" href="./data/tools-data.json" as="fetch" crossorigin>
    
    <!-- 样式表 -->
    <link rel="stylesheet" href="./css/main.css">
    
    <!-- 结构化数据 -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "AIPO上站工具箱",
        "description": "网站开发全流程工具集合，从选词到运营的完整解决方案",
        "url": "https://aipo-tools.com",
        "applicationCategory": "DeveloperApplication",
        "operatingSystem": "Any",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "CNY"
        }
    }
    </script>
</head>
<body>
    <!-- Skip Navigation for Accessibility -->
    <a href="#main-content" class="skip-link" aria-label="跳转到主内容">跳转到主内容</a>
    
    <div class="container">
        <!-- Header -->
        <header class="header" role="banner">
            <h1>🚢 AIPO上站工具箱</h1>
            <p>网站开发全流程工具集合 - 从选词到运营的完整解决方案</p>
        </header>

        <!-- Progress Navigation -->
        <nav class="progress-nav" role="navigation" aria-label="进度导航">
            <div class="progress-steps" id="progressSteps" role="tablist">
                <!-- 动态生成 -->
            </div>
        </nav>

        <!-- Search and Filter -->
        <section class="search-filter" role="search" aria-label="搜索和筛选">
            <div class="search-box">
                <label for="searchInput" class="sr-only">搜索工具</label>
                <input type="text" 
                       id="searchInput" 
                       class="search-input" 
                       placeholder="搜索工具名称、描述或标签..."
                       aria-describedby="search-help"
                       autocomplete="off">
                <div class="search-icon" aria-hidden="true">🔍</div>
                <button class="clear-search" 
                        id="clearSearch" 
                        onclick="clearSearch()" 
                        title="清空搜索"
                        aria-label="清空搜索">✕</button>
            </div>
            <div id="search-help" class="sr-only">输入关键词搜索工具，支持工具名称、描述和标签搜索</div>
            
            <div class="filter-tags" role="group" aria-label="标签筛选">
                <span class="filter-label">标签筛选：</span>
                <div class="filter-tag-list" id="filterTagList" role="group">
                    <!-- 动态生成的标签筛选按钮 -->
                </div>
            </div>
        </section>

        <!-- Main Content -->
        <main class="main-content" id="main-content" role="main">
            <!-- 动态生成内容 -->
        </main>

        <!-- Loading State -->
        <div class="loading" id="loading" role="status" aria-live="polite">
            <div class="spinner" aria-hidden="true"></div>
            <p>正在加载工具信息...</p>
        </div>
        
        <!-- Error State -->
        <div id="errorState" class="error-state" role="alert" aria-live="assertive">
            <div class="error-icon" aria-hidden="true">⚠️</div>
            <div class="error-message">加载失败</div>
            <div class="error-description">无法加载工具数据，请检查网络连接或稍后重试</div>
            <button class="retry-btn" onclick="retryLoadData()" aria-label="重新加载数据">重新加载</button>
        </div>
        
        <!-- Empty State -->
        <div id="emptyState" class="empty-state" role="status" aria-live="polite">
            <div class="empty-icon" aria-hidden="true">🔍</div>
            <div class="empty-title">没有找到匹配的工具</div>
            <div class="empty-description">尝试调整搜索关键词或筛选条件，或者清空搜索重新开始</div>
        </div>
    </div>

    <!-- Tool Info Modal -->
    <div id="toolModal" class="modal-overlay" aria-hidden="true" role="dialog" aria-modal="true" aria-labelledby="modalTitle">
        <div class="modal">
            <button class="modal-close" 
                    id="modalClose" 
                    aria-label="关闭对话框"
                    type="button">×</button>
            <div class="modal-hero" id="modalHero" aria-hidden="true"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <div class="modal-logo" id="modalLogo" aria-hidden="true">🔧</div>
                    <h3 id="modalTitle"></h3>
                </div>
                <p class="modal-desc" id="modalDesc"></p>
                <div class="modal-tags" id="modalTags" role="list"></div>
                <div class="modal-actions">
                    <a id="modalOpenBtn" 
                       class="btn-primary" 
                       href="#" 
                       target="_blank" 
                       rel="noopener noreferrer"
                       aria-label="在新窗口中打开工具">打开工具</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="./js/app.js"></script>
</body>
</html>
