/**
 * AIPO工具箱 - 主应用程序
 * 优化版本：增强错误处理、可访问性和性能
 */

// 全局变量
let toolsData = null;
let allTools = [];
let searchTimeout = null;

// 配置常量
const CONFIG = {
    DATA_URL: './data/tools-data.json',
    RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 1000,
    SEARCH_DEBOUNCE: 300,
    INTERSECTION_THRESHOLD: 0.3,
    INTERSECTION_ROOT_MARGIN: '-80px 0px -80px 0px'
};

// 错误类型
class DataLoadError extends Error {
    constructor(message, status) {
        super(message);
        this.name = 'DataLoadError';
        this.status = status;
    }
}

/**
 * 应用程序初始化
 */
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * 初始化应用程序
 */
async function initializeApp() {
    try {
        showLoading();
        await loadToolsData();
        hideLoading();
        
        renderProgressNav();
        renderMainContent();
        generateFilterTags(toolsData);
        initializeEventListeners();
        initializeModalElements();
        setupVirtualScrolling();
        setupAccessibility();
        
        // 应用默认密度
        applyDensity('compact');
        
        console.log('应用程序初始化完成');
    } catch (error) {
        console.error('应用程序初始化失败:', error);
        showErrorState(error.message);
    }
}

/**
 * 加载工具数据 - 增强版本，支持重试和fallback
 */
async function loadToolsData(retries = CONFIG.RETRY_ATTEMPTS) {
    try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时
        
        const response = await fetch(CONFIG.DATA_URL, {
            signal: controller.signal,
            headers: {
                'Accept': 'application/json',
                'Cache-Control': 'no-cache'
            }
        });
        
        clearTimeout(timeoutId);
        
        if (!response.ok) {
            throw new DataLoadError(`HTTP错误: ${response.status}`, response.status);
        }
        
        const data = await response.json();
        
        // 验证数据结构
        if (!data || !data.phases || !Array.isArray(data.phases)) {
            throw new DataLoadError('数据格式无效');
        }
        
        toolsData = data;
        
        // 扁平化所有工具数据
        allTools = [];
        toolsData.phases.forEach(phase => {
            if (phase.categories && Array.isArray(phase.categories)) {
                phase.categories.forEach(category => {
                    if (category.tools && Array.isArray(category.tools)) {
                        category.tools.forEach(tool => {
                            allTools.push({
                                ...tool,
                                phaseId: phase.id,
                                categoryId: category.id
                            });
                        });
                    }
                });
            }
        });
        
        console.log(`成功加载 ${allTools.length} 个工具`);
        
    } catch (error) {
        console.error('数据加载失败:', error);
        
        if (retries > 0 && !(error instanceof DataLoadError && error.status === 404)) {
            console.log(`重试加载数据，剩余尝试次数: ${retries - 1}`);
            await new Promise(resolve => setTimeout(resolve, CONFIG.RETRY_DELAY));
            return loadToolsData(retries - 1);
        }
        
        // 尝试使用fallback数据
        const fallbackData = getFallbackData();
        if (fallbackData) {
            console.warn('使用fallback数据');
            toolsData = fallbackData;
            allTools = [];
            // 处理fallback数据...
            return;
        }
        
        throw error;
    }
}

/**
 * 获取fallback数据
 */
function getFallbackData() {
    // 返回基本的fallback数据结构
    return {
        phases: [
            {
                id: 'fallback',
                title: '🔧 基础工具',
                description: '基础开发工具集合',
                categories: [
                    {
                        id: 'basic',
                        title: '基础工具',
                        tools: [
                            {
                                id: 'fallback-tool',
                                title: '数据加载失败',
                                description: '请检查网络连接或稍后重试',
                                url: '#',
                                tags: ['系统'],
                                logo: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiByeD0iOCIgZmlsbD0iI0Y3RjhGOSIvPgo8cGF0aCBkPSJNMTYgMTBWMTZMMjAgMjAiIHN0cm9rZT0iIzZCNzI4MCIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+',
                                screenshot: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDQwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjIwMCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LXNpemU9IjE0Ij7mlbDmja7liqDovb3lpLHotKU8L3RleHQ+Cjwvc3ZnPg=='
                            }
                        ]
                    }
                ]
            }
        ]
    };
}

/**
 * 显示加载状态
 */
function showLoading() {
    const loadingEl = document.getElementById('loading');
    const mainContent = document.getElementById('mainContent');
    const errorState = document.getElementById('errorState');
    
    if (loadingEl) loadingEl.style.display = 'block';
    if (mainContent) mainContent.style.display = 'none';
    if (errorState) errorState.style.display = 'none';
}

/**
 * 隐藏加载状态
 */
function hideLoading() {
    const loadingEl = document.getElementById('loading');
    const mainContent = document.getElementById('main-content');

    if (loadingEl) loadingEl.style.display = 'none';
    if (mainContent) mainContent.style.display = 'block';
}

/**
 * 显示错误状态
 */
function showErrorState(message = '加载失败') {
    const loadingEl = document.getElementById('loading');
    const errorState = document.getElementById('errorState');
    const mainContent = document.getElementById('main-content');

    if (loadingEl) loadingEl.style.display = 'none';
    if (errorState) {
        errorState.style.display = 'block';
        const errorMsg = errorState.querySelector('.error-message');
        if (errorMsg) {
            errorMsg.textContent = message;
        }
    }
    if (mainContent) mainContent.style.display = 'none';
}

/**
 * 重试加载数据
 */
async function retryLoadData() {
    const errorState = document.getElementById('errorState');
    const mainContent = document.getElementById('main-content');

    if (errorState) errorState.style.display = 'none';
    if (mainContent) mainContent.style.display = 'block';

    try {
        await initializeApp();
    } catch (error) {
        console.error('重试失败:', error);
        showErrorState(error.message);
    }
}

/**
 * 显示空状态
 */
function showEmptyState() {
    const emptyState = document.getElementById('emptyState');
    if (emptyState) {
        emptyState.style.display = 'block';
        // 设置焦点到空状态，便于屏幕阅读器
        emptyState.setAttribute('tabindex', '-1');
        emptyState.focus();
    }
}

/**
 * 隐藏空状态
 */
function hideEmptyState() {
    const emptyState = document.getElementById('emptyState');
    if (emptyState) {
        emptyState.style.display = 'none';
        emptyState.removeAttribute('tabindex');
    }
}

/**
 * 渲染进度导航
 */
function renderProgressNav() {
    const progressSteps = document.getElementById('progressSteps');
    if (!progressSteps || !toolsData) return;
    
    progressSteps.innerHTML = toolsData.phases.map((phase, index) => {
        // 使用更通用的正则表达式匹配任何表情符号
        const emojiMatch = phase.title.match(/^([\u{1F300}-\u{1F9FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}])\s*(.+)$/u);
        const emoji = emojiMatch ? emojiMatch[1] : '';
        const titleText = emojiMatch ? emojiMatch[2] : phase.title;
        
        return `
            <a href="#phase-${phase.id}" 
               class="progress-step ${index === 0 ? 'active' : ''}"
               role="tab"
               aria-selected="${index === 0}"
               aria-controls="phase-${phase.id}"
               tabindex="${index === 0 ? '0' : '-1'}">
                <div class="step-number">${index + 1}</div>
                <div class="step-content">
                    <span class="step-emoji" aria-hidden="true">${emoji}</span>
                    <span class="step-title">${titleText}</span>
                </div>
            </a>
        `;
    }).join('');
}

/**
 * 渲染主要内容
 */
function renderMainContent() {
    const mainContent = document.getElementById('main-content');
    if (!mainContent || !toolsData) return;

    mainContent.innerHTML = toolsData.phases.map(phase => `
        <section id="phase-${phase.id}"
                 class="phase-section"
                 role="tabpanel"
                 aria-labelledby="phase-${phase.id}-tab">
            <h2 class="phase-title">${phase.title}</h2>
            <p class="phase-description">${phase.description}</p>
            ${phase.categories.map(category => `
                <div class="category-section">
                    <div class="category-header">
                        <h3 class="category-title">${category.title}</h3>
                        <span class="category-count" aria-label="${category.tools.length} 个工具">${category.tools.length} 个工具</span>
                    </div>
                    <div class="tool-grid" role="grid" aria-label="${category.title} 工具列表">
                        ${category.tools.map(tool => renderToolCard(tool)).join('')}
                    </div>
                </div>
            `).join('')}
        </section>
    `).join('');
}

/**
 * 渲染工具卡片 - 增强可访问性
 */
function renderToolCard(tool) {
    const tagsHtml = tool.tags ? tool.tags.map(tag =>
        `<span class="tag" role="listitem">${escapeHtml(tag)}</span>`
    ).join('') : '';

    const toolTitle = escapeHtml(tool.title);
    const toolDescription = escapeHtml(tool.description);
    const toolUrl = escapeHtml(tool.url);
    const infoUrl = escapeHtml(tool.infoUrl || tool.url);

    return `
        <div class="tool-card"
             role="gridcell"
             data-tags="${tool.tags ? tool.tags.join(' ') : ''}"
             onclick="openTool('${toolUrl}')"
             tabindex="0"
             aria-label="查看 ${toolTitle} 工具详情"
             onkeydown="handleCardKeydown(event, '${toolUrl}')">
            <div class="tool-card-bg"
                 style="background-image: url('${escapeHtml(tool.screenshot)}')"
                 aria-hidden="true"></div>
            <a class="info-btn"
               href="${infoUrl}"
               target="_blank"
               rel="noopener noreferrer"
               onclick="event.stopPropagation();"
               title="查看 ${toolTitle} 详细信息"
               aria-label="查看 ${toolTitle} 详细信息">ℹ</a>
            <div class="tool-card-content">
                 <div class="tool-header">
                     <img class="tool-logo"
                          src="${escapeHtml(tool.logo)}"
                          alt="${toolTitle} 图标"
                          loading="lazy"
                          onerror="this.style.display='none'">
                     <div class="tool-info">
                         <h4 class="tool-title">${toolTitle}</h4>
                     </div>
                 </div>
                 <p class="tool-description" title="${toolDescription}">${toolDescription}</p>
                 <div class="tool-tags" role="list" aria-label="工具标签">
                     ${tagsHtml}
                 </div>
             </div>
        </div>
    `;
}

/**
 * HTML转义函数，防止XSS攻击
 */
function escapeHtml(text) {
    if (typeof text !== 'string') return '';
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}

/**
 * 处理工具卡片键盘事件
 */
function handleCardKeydown(event, url) {
    if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        openTool(url);
    }
}

/**
 * 打开工具链接
 */
function openTool(url) {
    if (url && url !== '#') {
        // 记录用户行为（可选）
        console.log('打开工具:', url);
        window.open(url, '_blank', 'noopener,noreferrer');
    }
}

/**
 * 虚拟滚动优化
 */
function setupVirtualScrolling() {
    const toolGrids = document.querySelectorAll('.tool-grid');
    if (!toolGrids.length) return;

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target.querySelector('img[data-src]');
                if (img) {
                    img.src = img.dataset.src;
                    img.removeAttribute('data-src');
                    observer.unobserve(entry.target);
                }
            }
        });
    }, {
        rootMargin: '50px'
    });

    // 观察所有工具卡片
    toolGrids.forEach(grid => {
        const toolCards = grid.querySelectorAll('.tool-card');
        toolCards.forEach(card => observer.observe(card));
    });
}

/**
 * 初始化事件监听器
 */
function initializeEventListeners() {
    const searchInput = document.getElementById('searchInput');
    const clearSearchBtn = document.getElementById('clearSearch');
    const progressSteps = document.querySelectorAll('.progress-step');

    // 搜索功能 - 防抖优化
    if (searchInput) {
        searchInput.addEventListener('input', handleSearchInput);
        searchInput.addEventListener('keydown', handleSearchKeydown);
    }

    // 清空搜索按钮
    if (clearSearchBtn) {
        clearSearchBtn.addEventListener('click', clearSearch);
    }

    // 进度导航
    progressSteps.forEach((step, index) => {
        step.addEventListener('click', (e) => handleProgressStepClick(e, step, index));
        step.addEventListener('keydown', (e) => handleProgressStepKeydown(e, step, index));
    });

    // 全局键盘导航
    document.addEventListener('keydown', handleGlobalKeydown);

    // 滚动监听
    setupScrollObserver();
}

/**
 * 处理搜索输入
 */
function handleSearchInput(e) {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        filterTools();
    }, CONFIG.SEARCH_DEBOUNCE);

    // 立即更新清空按钮状态
    const clearBtn = document.getElementById('clearSearch');
    if (clearBtn) {
        clearBtn.classList.toggle('show', !!e.target.value);
    }
}

/**
 * 处理搜索键盘事件
 */
function handleSearchKeydown(e) {
    if (e.key === 'Enter') {
        clearTimeout(searchTimeout);
        filterTools();
    } else if (e.key === 'Escape') {
        clearSearch();
    }
}

/**
 * 处理进度步骤点击
 */
function handleProgressStepClick(e, step, index) {
    e.preventDefault();
    activateProgressStep(step, index);

    const targetId = step.getAttribute('href');
    const targetSection = document.querySelector(targetId);
    if (targetSection) {
        targetSection.scrollIntoView({ behavior: 'smooth' });
        // 设置焦点到目标区域
        targetSection.setAttribute('tabindex', '-1');
        targetSection.focus();
    }
}

/**
 * 处理进度步骤键盘事件
 */
function handleProgressStepKeydown(e, step, index) {
    const progressSteps = document.querySelectorAll('.progress-step');

    switch(e.key) {
        case 'Enter':
        case ' ':
            e.preventDefault();
            step.click();
            break;
        case 'ArrowLeft':
        case 'ArrowUp':
            e.preventDefault();
            const prevIndex = index > 0 ? index - 1 : progressSteps.length - 1;
            focusProgressStep(progressSteps[prevIndex], prevIndex);
            break;
        case 'ArrowRight':
        case 'ArrowDown':
            e.preventDefault();
            const nextIndex = index < progressSteps.length - 1 ? index + 1 : 0;
            focusProgressStep(progressSteps[nextIndex], nextIndex);
            break;
        case 'Home':
            e.preventDefault();
            focusProgressStep(progressSteps[0], 0);
            break;
        case 'End':
            e.preventDefault();
            focusProgressStep(progressSteps[progressSteps.length - 1], progressSteps.length - 1);
            break;
    }
}

/**
 * 激活进度步骤
 */
function activateProgressStep(step, index) {
    const progressSteps = document.querySelectorAll('.progress-step');
    progressSteps.forEach((s, i) => {
        s.classList.toggle('active', i === index);
        s.setAttribute('aria-selected', i === index);
        s.setAttribute('tabindex', i === index ? '0' : '-1');
    });
}

/**
 * 聚焦进度步骤
 */
function focusProgressStep(step, index) {
    activateProgressStep(step, index);
    step.focus();
}

/**
 * 全局键盘导航处理
 */
function handleGlobalKeydown(e) {
    // ESC 键关闭模态框
    if (e.key === 'Escape') {
        closeModal();
        return;
    }

    // Ctrl/Cmd + K 快速搜索
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.focus();
            searchInput.select();
        }
        return;
    }
}

/**
 * 搜索和筛选工具 - 增强版本
 */
function filterTools() {
    const searchTerm = document.getElementById('searchInput')?.value.toLowerCase().trim() || '';
    const toolCards = document.querySelectorAll('.tool-card');
    const clearBtn = document.getElementById('clearSearch');

    // 显示/隐藏清空按钮
    if (clearBtn) {
        clearBtn.classList.toggle('show', !!searchTerm);
    }

    // 获取激活的标签筛选
    const activeTagFilters = Array.from(document.querySelectorAll('.filter-tag.active'))
        .map(tag => tag.textContent.toLowerCase().trim());

    let visibleCount = 0;

    toolCards.forEach(card => {
        const title = card.querySelector('.tool-title')?.textContent.toLowerCase() || '';
        const description = card.querySelector('.tool-description')?.textContent.toLowerCase() || '';
        const tags = (card.dataset.tags || '').toLowerCase();

        // 搜索匹配
        const searchMatch = !searchTerm ||
            title.includes(searchTerm) ||
            description.includes(searchTerm) ||
            tags.includes(searchTerm);

        // 标签筛选匹配
        const tagMatch = activeTagFilters.length === 0 ||
            activeTagFilters.some(filterTag => tags.includes(filterTag));

        // 同时满足搜索和标签筛选条件
        const shouldShow = searchMatch && tagMatch;
        card.style.display = shouldShow ? 'block' : 'none';

        // 更新ARIA属性
        card.setAttribute('aria-hidden', !shouldShow);

        if (shouldShow) {
            visibleCount++;
        }
    });

    // 显示或隐藏空状态
    if (visibleCount === 0 && (searchTerm || activeTagFilters.length > 0)) {
        showEmptyState();
    } else {
        hideEmptyState();
    }

    // 更新分类计数
    updateCategoryCounts();

    // 高亮匹配的标签
    highlightMatchingTags(searchTerm);

    // 通知屏幕阅读器
    announceSearchResults(visibleCount, searchTerm, activeTagFilters);
}

/**
 * 通知搜索结果给屏幕阅读器
 */
function announceSearchResults(count, searchTerm, activeFilters) {
    const announcement = `找到 ${count} 个匹配的工具${searchTerm ? `，搜索词：${searchTerm}` : ''}${activeFilters.length ? `，筛选标签：${activeFilters.join('、')}` : ''}`;

    // 创建临时的live region来通知结果
    const liveRegion = document.createElement('div');
    liveRegion.setAttribute('aria-live', 'polite');
    liveRegion.setAttribute('aria-atomic', 'true');
    liveRegion.className = 'sr-only';
    liveRegion.textContent = announcement;

    document.body.appendChild(liveRegion);

    // 清理
    setTimeout(() => {
        document.body.removeChild(liveRegion);
    }, 1000);
}

/**
 * 更新分类工具计数
 */
function updateCategoryCounts() {
    const categoryHeaders = document.querySelectorAll('.category-header');
    categoryHeaders.forEach(header => {
        const categorySection = header.parentElement;
        const visibleCards = Array.from(categorySection.querySelectorAll('.tool-card'))
            .filter(card => card.style.display !== 'none');

        const countEl = header.querySelector('.category-count');
        if (countEl) {
            const visibleCount = visibleCards.length;
            countEl.textContent = `${visibleCount} 个工具`;
            countEl.setAttribute('aria-label', `${visibleCount} 个工具`);
        }
    });
}

/**
 * 清空搜索
 */
function clearSearch() {
    const searchInput = document.getElementById('searchInput');
    const filterTags = document.querySelectorAll('.filter-tag');

    if (searchInput) {
        searchInput.value = '';
    }

    filterTags.forEach(tag => {
        tag.classList.remove('active');
        tag.setAttribute('aria-pressed', 'false');
    });

    filterTools();

    // 聚焦到搜索框
    if (searchInput) {
        searchInput.focus();
    }
}

/**
 * 高亮匹配的标签
 */
function highlightMatchingTags(searchTerm) {
    const allTags = document.querySelectorAll('.tag');

    allTags.forEach(tag => {
        tag.classList.remove('tag-highlighted');
        if (searchTerm && tag.textContent.toLowerCase().includes(searchTerm)) {
            tag.classList.add('tag-highlighted');
        }
    });
}

/**
 * 设置滚动观察器
 */
function setupScrollObserver() {
    const sections = document.querySelectorAll('.phase-section');
    const progressSteps = document.querySelectorAll('.progress-step');

    if (!sections.length || !progressSteps.length) return;

    const observerOptions = {
        threshold: CONFIG.INTERSECTION_THRESHOLD,
        rootMargin: CONFIG.INTERSECTION_ROOT_MARGIN
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const sectionId = entry.target.id;
                const correspondingStep = document.querySelector(`[href="#${sectionId}"]`);

                if (correspondingStep) {
                    const stepIndex = Array.from(progressSteps).indexOf(correspondingStep);
                    if (stepIndex >= 0) {
                        activateProgressStep(correspondingStep, stepIndex);
                    }
                }
            }
        });
    }, observerOptions);

    sections.forEach(section => {
        observer.observe(section);
    });
}

/**
 * 应用密度设置
 */
function applyDensity(density) {
    document.body.classList.remove('density-compact', 'density-comfortable', 'density-cozy');
    document.body.classList.add(`density-${density}`);
}

/**
 * 生成标签筛选按钮
 */
function generateFilterTags(toolsData) {
    const allTags = new Set();

    // 收集所有标签
    toolsData.phases.forEach(phase => {
        phase.categories.forEach(category => {
            category.tools.forEach(tool => {
                if (tool.tags && Array.isArray(tool.tags)) {
                    tool.tags.forEach(tag => allTags.add(tag));
                }
            });
        });
    });

    // 生成筛选按钮
    const filterTagList = document.getElementById('filterTagList');
    if (!filterTagList) return;

    const sortedTags = Array.from(allTags).sort();
    filterTagList.innerHTML = sortedTags.map(tag => `
        <button class="filter-tag"
                onclick="toggleFilterTag(this)"
                onkeydown="handleFilterTagKeydown(event, this)"
                aria-pressed="false"
                role="button"
                tabindex="0">
            ${escapeHtml(tag)}
        </button>
    `).join('');
}

/**
 * 切换筛选标签
 */
function toggleFilterTag(tagElement) {
    const isActive = tagElement.classList.contains('active');
    tagElement.classList.toggle('active');
    tagElement.setAttribute('aria-pressed', !isActive);

    filterTools();
}

/**
 * 处理筛选标签键盘事件
 */
function handleFilterTagKeydown(event, tagElement) {
    if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        toggleFilterTag(tagElement);
    }
}

// Modal 相关变量
let modalEl, modalHeroEl, modalTitleEl, modalDescEl, modalTagsEl, modalOpenBtnEl, modalCloseEl;
let previousFocusElement = null;

/**
 * 初始化模态框元素
 */
function initializeModalElements() {
    modalEl = document.getElementById('toolModal');
    modalHeroEl = document.getElementById('modalHero');
    modalTitleEl = document.getElementById('modalTitle');
    modalDescEl = document.getElementById('modalDesc');
    modalTagsEl = document.getElementById('modalTags');
    modalOpenBtnEl = document.getElementById('modalOpenBtn');
    modalCloseEl = document.getElementById('modalClose');

    if (modalCloseEl) {
        modalCloseEl.addEventListener('click', closeModal);
    }

    if (modalEl) {
        modalEl.addEventListener('click', (e) => {
            if (e.target === modalEl) closeModal();
        });
    }
}

/**
 * 显示工具详情模态框
 */
function showToolInfo(toolId) {
    const tool = allTools.find(t => t.id === toolId);
    if (!tool || !modalEl) return;

    // 保存当前焦点元素
    previousFocusElement = document.activeElement;

    if (modalHeroEl) {
        modalHeroEl.style.backgroundImage = tool.screenshot ? `url('${tool.screenshot}')` : 'none';
    }

    if (modalTitleEl) {
        modalTitleEl.textContent = tool.title;
    }

    if (modalDescEl) {
        modalDescEl.textContent = tool.description;
    }

    if (modalTagsEl) {
        modalTagsEl.innerHTML = `
            <span class="chip" role="listitem">${tool.price === 'free' ? '免费' : '付费'}</span>
            <span class="chip" role="listitem">${tool.difficulty === 'easy' ? '易用' : tool.difficulty === 'medium' ? '中等' : '高级'}</span>
            ${tool.tags ? tool.tags.map(tag => `<span class="chip" role="listitem">#${escapeHtml(tag)}</span>`).join('') : ''}
        `;
    }

    if (modalOpenBtnEl) {
        modalOpenBtnEl.href = tool.url;
    }

    openModal();
}

/**
 * 打开模态框
 */
function openModal() {
    if (!modalEl) return;

    modalEl.classList.add('open');
    modalEl.setAttribute('aria-hidden', 'false');
    document.body.style.overflow = 'hidden';

    // 设置焦点到关闭按钮
    if (modalCloseEl) {
        modalCloseEl.focus();
    }

    // 设置焦点陷阱
    setupModalFocusTrap();
}

/**
 * 关闭模态框
 */
function closeModal() {
    if (!modalEl) return;

    modalEl.classList.remove('open');
    modalEl.setAttribute('aria-hidden', 'true');
    document.body.style.overflow = '';

    // 恢复之前的焦点
    if (previousFocusElement) {
        previousFocusElement.focus();
        previousFocusElement = null;
    }

    // 移除焦点陷阱
    removeModalFocusTrap();
}

/**
 * 设置模态框焦点陷阱
 */
function setupModalFocusTrap() {
    if (!modalEl) return;

    const focusableElements = modalEl.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );

    const firstFocusable = focusableElements[0];
    const lastFocusable = focusableElements[focusableElements.length - 1];

    function handleModalKeydown(e) {
        if (e.key === 'Tab') {
            if (e.shiftKey) {
                if (document.activeElement === firstFocusable) {
                    e.preventDefault();
                    lastFocusable.focus();
                }
            } else {
                if (document.activeElement === lastFocusable) {
                    e.preventDefault();
                    firstFocusable.focus();
                }
            }
        }
    }

    modalEl.addEventListener('keydown', handleModalKeydown);
    modalEl._focusTrapHandler = handleModalKeydown;
}

/**
 * 移除模态框焦点陷阱
 */
function removeModalFocusTrap() {
    if (modalEl && modalEl._focusTrapHandler) {
        modalEl.removeEventListener('keydown', modalEl._focusTrapHandler);
        delete modalEl._focusTrapHandler;
    }
}

/**
 * 设置可访问性增强
 */
function setupAccessibility() {
    // 为工具卡片添加键盘支持
    const toolCards = document.querySelectorAll('.tool-card');
    toolCards.forEach((card, index) => {
        card.setAttribute('tabindex', '0');
        card.setAttribute('role', 'button');

        const title = card.querySelector('.tool-title')?.textContent || '';
        card.setAttribute('aria-label', `查看 ${title} 工具详情`);
    });

    // 为筛选标签添加ARIA属性
    const filterTags = document.querySelectorAll('.filter-tag');
    filterTags.forEach(tag => {
        tag.setAttribute('role', 'button');
        tag.setAttribute('aria-pressed', 'false');
    });

    // 设置搜索区域的ARIA标签
    const searchFilter = document.querySelector('.search-filter');
    if (searchFilter) {
        searchFilter.setAttribute('role', 'search');
        searchFilter.setAttribute('aria-label', '搜索和筛选工具');
    }

    // 设置进度导航的ARIA属性
    const progressNav = document.querySelector('.progress-nav');
    if (progressNav) {
        progressNav.setAttribute('role', 'navigation');
        progressNav.setAttribute('aria-label', '页面导航');
    }
}

/**
 * 错误处理和日志记录
 */
window.addEventListener('error', function(e) {
    console.error('JavaScript错误:', e.error);

    // 可以在这里添加错误报告逻辑
    // reportError(e.error);
});

window.addEventListener('unhandledrejection', function(e) {
    console.error('未处理的Promise拒绝:', e.reason);

    // 可以在这里添加错误报告逻辑
    // reportError(e.reason);
});

/**
 * 性能监控
 */
if ('performance' in window) {
    window.addEventListener('load', function() {
        setTimeout(function() {
            const perfData = performance.getEntriesByType('navigation')[0];
            console.log('页面加载性能:', {
                domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
                loadComplete: perfData.loadEventEnd - perfData.loadEventStart,
                totalTime: perfData.loadEventEnd - perfData.fetchStart
            });
        }, 0);
    });
}

// 导出全局函数供HTML调用
window.retryLoadData = retryLoadData;
window.clearSearch = clearSearch;
window.toggleFilterTag = toggleFilterTag;
window.handleFilterTagKeydown = handleFilterTagKeydown;
window.openTool = openTool;
window.handleCardKeydown = handleCardKeydown;
