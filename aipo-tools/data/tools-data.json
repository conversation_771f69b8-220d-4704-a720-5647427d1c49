{"phases": [{"id": "newbie", "title": "🌱 新手", "description": "新手入门基础必备，从注册账号开始", "categories": [{"id": "register", "title": "🔑 账号注册", "tools": [{"id": "google-account", "title": "Google账号注册", "description": "基础中的基础，授权登录其他平台。", "url": "https://accounts.google.com/lifecycle/steps/signup/emailsignup?continue=https://console.cloud.google.com/freetrial?facet_utm_source%3D(direct)%26facet_utm_campaign%3D(direct)%26facet_utm_medium%3D(none)%26facet_url%3Dhttps://cloud.google.com&flowName=GlifWebSignIn&service=cloudconsole&TL=ALgCv6x6JRj7zA66AzWgXN1bEB3OCOJV1dYTSSKQGSIh92CMGYQeTqhuQDrvMnwB", "logo": "https://www.google.com/favicon.ico", "screenshot": "https://lh3.googleusercontent.com/cjcqhPAiG_vn0Zr2JgBQbDD0H839QPGoGOrWki8ilKKRj5V6LdXw96Yp82_oRyMlpQ292hujh-ezLNOartd2uOz2AAkGtapQkgQgThs=rw-e365-w400-v1", "infoUrl": "https://erlitech.feishu.cn/wiki/GACcwDEzciq4xGkSA9FcxF17nfh?from=from_copylink", "rating": 5, "tags": ["google", "account"]}, {"id": "github-account", "title": "Github账号注册", "description": "代码中的基础，授权登录其他平台。", "url": "https://github.com/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F&source=header-home", "logo": "https://github.githubassets.com/favicons/favicon.svg", "screenshot": "https://github.githubassets.com/assets/github-octocat-13c86b8b336d.png", "infoUrl": "https://erlitech.feishu.cn/wiki/PWO9wEe6piuXpTkClJycSKGcnzc?from=from_copylink", "rating": 5, "tags": ["code", "account"]}, {"id": "microsoft-account", "title": "Microsoft账号注册", "description": "授权登录其他平台", "url": "https://signup.live.com/signup", "logo": "https://acctcdn.msauth.net/images/favicon.ico?v=2", "screenshot": "https://logincdn.msftauth.net/shared/5/images/fluent_web_light_2_145a07dcb971527a82b8.svg", "infoUrl": "", "rating": 5, "tags": ["account"]}, {"id": "discord", "title": "Discord", "description": "社区平台，提供了聊天、语音和视频通话功能。", "url": "https://discord.com", "logo": "https://cdn.prod.website-files.com/6257adef93867e50d84d30e2/6266bc493fb42d4e27bb8393_847541504914fd33810e70a0ea73177e.ico", "screenshot": "https://cdn.prod.website-files.com/6257adef93867e50d84d30e2/665643dd8c7ac752237b5cef_Discord-OG-1200x630.jpg", "infoUrl": "", "rating": 5, "tags": ["community"]}]}, {"id": "ai-chat", "title": "🤖 AI对话", "tools": [{"id": "chatgpt", "title": "ChatGPT", "description": "OpenAI的头牌", "url": "https://chatgpt.com/", "logo": "https://cdn.oaistatic.com/assets/favicon-eex17e9e.ico", "screenshot": "https://cdn.oaistatic.com/assets/chatgpt-share-og-u7j5uyao.webp", "infoUrl": "https://erlitech.feishu.cn/wiki/YisZw8hJ3id5C2kMH9eci2P8nXe?from=from_copylink", "rating": 5, "tags": ["ai", "chat"]}, {"id": "gemini", "title": "Gemini", "description": "Google的头牌", "url": "https://gemini.google.com", "logo": "https://www.gstatic.com/lamda/images/gemini_sparkle_aurora_33f86dc0c0257da337c63.svg", "screenshot": "https://www.gstatic.com/lamda/images/gemini_aurora_thumbnail_4g_e74822ff0ca4259beb718.png", "infoUrl": "https://erlitech.feishu.cn/wiki/OJZSwhKsMik7DKkswbqcf2icnUg?from=from_copylink", "rating": 5, "tags": ["ai", "chat"]}, {"id": "grok", "title": "Grok", "description": "马斯克的杀手锏", "url": "https://grok.com", "logo": "https://grok.com/images/favicon-light.png", "screenshot": "https://logincdn.msftauth.net/shared/5/images/fluent_web_light_2_145a07dcb971527a82b8.svg", "infoUrl": "https://erlitech.feishu.cn/wiki/PnD3w3nL1ibo2yksjVQcG4YWnwd?from=from_copylink", "rating": 5, "tags": ["ai", "chat"]}, {"id": "claude", "title": "<PERSON>", "description": "最强编程", "url": "https://claude.ai", "logo": "https://claude.ai/favicon.ico", "screenshot": "https://claude.ai/images/claude_ogimage.png", "infoUrl": "", "rating": 5, "tags": ["ai", "chat"]}, {"id": "perplexity", "title": "Perplexity", "description": "最强搜索", "url": "https://www.perplexity.ai/", "logo": "https://www.perplexity.ai/favicon.ico", "screenshot": "https://ppl-ai-public.s3.amazonaws.com/static/img/pplx-default-preview.png", "infoUrl": "", "rating": 5, "tags": ["ai", "chat"]}, {"id": "manus", "title": "<PERSON><PERSON>", "description": "托管式AI智能体平台，支持多模态交互和自动化任务执行", "url": "https://manus.ai/", "logo": "https://manus.ai/favicon.ico", "screenshot": "https://manus.ai/images/preview.png", "infoUrl": "https://docs.manus.ai/", "rating": 4.6, "tags": ["ai", "agent", "automation"]}, {"id": "flowith", "title": "<PERSON><PERSON>", "description": "可视化画布式AI智能体，支持流程图式的AI工作流设计", "url": "https://flowith.io/", "logo": "https://flowith.io/favicon.ico", "screenshot": "https://flowith.io/images/canvas-preview.png", "infoUrl": "https://help.flowith.io/", "rating": 4.5, "tags": ["ai", "workflow", "visual", "canvas"]}, {"id": "coze-space", "title": "扣子空间", "description": "字节跳动推出的AI智能体开发平台，支持插件和工具集成", "url": "https://space.coze.cn/", "logo": "https://lf-coze-web-cdn.coze.cn/obj/coze-web-cn/coze-space/static/favicon.ico", "screenshot": "https://lf-coze-web-cdn.coze.cn/obj/coze-web-cn/coze-space/static/preview.jpg", "infoUrl": "https://space.coze.cn/docs/", "rating": 4.7, "tags": ["ai", "agent", "cn", "bytedance", "plugins"]}]}]}, {"id": "selection", "title": "🎯 选词", "description": "明确站点定位，调研用户意图与竞品，产出高搜索量、低竞争度的核心与长尾关键词清单。", "categories": [{"id": "idea", "title": "💡 创意聚合", "tools": [{"id": "toolify", "title": "Toolify", "description": "最佳的AI工具导航站", "url": "https://www.toolify.ai/", "logo": "https://www.toolify.ai/favicon.ico", "screenshot": "https://cdn.toolify.ai/default.webp", "infoUrl": "https://erlitech.feishu.cn/wiki/IVMawOX9eiT5d0kYZUdchSQjnph?from=from_copylink", "rating": 4.8, "tags": ["导航站", "idea"]}, {"id": "boringcashcow", "title": "BoringCashCow", "description": "专注于发现和分享盈利性商业模式的创意平台，帮助创业者找到可行的商业想法", "url": "https://boringcashcow.com/", "logo": "https://boringcashcow.com/favicon.ico", "screenshot": "https://boringcashcow.com/images/preview.jpg", "infoUrl": "https://erlitech.feishu.cn/wiki/YVFgwrnXiia7DQks4zqcKcP5n0b?from=from_copylink", "rating": 4.6, "tags": ["idea", "business", "startup"]}, {"id": "youquhome", "title": "有趣网址之家", "description": "精选全球有趣、实用、创新的网站资源，为创意工作者提供灵感来源", "url": "https://youquhome.com/", "logo": "https://youquhome.com/favicon.ico", "screenshot": "https://youquhome.com/images/homepage.png", "infoUrl": "https://erlitech.feishu.cn/wiki/PYFCwAC1iiXgXokrOiVchVvUnPd?from=from_copylink", "rating": 4.4, "tags": ["idea", "inspiration", "cn", "directory"]}]}, {"id": "keywords", "title": "🔥 热词分析", "tools": [{"id": "google-trends", "title": "Google Trends", "description": "查看关键词在不同时间、不同地区、不同设备上的搜索热度变化的工具。", "url": "https://trends.google.com/", "logo": "https://www.google.com/favicon.ico", "screenshot": "", "infoUrl": "https://erlitech.feishu.cn/wiki/JBQzwz8Q1idhlLk6TW8c0coknQe?from=from_copylink", "rating": 4.8, "tags": ["google", "keywords"]}, {"id": "keywords-planner", "title": "Keywords Planner", "description": "Google Ads关键词规划工具，帮助广告主优化关键词策略。", "url": "https://ads.google.com/aw/keywordplanner/home", "logo": "https://www.gstatic.com/awn/awsm//brt/awn_awsm_auto_20250828-0848_RC000/aw_blend/ads_favicon.ico", "screenshot": "", "infoUrl": "https://erlitech.feishu.cn/wiki/VOyZwFS4LitGdBki6HUcoV84nSg?from=from_copylink", "rating": 4.8, "tags": ["google", "keywords"]}, {"id": "similarweb", "title": "SimilarWeb", "description": "提供网站排名、流量、流量来源、流量路径、流量转换等数据。", "url": "https://sim.3ue.com/", "logo": "https://www.similarweb.com/favicon.ico", "screenshot": "https://static-us-west-2.similarcdn.com/build/20250831.master.6840f9c/dist/scripts/lite-app/assets/e23cf3508cf10cb3b580.png", "infoUrl": "https://erlitech.feishu.cn/wiki/UcVpwAeN1iutMHkxYB4c9J3Inzh?from=from_copylink", "rating": 4.8, "tags": ["seo", "analytics", "keywords"]}, {"id": "semrush", "title": "SEMRUSH", "description": "提供网站排名、流量、流量来源、流量路径、流量转换等数据。", "url": "https://sem.3ue.com/", "logo": "https://www.semrush.com/__static__/favicon.f8cd638f087a.ico", "screenshot": "https://static.semrush.com/wt-static/semrush-com.png", "infoUrl": "https://erlitech.feishu.cn/wiki/XwSwwhWUaiRqDjkarGjcJp9Envh?from=from_copylink", "rating": 4.8, "tags": ["seo", "analytics", "keywords"]}, {"id": "ahrefs", "title": "<PERSON><PERSON><PERSON>", "description": "专业的SEO分析工具，提供关键词研究、竞争对手分析和外链监控。", "url": "https://ahrefs.com/", "logo": "https://static.ahrefs.com/favicon.ico?v=2", "screenshot": "https://static.ahrefs.com/assets/img/og/ahrefs.png?v=2", "infoUrl": "https://erlitech.feishu.cn/wiki/HalEwSDZxiF7rikYERUcAevXnvg?from=from_copylink", "rating": 4.7, "tags": ["seo", "keywords", "analytics"]}]}, {"id": "site", "title": "📊 网站分析", "tools": [{"id": "aitdk", "title": "AITDK", "description": "AI SEO工具，有浏览器插件", "url": "https://aitdk.com/", "logo": "https://aitdk.com/favicon.svg", "screenshot": "https://aitdk.com/images/logo/social.png", "infoUrl": "https://erlitech.feishu.cn/wiki/IwmxwxM1di8sCXklYZNcHft1nTb?from=from_copylink", "rating": 4.8, "tags": ["seo", "ai"]}, {"id": "archive", "title": "Archive", "description": "网站的历史版本", "url": "https://archive.org/", "logo": "https://archive.org/offshoot_assets/favicon.ico", "screenshot": "", "infoUrl": "https://erlitech.feishu.cn/wiki/HLQtwisPYiiNKFkysQtcwXVpnPe?from=from_copylink", "rating": 4.9, "tags": ["site"]}]}, {"id": "domain", "title": "🌐 域名工具", "tools": [{"id": "namecheap", "title": "NameCheap", "description": "域名注册商，价格实惠，提供免费隐私保护和DNS管理服务。", "url": "https://namecheap.com/domains/", "logo": "https://www.namecheap.com/assets/img/nc-icon/favicon.ico", "screenshot": "", "infoUrl": "https://erlitech.feishu.cn/wiki/FAspwHZV1ilxdVkrVdVcAj2SnHb?from=from_copylink", "rating": 4.8, "tags": ["domain"]}, {"id": "cloudflare-domain", "title": "Cloudflare Domain", "description": "域名注册服务，提供免费的域名注册和管理。", "url": "https://domains.cloudflare.com/", "logo": "https://domains.cloudflare.com/favicon.svg", "screenshot": "", "infoUrl": "https://erlitech.feishu.cn/wiki/MQZzwZMxFiWJ2PkFkIsc69x5n6f?from=from_copylink", "rating": 4.8, "tags": ["domain"]}, {"id": "<PERSON><PERSON>dy", "title": "<PERSON><PERSON><PERSON>", "description": "域名注册服务，提供免费的域名注册和管理。", "url": "https://www.godaddy.com/", "logo": "https://img6.wsimg.com/ux-assets/favicon/favicon-32x32.png", "screenshot": "https://img1.wsimg.com/cdn/Image/All/All/2/en-US/be0a3335-75ef-4e1a-b8c7-12a82706ce4d/og-godaddy.jpg", "infoUrl": "https://erlitech.feishu.cn/wiki/VLVew2i3VixmyTk1pJ8c1Vgrnkb?from=from_copylink", "rating": 4.8, "tags": ["domain"]}, {"id": "aliyun-domain", "title": "阿里云域名", "description": "原万网，提供免费的域名注册和管理。", "url": "https://wanwang.aliyun.com/domain", "logo": "https://img.alicdn.com/tfs/TB1_ZXuNcfpK1RjSZFOXXa6nFXa-32-32.ico", "screenshot": "", "infoUrl": "https://erlitech.feishu.cn/wiki/T0rjw3xTBis7KckAGOzcgQlTnCd?from=from_copylink", "rating": 4.8, "tags": ["domain", "cn"]}, {"id": "volcengine-domain", "title": "火山引擎域名", "description": "原新网，提供免费的域名注册和管理。", "url": "https://www.volcengine.com/product/domain-service/search", "logo": "https://portal.volccdn.com/obj/volcfe/misc/favicon.png", "screenshot": "", "infoUrl": "https://erlitech.feishu.cn/wiki/WXWhwmoQsiu2nAkYlZqcnOnOnPg?from=from_copylink", "rating": 4.8, "tags": ["domain", "cn"]}]}]}, {"id": "design", "title": "🎨  设计", "description": "基于选词确定网站品牌与信息架构，确定配色、排版、定价等，输出简易版PRD。", "categories": [{"id": "demo-generate", "title": "⚡ Demo生成", "tools": [{"id": "replit", "title": "Replit", "description": "基于AI的代码编辑器，支持多种编程语言，提供免费的代码托管和协作功能。", "url": "https://replit.com/", "logo": "https://cdn.replit.com/dotcom/favicon-196.png", "screenshot": "https://cdn.sanity.io/images/bj34pdbp/migration/2017ad20cbb1770bcb0d23d6d4be8ff9a5105df1-1200x650.png?auto=format&q=75&w=200&format=png", "infoUrl": "", "rating": 4.8, "tags": ["coding", "ai"]}, {"id": "bolt", "title": "<PERSON><PERSON>", "description": "基于AI的代码编辑器，支持多种编程语言，提供免费的代码托管和协作功能。", "url": "https://bolt.new/", "logo": "https://bolt.new/static/favicon.svg", "screenshot": "https://bolt.new/static/social_preview_index.jpg", "infoUrl": "", "rating": 4.7, "tags": ["coding", "ai"]}, {"id": "lovable", "title": "Lovable", "description": "基于AI的代码编辑器，支持多种编程语言，提供免费的代码托管和协作功能。", "url": "https://lovable.dev/", "logo": "https://lovable.dev/favicon.ico", "screenshot": "https://lovable.dev/opengraph-image.png?e7ae4aa2faea9aea", "infoUrl": "", "rating": 4.6, "tags": ["coding", "ai"]}, {"id": "v0", "title": "V0", "description": "基于AI的代码编辑器，支持多种编程语言，提供免费的代码托管和协作功能。", "url": "https://v0.app/", "logo": "https://v0.app/assets/icon-light-32x32.png", "screenshot": "https://v0.app/chat/api/og", "infoUrl": "", "rating": 4.8, "tags": ["coding", "ai"]}, {"id": "same", "title": "Same", "description": "圈友作品，复刻网站", "url": "https://same.new/", "logo": "https://same.new/favicon.ico", "screenshot": "https://same.new/thumbnail.png", "infoUrl": "", "rating": 4.5, "tags": ["coding", "ai"]}]}, {"id": "template-select", "title": "📋 模版选择", "tools": [{"id": "shipany", "title": "ShipAny", "description": "快速上线 AI SaaS 项目的模版", "url": "https://shipany.ai/", "logo": "https://shipany.ai/favicon.ico", "screenshot": "", "infoUrl": "https://erlitech.feishu.cn/wiki/KSlqwADn5iwlAikyl7kcEDTwnsg?from=from_copylink", "rating": 4.8, "tags": ["template"]}, {"id": "wordpress", "title": "WordPress", "description": "全球最受欢迎的内容管理系统，拥有丰富的主题和插件生态。", "url": "https://wordpress.org/", "logo": "https://images.unsplash.com/photo-1432888498266-38ffec3eaf0a?w=32&h=32&fit=crop", "screenshot": "https://images.unsplash.com/photo-1432888498266-38ffec3eaf0a?w=400&h=200&fit=crop", "infoUrl": "https://wordpress.org/support/", "rating": 4.6, "tags": ["cms"]}]}, {"id": "model-try", "title": "🧪 模型体验", "tools": [{"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "阿里云百炼", "description": "全链路大模型服务与应用开发平台", "url": "https://bailian.console.aliyun.com//", "logo": "https://gw.alicdn.com/imgextra/i4/O1CN01vVn7g32134zNZEeAR_!!6000000006928-55-tps-24-24.svg", "screenshot": "", "infoUrl": "", "rating": 4.8, "tags": ["cn"]}, {"id": "volcengine-exp", "title": "火山方舟", "description": "全球最受欢迎的内容管理系统，拥有丰富的主题和插件生态。", "url": "https://exp.volcengine.com/", "logo": "https://portal.volccdn.com/obj/volcfe/misc/favicon.png", "screenshot": "", "infoUrl": "", "rating": 4.6, "tags": ["cn"]}, {"id": "google-aistudio", "title": "Google AI Studio", "description": "Google 提供的 AI 开发平台，支持模型训练、推理和部署。", "url": "https://aistudio.google.com/", "logo": "https://www.gstatic.com/aistudio/ai_studio_favicon_2_32x32.png", "screenshot": "", "infoUrl": "", "rating": 4.8, "tags": []}, {"id": "huggingface-spaces", "title": "Hugging Face Spaces", "description": "Hugging Face 提供的 AI 模型和应用托管平台，支持模型训练、推理和部署。", "url": "https://huggingface.co/spaces", "logo": "https://huggingface.co/favicon.ico", "screenshot": "", "infoUrl": "", "rating": 4.8, "tags": []}, {"id": "openai-playground", "title": "OpenAI Playground", "description": "OpenAI 提供的 AI 模型和应用托管平台，支持模型训练、推理和部署。", "url": "https://playground.openai.com/", "logo": "https://platform.openai.com/favicon-platform-alt.png", "screenshot": "", "infoUrl": "", "rating": 4.8, "tags": []}, {"id": "replicate", "title": "Replicate", "description": "Replicate 提供的 AI 模型和应用托管平台，支持模型训练、推理和部署。", "url": "https://replicate.com/", "logo": "https://replicate.com/assets/favicon.png", "screenshot": "", "infoUrl": "", "rating": 4.8, "tags": []}, {"id": "modelscope", "title": "ModelScope", "description": "ModelScope 提供的 AI 模型和应用托管平台，支持模型训练、推理和部署。", "url": "https://modelscope.cn/", "logo": "https://g.alicdn.com/sail-web/maas/2.9.69/favicon/128.ico", "screenshot": "", "infoUrl": "", "rating": 4.8, "tags": []}]}]}, {"id": "development", "title": "☕ 开发", "description": "基于PRD和基础模版进行功能开发，接入API，完善单元/集成测试与性能优化。", "categories": [{"id": "code-repository", "title": "📦 代码仓库", "tools": [{"id": "github", "title": "GitHub", "description": "全球最大的代码托管平台，提供版本控制、协作开发和项目管理功能", "url": "https://github.com/", "logo": "https://github.com/favicon.ico", "screenshot": "", "infoUrl": "https://docs.github.com/", "rating": 4.9, "tags": ["git"]}, {"id": "gitlab", "title": "GitLab", "description": "完整的DevOps平台，提供代码托管、CI/CD、项目管理等一体化解决方案", "url": "https://gitlab.com/", "logo": "https://gitlab.com/favicon.ico", "screenshot": "", "infoUrl": "https://docs.gitlab.com/", "rating": 4.7, "tags": ["git"]}, {"id": "gitee", "title": "<PERSON><PERSON><PERSON>", "description": "国内领先的代码托管平台，提供Git仓库管理、代码审查、项目协作等服务", "url": "https://gitee.com/", "logo": "https://gitee.com/favicon.ico", "screenshot": "", "infoUrl": "https://gitee.com/help", "rating": 4.5, "tags": ["git"]}, {"id": "codeup", "title": "CodeUp", "description": "阿里云旗下的企业级代码管理平台，提供安全可靠的代码托管和协作服务", "url": "https://codeup.aliyun.com/", "logo": "https://img.alicdn.com/tfs/TB1_ZXuNcfpK1RjSZFOXXa6nFXa-32-32.ico", "screenshot": "", "infoUrl": "https://help.aliyun.com/product/153741.html", "rating": 4.3, "tags": ["git"]}]}, {"id": "coding", "title": "💻 编程工具", "tools": [{"id": "cursor", "title": "<PERSON><PERSON><PERSON>", "description": "AI原生代码编辑器，基于VSCode构建，提供强大的AI辅助编程功能和自然语言代码生成", "url": "https://cursor.sh/", "logo": "https://cursor.sh/favicon.ico", "screenshot": "https://cursor.sh/images/hero-screenshot.png", "infoUrl": "https://cursor.sh/docs/", "rating": 4.9, "tags": ["coding", "ai", "editor"]}, {"id": "vscode", "title": "Visual Studio Code", "description": "微软开源的轻量级代码编辑器，拥有丰富的插件生态和强大的调试功能", "url": "https://code.visualstudio.com/", "logo": "https://code.visualstudio.com/favicon.ico", "screenshot": "https://code.visualstudio.com/assets/home/<USER>", "infoUrl": "https://code.visualstudio.com/docs/", "rating": 4.8, "tags": ["coding", "editor", "microsoft", "open-source"]}, {"id": "trae", "title": "<PERSON><PERSON>", "description": "专为团队协作设计的AI代码编辑器，支持实时协作和智能代码审查", "url": "https://trae.ai/", "logo": "https://lf-cdn.trae.ai/obj/trae-ai-sg/trae_website_prod/favicon.png", "screenshot": "https://trae.ai/images/editor-preview.png", "infoUrl": "https://trae.ai/docs/", "rating": 4.5, "tags": ["coding", "ai", "collaboration", "team"]}, {"id": "augmentcode", "title": "AugmentCode", "description": "企业级AI代码助手，提供上下文感知的代码生成和重构建议", "url": "https://augmentcode.com/", "logo": "https://augmentcode.com/favicon.ico", "screenshot": "https://augmentcode.com/images/product-demo.png", "infoUrl": "https://augmentcode.com/docs/", "rating": 4.6, "tags": ["coding", "ai", "enterprise", "refactoring"]}, {"id": "kiro", "title": "<PERSON><PERSON>", "description": "轻量级AI代码编辑器，专注于快速原型开发和代码实验", "url": "https://kiro.dev/", "logo": "https://kiro.dev/favicon.ico", "screenshot": "https://kiro.dev/images/interface.png", "infoUrl": "https://kiro.dev/docs/", "rating": 4.3, "tags": ["coding", "ai", "prototype", "lightweight"]}, {"id": "claude-code", "title": "<PERSON>", "description": "Anthropic推出的AI编程助手，擅长代码理解、调试和架构设计", "url": "https://www.anthropic.com/claude-code", "logo": "https://cdn.prod.website-files.com/67ce28cfec624e2b733f8a52/681d52619fec35886a7f1a70_favicon.png", "screenshot": "https://www.anthropic.com/images/claude-code-preview.png", "infoUrl": "https://docs.anthropic.com/claude-code/", "rating": 4.7, "tags": ["coding", "ai", "anthropic", "debugging"]}, {"id": "gemini-cli", "title": "Gemini CLI", "description": "Google Gemini的命令行工具，支持代码生成、分析和自动化脚本编写", "url": "https://github.com/google-gemini/gemini-cli", "logo": "https://www.gstatic.com/lamda/images/gemini_sparkle_aurora_33f86dc0c0257da337c63.svg", "screenshot": "https://github.com/google-gemini/gemini-cli/raw/main/docs/demo.gif", "infoUrl": "https://github.com/google-gemini/gemini-cli/wiki", "rating": 4.4, "tags": ["coding", "ai", "cli", "google", "automation"]}]}, {"id": "database-tools", "title": "🗄️ 数据库工具", "tools": [{"id": "mongodb-compass", "title": "MongoDB Compass", "description": "MongoDB官方GUI工具，提供可视化的数据库管理和查询功能", "url": "https://www.mongodb.com/products/compass", "logo": "https://www.mongodb.com/favicon.ico", "screenshot": "https://webimages.mongodb.com/_com_assets/cms/compass-screenshot-kqfvqd1j6k.png", "infoUrl": "https://docs.mongodb.com/compass/", "rating": 4.7, "tags": ["database", "mongodb", "gui", "nosql"]}, {"id": "dbeaver", "title": "DBeaver", "description": "免费的通用数据库工具，支持多种数据库类型的连接和管理", "url": "https://dbeaver.io/", "logo": "https://dbeaver.io/favicon.ico", "screenshot": "https://dbeaver.io/wp-content/uploads/2019/02/beaver-head.png", "infoUrl": "https://dbeaver.io/docs/", "rating": 4.6, "tags": ["database", "sql", "multi-platform", "free"]}, {"id": "prisma-studio", "title": "Prisma Studio", "description": "现代化的数据库GUI，提供直观的数据浏览和编辑功能", "url": "https://www.prisma.io/studio", "logo": "https://www.prisma.io/favicon.ico", "screenshot": "https://www.prisma.io/images/studio-screenshot.png", "infoUrl": "https://www.prisma.io/docs/concepts/components/prisma-studio", "rating": 4.5, "tags": ["database", "orm", "modern", "gui"]}]}, {"id": "api-testing", "title": "🔧 API测试", "tools": [{"id": "postman", "title": "Postman", "description": "最流行的API开发和测试平台，支持REST、GraphQL等多种API类型", "url": "https://www.postman.com/", "logo": "https://www.postman.com/favicon.ico", "screenshot": "https://www.postman.com/img/v2/logo-postman.svg", "infoUrl": "https://learning.postman.com/docs/", "rating": 4.8, "tags": ["api", "testing", "rest", "graphql"]}, {"id": "insomnia", "title": "Insomnia", "description": "简洁高效的REST和GraphQL客户端，专注于API测试和调试", "url": "https://insomnia.rest/", "logo": "https://insomnia.rest/favicon.ico", "screenshot": "https://insomnia.rest/images/run-in-insomnia.svg", "infoUrl": "https://docs.insomnia.rest/", "rating": 4.6, "tags": ["api", "rest", "graphql", "client"]}, {"id": "hoppscotch", "title": "Hoppscotch", "description": "开源的API开发生态系统，提供轻量级的在线API测试体验", "url": "https://hoppscotch.io/", "logo": "https://hoppscotch.io/favicon.ico", "screenshot": "https://hoppscotch.io/images/logo.svg", "infoUrl": "https://docs.hoppscotch.io/", "rating": 4.4, "tags": ["api", "open-source", "online", "lightweight"]}]}, {"id": "payment-integration", "title": "💳 支付接入", "tools": [{"id": "paypal", "title": "PayPal", "description": "全球领先的在线支付平台，支持多种货币和支付方式", "url": "https://www.paypal.com/", "logo": "https://www.paypal.com/favicon.ico", "screenshot": "https://www.paypal.com/images/shared/paypal-logo-129x32.svg", "infoUrl": "https://developer.paypal.com/docs/", "rating": 4.6, "tags": ["payment", "global", "api"]}, {"id": "stripe", "title": "Stripe", "description": "现代化的在线支付处理平台，为开发者提供强大的API和工具", "url": "https://stripe.com/", "logo": "https://stripe.com/favicon.ico", "screenshot": "https://stripe.com/img/v3/home/<USER>", "infoUrl": "https://stripe.com/docs/", "rating": 4.8, "tags": ["payment", "api", "developer-friendly"]}, {"id": "creem", "title": "Creem", "description": "新兴的支付平台，提供简单易用的支付解决方案", "url": "https://creem.io/", "logo": "https://creem.io/favicon.ico", "screenshot": "https://creem.io/images/preview.png", "infoUrl": "https://docs.creem.io/", "rating": 4.2, "tags": ["payment", "simple", "startup"]}]}]}, {"id": "deploy", "title": "🚀 部署", "description": "注册各部署平台账号，配置环境变量与CI/CD流水线，进行构建并申请收录等。", "categories": [{"id": "deployment-platforms", "title": "🌍 海外部署", "tools": [{"id": "cloudflare", "title": "Cloudflare", "description": "全球领先的CDN和网络安全服务提供商，提供免费的网站加速和保护", "url": "https://www.cloudflare.com/", "logo": "https://www.cloudflare.com/favicon.ico", "screenshot": "", "infoUrl": "https://erlitech.feishu.cn/wiki/", "rating": 4.8, "tags": []}, {"id": "vercel-deploy", "title": "Vercel", "description": "专为前端框架优化的部署平台，支持自动部署和全球CDN加速", "url": "https://vercel.com/", "logo": "https://vercel.com/favicon.ico", "screenshot": "", "infoUrl": "https://erlitech.feishu.cn/wiki/", "rating": 4.9, "tags": []}, {"id": "netlify-deploy", "title": "Netlify", "description": "现代化的静态网站托管平台，提供持续部署和无服务器函数", "url": "https://www.netlify.com/", "logo": "https://www.netlify.com/favicon.ico", "screenshot": "", "infoUrl": "https://erlitech.feishu.cn/wiki/", "rating": 4.7, "tags": []}, {"id": "aws-deploy", "title": "AWS", "description": "亚马逊云服务，提供全面的云计算解决方案和强大的扩展性", "url": "https://aws.amazon.com/", "logo": "https://aws.amazon.com/favicon.ico", "screenshot": "", "infoUrl": "https://erlitech.feishu.cn/wiki/", "rating": 4.6, "tags": []}, {"id": "supabase", "title": "Supabase", "description": "开源的Firebase替代方案，提供数据库、认证、实时订阅和存储服务", "url": "https://supabase.com/", "logo": "https://supabase.com/favicon/favicon-16x16.png", "screenshot": "", "infoUrl": "https://erlitech.feishu.cn/wiki/", "rating": 4.7, "tags": ["数据库"]}]}, {"id": "deployment-platforms-cn", "title": "🇨🇳 国内部署", "tools": [{"id": "<PERSON><PERSON><PERSON>", "title": "阿里云", "description": "阿里巴巴集团的云计算服务平台，提供全面的云计算和大数据服务", "url": "https://www.aliyun.com/", "logo": "https://img.alicdn.com/tfs/TB1_ZXuNcfpK1RjSZFOXXa6nFXa-32-32.ico", "screenshot": "", "infoUrl": "https://erlitech.feishu.cn/wiki/", "rating": 4.5, "tags": ["cn"]}, {"id": "volcengine", "title": "火山引擎", "description": "字节跳动旗下的云服务平台，提供云计算、大数据和AI服务", "url": "https://www.volcengine.com/", "logo": "https://portal.volccdn.com/obj/volcfe/misc/favicon.png", "screenshot": "", "infoUrl": "https://erlitech.feishu.cn/wiki/", "rating": 4.4, "tags": ["cn"]}, {"id": "tencent-cloud", "title": "腾讯云", "description": "腾讯公司的云计算服务平台，提供云服务器、数据库、CDN等服务", "url": "https://cloud.tencent.com/", "logo": "https://cloud.tencent.com/favicon.ico", "screenshot": "", "infoUrl": "https://erlitech.feishu.cn/wiki/", "rating": 4.5, "tags": ["cn"]}, {"id": "huawei-cloud", "title": "华为云", "description": "华为公司的云计算服务平台，提供全栈云服务和行业解决方案", "url": "https://www.huaweicloud.com/", "logo": "https://www.huaweicloud.com/favicon.ico", "screenshot": "", "infoUrl": "https://erlitech.feishu.cn/wiki/", "rating": 4.3, "tags": ["cn"]}]}]}, {"id": "growth", "title": "📈 推广", "description": "制定SEO/内容/社媒/投放计划，发布外链，搭建转化漏斗与A/B实验，快速获取流量。", "categories": [{"id": "se-submit", "title": "🔍 引擎收录", "tools": [{"id": "google-search-console-ops", "title": "Google Search Console", "description": "Google官方SEO工具，监控网站在搜索结果中的表现和索引状态", "url": "https://search.google.com/search-console/", "logo": "https://www.gstatic.com/search-console/scfe/favicon.png", "screenshot": "", "infoUrl": "", "rating": 4.7, "tags": ["seo", "google"]}, {"id": "bing-webmaster-tools", "title": "Bing Webmaster Tools", "description": "微软必应搜索引擎的官方网站管理工具，优化网站在Bing中的表现", "url": "https://www.bing.com/webmasters/", "logo": "https://www.bing.com/favicon.ico", "screenshot": "", "infoUrl": "https://www.bing.com/webmasters/help", "rating": 4.3, "tags": ["seo", "microsoft"]}, {"id": "yandex-webmaster-tools", "title": "Yandex Webmaster Tools", "description": "俄罗斯搜索引擎Yandex的官方网站管理工具，优化网站在Yandex中的表现", "url": "https://webmaster.yandex.com/", "logo": "https://webmaster.yandex.com/favicon.ico", "screenshot": "", "infoUrl": "https://webmaster.yandex.com/help", "rating": 4.2, "tags": ["seo"]}, {"id": "naver-search-advisor", "title": "Naver Search Advisor", "description": "韩国搜索引擎Naver的官方SEO工具，优化网站在Naver中的表现", "url": "https://searchadvisor.naver.com/", "logo": "https://searchadvisor.naver.com/favicon.ico", "screenshot": "", "infoUrl": "https://searchadvisor.naver.com/help", "rating": 4.1, "tags": ["seo", "naver"]}]}, {"id": "analytics", "title": "📈 统计分析", "tools": [{"id": "google-analytics-ops", "title": "Google Analytics", "description": "权威的网站流量统计和用户行为分析工具，数据详尽准确", "url": "https://analytics.google.com/", "logo": "https://www.google.com/favicon.ico", "screenshot": "", "infoUrl": "https://support.google.com/analytics/", "rating": 4.8, "tags": ["google"]}, {"id": "microsoft-clarity", "title": "Microsoft Clarity", "description": "免费的用户行为分析工具，提供热力图和会话录制功能", "url": "https://clarity.microsoft.com/", "logo": "https://clarity.microsoft.com/favicon.ico", "screenshot": "", "infoUrl": "https://docs.microsoft.com/en-us/clarity/", "rating": 4.6, "tags": ["microsoft"]}]}, {"id": "backlinks", "title": "🔗 外链发布", "tools": [{"id": "lxx-ai", "title": "LXX.AI", "description": "AI工具，提供内容创作、优化、分析等功能", "url": "https://www.lxx.ai/", "logo": "", "screenshot": "", "infoUrl": "https://www.lxx.ai/help", "rating": 4.5, "tags": ["seo"]}, {"id": "backlinkdirs", "title": "BacklinkDirs", "description": "外链发布工具，提供外链发布、管理、分析等功能", "url": "https://backlinkdirs.com/", "logo": "https://backlinkdirs.com/favicon.ico", "screenshot": "", "infoUrl": "https://backlinkdirs.com/help", "rating": 4.5, "tags": ["seo"]}]}]}, {"id": "operation", "title": "♻️ 运营", "description": "通过数据分析与用户反馈迭代产品，维护内容与版本，优化留存、活跃与营收指标闭环。", "categories": [{"id": "ad-ops", "title": "📺 广告接入", "tools": [{"id": "google-adsense-ops", "title": "Google AdSense", "description": "Google的广告联盟平台，通过展示相关广告实现网站变现", "url": "https://www.google.com/adsense/", "logo": "https://www.google.com/favicon.ico", "screenshot": "", "infoUrl": "https://support.google.com/adsense/", "rating": 4.5, "tags": ["google"]}, {"id": "amazon-affiliate", "title": "Amazon Affiliate Program", "description": "亚马逊联盟计划，通过推广亚马逊产品获得佣金收入", "url": "https://affiliate-program.amazon.com/", "logo": "https://www.amazon.com/favicon.ico", "screenshot": "", "infoUrl": "https://affiliate-program.amazon.com/help", "rating": 4.4, "tags": []}, {"id": "facebook-ads", "title": "Facebook Ads", "description": "Facebook的广告平台，通过展示广告实现网站变现", "url": "https://www.facebook.com/ads/", "logo": "https://www.facebook.com/favicon.ico", "screenshot": "", "infoUrl": "https://www.facebook.com/ads/help/", "rating": 4.3, "tags": []}, {"id": "twitter-ads", "title": "Twitter Ads", "description": "Twitter的广告平台，通过展示广告实现网站变现", "url": "https://ads.twitter.com/", "logo": "https://www.twitter.com/favicon.ico", "screenshot": "", "infoUrl": "https://ads.twitter.com/help", "rating": 4.2, "tags": []}]}, {"id": "ad-delivery", "title": "🎯 广告投放", "tools": [{"id": "google-ads", "title": "Google Ads", "description": "Google的广告平台，通过展示广告实现网站变现", "url": "https://ads.google.com/", "logo": "https://www.google.com/favicon.ico", "screenshot": "", "infoUrl": "https://support.google.com/adsense/", "rating": 4.5, "tags": ["google"]}]}, {"id": "pay", "title": "💰 收款统计", "tools": [{"id": "stripe-dashboard", "title": "Stripe Dashboard", "description": "Stripe官方仪表板，提供详细的支付数据分析和收入统计", "url": "https://dashboard.stripe.com/", "logo": "https://stripe.com/favicon.ico", "screenshot": "https://stripe.com/img/v3/home/<USER>", "infoUrl": "https://stripe.com/docs/dashboard", "rating": 4.8, "tags": ["payment", "analytics", "dashboard"]}, {"id": "paypal-reports", "title": "PayPal Reports", "description": "PayPal商家报告中心，提供交易统计和财务分析功能", "url": "https://www.paypal.com/reports/", "logo": "https://www.paypal.com/favicon.ico", "screenshot": "https://www.paypal.com/images/shared/paypal-logo-129x32.svg", "infoUrl": "https://www.paypal.com/us/smarthelp/article/how-do-i-view-reports-faq1338", "rating": 4.5, "tags": ["payment", "reports", "paypal"]}]}, {"id": "cms", "title": "📝 内容管理", "tools": [{"id": "notion", "title": "Notion", "description": "全能的工作空间，集成笔记、数据库、项目管理和协作功能", "url": "https://www.notion.so/", "logo": "https://www.notion.so/favicon.ico", "screenshot": "https://www.notion.so/images/meta/default.png", "infoUrl": "https://www.notion.so/help", "rating": 4.7, "tags": ["cms", "productivity", "collaboration"]}, {"id": "contentful", "title": "Contentful", "description": "无头CMS平台，为开发者和内容创作者提供灵活的内容管理解决方案", "url": "https://www.contentful.com/", "logo": "https://www.contentful.com/favicon.ico", "screenshot": "https://images.ctfassets.net/fo9twyrwpveg/44baP970q8HKT6OjBqUOUW/c5078208e40f619dcbf9e3a7c4d70b7b/homepage-hero-2021-v3.png", "infoUrl": "https://www.contentful.com/developers/docs/", "rating": 4.6, "tags": ["cms", "headless", "api"]}, {"id": "strapi", "title": "<PERSON><PERSON><PERSON>", "description": "开源的无头CMS，提供灵活的API和直观的管理界面", "url": "https://strapi.io/", "logo": "https://strapi.io/favicon.ico", "screenshot": "https://strapi.io/assets/strapi-logo-dark.svg", "infoUrl": "https://docs.strapi.io/", "rating": 4.5, "tags": ["cms", "open-source", "headless", "api"]}]}]}]}