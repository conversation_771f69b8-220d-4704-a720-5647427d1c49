generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model account {
  id                       String  @id @default(cuid())
  userId                   String
  type                     String
  provider                 String
  providerAccountId        String
  refresh_token            String? @db.Text
  access_token             String? @db.Text
  expires_at               Int?
  token_type               String?
  scope                    String?
  id_token                 String? @db.Text
  session_state            String?
  refresh_token_expires_in Int?
  user                     user    @relation(fields: [userId], references: [id], onDelete: Cascade, map: "Account_userId_fkey")

  @@unique([provider, providerAccountId], map: "Account_provider_providerAccountId_key")
  @@index([userId], map: "Account_userId_fkey")
}

model passwordResetToken {
  id        String   @id @default(cuid())
  email     String   @db.VarChar(255)
  token     String   @unique(map: "PasswordResetToken_token_key") @db.VarChar(255)
  expires   DateTime
  used      <PERSON><PERSON>an  @default(false)
  createdAt DateTime @default(now())

  @@index([email], map: "PasswordResetToken_email_idx")
  @@index([expires], map: "PasswordResetToken_expires_idx")
  @@index([token], map: "PasswordResetToken_token_idx")
  @@map("password_reset_token")
}

model post {
  id          Int      @id @default(autoincrement())
  name        String   @db.VarChar(255)
  createdAt   DateTime @default(now())
  updatedAt   DateTime
  createdById String
  user        user     @relation(fields: [createdById], references: [id], map: "Post_createdById_fkey")

  @@index([createdById], map: "Post_createdById_fkey")
  @@index([name], map: "Post_name_idx")
}

model project {
  id                             String                 @id @default(cuid())
  parentId                       String?                @db.VarChar(255)
  name                           String                 @db.VarChar(100)
  code                           String                 @unique @default("") @db.VarChar(50)
  description                    String?                @db.Text
  type                           String                 @default("other") @db.VarChar(20)
  status                         String                 @default("ideation") @db.VarChar(20)
  category                       String?                @db.VarChar(50)
  tags                           String?                @db.Text
  priority                       String                 @default("medium") @db.VarChar(10)
  startDate                      DateTime?
  endDate                        DateTime?
  deadline                       DateTime?
  budget                         Float?
  investment                     Float?
  revenue                        Float?
  currentMonthRevenue            Float                  @default(0)
  totalRevenue                   Float                  @default(0)
  logo                           String?                @db.VarChar(500)
  visitUrl                       String?                @db.VarChar(500)
  wikiUrl                        String?                @db.VarChar(500)
  gitUrl                         String?                @db.VarChar(500)
  healthStatus                   String                 @default("healthy") @db.VarChar(20)
  isDeleted                      Boolean                @default(false)
  version                        String                 @default("1.0.0") @db.VarChar(50)
  techStack                      String?                @db.VarChar(500)
  lastDeployAt                   DateTime?
  createdBy                      String                 @db.VarChar(50)
  updatedBy                      String                 @db.VarChar(50)
  createdById                    String
  updatedById                    String
  poUserId                       String?                @db.VarChar(255)
  createdAt                      DateTime               @default(now())
  updatedAt                      DateTime
  user_project_createdByIdTouser user                   @relation("project_createdByIdTouser", fields: [createdById], references: [id], map: "Project_createdById_fkey")
  parent                         project?               @relation("ProjectHierarchy", fields: [parentId], references: [id], map: "Project_parentId_fkey")
  children                       project[]              @relation("ProjectHierarchy")
  user_project_poUserIdTouser    user?                  @relation("project_poUserIdTouser", fields: [poUserId], references: [id], map: "Project_poUserId_fkey")
  user_project_updatedByIdTouser user                   @relation("project_updatedByIdTouser", fields: [updatedById], references: [id], map: "Project_updatedById_fkey")
  projectMember                  projectMember[]
  projectMilestone               projectMilestone[]
  projectShare                   projectShare[]
  projectRevenues                projectRevenue[]
  financialTransactions          financialTransaction[]
  projectBudgets                 projectBudget[]
  financialReports               financialReport[]
  costAllocationDetails          costAllocationDetail[]

  // 项目任务关系
  tasks                          projectTask[]

  @@index([createdAt], map: "Project_createdAt_idx")
  @@index([createdById], map: "Project_createdById_idx")
  @@index([updatedById], map: "Project_updatedById_idx")
  @@index([status], map: "Project_status_idx")
  @@index([code], map: "Project_code_idx")
  @@index([parentId], map: "Project_parentId_idx")
  @@index([poUserId], map: "Project_poUserId_idx")
  @@index([type], map: "Project_type_idx")
  @@index([healthStatus], map: "Project_healthStatus_idx")
  @@index([isDeleted], map: "Project_isDeleted_idx")
  @@index([lastDeployAt], map: "Project_lastDeployAt_idx")
}

model projectMember {
  id                                   String    @id @default(cuid())
  projectId                            String
  userId                               String
  role                                 String    @default("other") @db.VarChar(20)
  permissions                          String?   @db.Text
  profitRate                           Float?
  status                               String    @default("active") @db.VarChar(20)
  joinedAt                             DateTime  @default(now())
  leftAt                               DateTime?
  createdBy                            String    @db.VarChar(50)
  updatedBy                            String    @db.VarChar(50)
  createdById                          String
  updatedById                          String
  createdAt                            DateTime  @default(now())
  updatedAt                            DateTime
  user_projectMember_createdByIdTouser user      @relation("projectMember_createdByIdTouser", fields: [createdById], references: [id], map: "ProjectMember_createdById_fkey")
  project                              project   @relation(fields: [projectId], references: [id], onDelete: Cascade, map: "ProjectMember_projectId_fkey")
  user_projectMember_updatedByIdTouser user      @relation("projectMember_updatedByIdTouser", fields: [updatedById], references: [id], map: "ProjectMember_updatedById_fkey")
  user                                 user      @relation(fields: [userId], references: [id], onDelete: Cascade, map: "ProjectMember_userId_fkey")

  @@unique([projectId, userId], map: "ProjectMember_projectId_userId_key")
  @@index([projectId], map: "ProjectMember_projectId_idx")
  @@index([role], map: "ProjectMember_role_idx")
  @@index([status], map: "ProjectMember_status_idx")
  @@index([userId], map: "ProjectMember_userId_idx")
  @@index([createdById], map: "ProjectMember_createdById_idx")
  @@index([updatedById], map: "ProjectMember_updatedById_idx")
  @@map("project_member")
}

model projectShare {
  id                                  String        @id @default(cuid())
  projectId                           String
  userId                              String
  shareType                           String        @db.VarChar(20)
  percentage                          Float
  amount                              Float?
  conditions                          String?       @db.Text
  rules                               String?       @db.Text
  status                              String        @default("active") @db.VarChar(20)
  period                              String        @default("monthly") @db.VarChar(20)
  startDate                           DateTime
  endDate                             DateTime?
  createdBy                           String        @db.VarChar(50)
  updatedBy                           String        @db.VarChar(50)
  createdById                         String
  updatedById                         String
  createdAt                           DateTime      @default(now())
  updatedAt                           DateTime
  user_projectShare_createdByIdTouser user          @relation("projectShare_createdByIdTouser", fields: [createdById], references: [id], map: "ProjectShare_createdById_fkey")
  project                             project       @relation(fields: [projectId], references: [id], onDelete: Cascade, map: "ProjectShare_projectId_fkey")
  user_projectShare_updatedByIdTouser user          @relation("projectShare_updatedByIdTouser", fields: [updatedById], references: [id], map: "ProjectShare_updatedById_fkey")
  user                                user          @relation(fields: [userId], references: [id], onDelete: Cascade, map: "ProjectShare_userId_fkey")
  sharePayout                         sharePayout[]

  @@index([projectId], map: "ProjectShare_projectId_idx")
  @@index([shareType], map: "ProjectShare_shareType_idx")
  @@index([status], map: "ProjectShare_status_idx")
  @@index([userId], map: "ProjectShare_userId_idx")
  @@index([createdById], map: "ProjectShare_createdById_idx")
  @@index([updatedById], map: "ProjectShare_updatedById_idx")
  @@map("project_share")
}

model session {
  id           String   @id @default(cuid())
  sessionToken String   @unique(map: "Session_sessionToken_key")
  userId       String
  expires      DateTime
  user         user     @relation(fields: [userId], references: [id], onDelete: Cascade, map: "Session_userId_fkey")

  @@index([userId], map: "Session_userId_fkey")
}

model sharePayout {
  id                                 String       @id @default(cuid())
  shareId                            String
  amount                             Float
  currency                           String       @default("CNY") @db.VarChar(10)
  periodStart                        DateTime
  periodEnd                          DateTime
  status                             String       @default("pending") @db.VarChar(20)
  paymentMethod                      String?      @db.VarChar(50)
  paymentInfo                        String?      @db.Text
  paidAt                             DateTime?
  notes                              String?      @db.Text
  createdBy                          String       @db.VarChar(50)
  updatedBy                          String       @db.VarChar(50)
  createdById                        String
  updatedById                        String
  createdAt                          DateTime     @default(now())
  updatedAt                          DateTime
  user_sharePayout_createdByIdTouser user         @relation("sharePayout_createdByIdTouser", fields: [createdById], references: [id], map: "SharePayout_createdById_fkey")
  projectShare                       projectShare @relation(fields: [shareId], references: [id], onDelete: Cascade, map: "SharePayout_shareId_fkey")
  user_sharePayout_updatedByIdTouser user         @relation("sharePayout_updatedByIdTouser", fields: [updatedById], references: [id], map: "SharePayout_updatedById_fkey")
  payouts                            payout[]

  @@index([periodStart], map: "SharePayout_periodStart_idx")
  @@index([shareId], map: "SharePayout_shareId_idx")
  @@index([status], map: "SharePayout_status_idx")
  @@index([createdById], map: "SharePayout_createdById_idx")
  @@index([updatedById], map: "SharePayout_updatedById_idx")
  @@map("share_payout")
}

model user {
  id                                                   String             @id @default(cuid())
  name                                                 String             @db.VarChar(50)
  realname                                             String             @db.VarChar(50)
  email                                                String             @unique(map: "User_email_key") @db.VarChar(255)
  phone                                                String             @unique(map: "User_phone_key") @db.VarChar(20)
  password                                             String             @db.VarChar(255)
  emailVerified                                        DateTime?
  image                                                String?            @db.VarChar(500)
  avatar                                               String?            @db.VarChar(500)
  birthday                                             DateTime?
  gender                                               String?            @db.VarChar(10)
  country                                              String?            @db.VarChar(50)
  city                                                 String?            @db.VarChar(50)
  bio                                                  String?            @db.VarChar(500)
  occupation                                           String?            @db.VarChar(100)
  education                                            String?            @db.VarChar(500)
  github                                               String?            @db.VarChar(255)
  linkedin                                             String?            @db.VarChar(255)
  twitter                                              String?            @db.VarChar(255)
  website                                              String?            @db.VarChar(255)
  skills                                               String?            @db.VarChar(500)
  interests                                            String?            @db.VarChar(500)
  level                                                String             @default("bronze") @db.VarChar(20)
  subscriptionStatus                                   String             @default("trial") @db.VarChar(20)
  subscriptionExpiry                                   DateTime?
  inviteCode                                           String?            @db.VarChar(20)
  invitedBy                                            String?
  role                                                 String             @default("member") @db.VarChar(20)
  status                                               String             @default("pending") @db.VarChar(20)
  tags                                                 String?            @db.Text
  refreshToken                                         String?            @db.Text
  lastLoginAt                                          DateTime?
  loginAttempts                                        Int                @default(0)
  lockUntil                                            DateTime?
  joinDate                                             DateTime           @default(now())
  createdAt                                            DateTime           @default(now())
  updatedAt                                            DateTime
  account                                              account[]
  inviteCodesCreated                                   inviteCode[]       @relation("InviteCodeCreator")
  inviteCodeUsages                                     inviteCodeUsage[]
  post                                                 post[]
  project_project_createdByIdTouser                    project[]          @relation("project_createdByIdTouser")
  project_project_poUserIdTouser                       project[]          @relation("project_poUserIdTouser")
  project_project_updatedByIdTouser                    project[]          @relation("project_updatedByIdTouser")
  projectMember_projectMember_createdByIdTouser        projectMember[]    @relation("projectMember_createdByIdTouser")
  projectMember_projectMember_updatedByIdTouser        projectMember[]    @relation("projectMember_updatedByIdTouser")
  projectMember                                        projectMember[]
  projectMilestone_projectMilestone_assignedToIdTouser projectMilestone[] @relation("projectMilestone_assignedToIdTouser")
  projectMilestone_projectMilestone_createdByIdTouser  projectMilestone[] @relation("projectMilestone_createdByIdTouser")
  projectMilestone_projectMilestone_updatedByIdTouser  projectMilestone[] @relation("projectMilestone_updatedByIdTouser")
  projectShare_projectShare_createdByIdTouser          projectShare[]     @relation("projectShare_createdByIdTouser")
  projectShare_projectShare_updatedByIdTouser          projectShare[]     @relation("projectShare_updatedByIdTouser")
  projectShare                                         projectShare[]
  session                                              session[]
  sharePayout_sharePayout_createdByIdTouser            sharePayout[]      @relation("sharePayout_createdByIdTouser")
  sharePayout_sharePayout_updatedByIdTouser            sharePayout[]      @relation("sharePayout_updatedByIdTouser")
  inviter                                              user?              @relation("UserInvites", fields: [invitedBy], references: [id])
  invitees                                             user[]             @relation("UserInvites")

  // 支付和财务相关关系
  paymentAccounts                paymentAccount[]
  projectRevenues                projectRevenue[]
  financialTransactions_creator  financialTransaction[] @relation("financialTransaction_createdBy")
  financialTransactions_approver financialTransaction[] @relation("financialTransaction_approvedBy")
  projectBudgets                 projectBudget[]
  costAllocations                costAllocation[]

  // 任务相关关系
  assignedTasks                  projectTask[]          @relation("task_assignee")
  reportedTasks                  projectTask[]          @relation("task_reporter")
  taskComments                   taskComment[]
  taskTimeEntries                taskTimeEntry[]
  taskAttachments                taskAttachment[]

  @@index([createdAt], map: "User_createdAt_idx")
  @@index([role], map: "User_role_idx")
  @@index([status], map: "User_status_idx")
  @@index([invitedBy], map: "User_invitedBy_idx")
}

model verificationToken {
  identifier String
  token      String   @unique(map: "VerificationToken_token_key")
  expires    DateTime

  @@unique([identifier, token], map: "VerificationToken_identifier_token_key")
  @@map("verification_token")
}

model projectMilestone {
  id                                       String    @id @default(cuid())
  projectId                                String
  name                                     String    @db.VarChar(100)
  description                              String?   @db.Text
  status                                   String    @default("pending") @db.VarChar(20)
  priority                                 String    @default("medium") @db.VarChar(10)
  dueDate                                  DateTime?
  completedAt                              DateTime?
  createdBy                                String    @db.VarChar(50)
  updatedBy                                String    @db.VarChar(50)
  createdById                              String
  updatedById                              String
  assignedToId                             String?
  createdAt                                DateTime  @default(now())
  updatedAt                                DateTime
  user_projectMilestone_assignedToIdTouser user?     @relation("projectMilestone_assignedToIdTouser", fields: [assignedToId], references: [id], map: "ProjectMilestone_assignedToId_fkey")
  user_projectMilestone_createdByIdTouser  user      @relation("projectMilestone_createdByIdTouser", fields: [createdById], references: [id], map: "ProjectMilestone_createdById_fkey")
  project                                  project   @relation(fields: [projectId], references: [id], onDelete: Cascade, map: "ProjectMilestone_projectId_fkey")
  user_projectMilestone_updatedByIdTouser  user      @relation("projectMilestone_updatedByIdTouser", fields: [updatedById], references: [id], map: "ProjectMilestone_updatedById_fkey")

  // 里程碑任务关系
  tasks                                    projectTask[]

  @@index([assignedToId], map: "ProjectMilestone_assignedToId_idx")
  @@index([createdById], map: "ProjectMilestone_createdById_idx")
  @@index([updatedById], map: "ProjectMilestone_updatedById_idx")
  @@index([dueDate], map: "ProjectMilestone_dueDate_idx")
  @@index([priority], map: "ProjectMilestone_priority_idx")
  @@index([projectId], map: "ProjectMilestone_projectId_idx")
  @@index([status], map: "ProjectMilestone_status_idx")
  @@map("project_milestone")
}

model inviteCode {
  id          String            @id @default(cuid())
  code        String            @unique @db.VarChar(20)
  createdBy   String
  maxUses     Int               @default(1)
  usedCount   Int               @default(0)
  expiresAt   DateTime?
  isActive    Boolean           @default(true)
  description String?           @db.VarChar(200)
  createdAt   DateTime          @default(now())
  updatedAt   DateTime
  creator     user              @relation("InviteCodeCreator", fields: [createdBy], references: [id], onDelete: Cascade)
  usages      inviteCodeUsage[]

  @@index([code], map: "InviteCode_code_idx")
  @@index([createdBy], map: "InviteCode_createdBy_idx")
  @@index([expiresAt], map: "InviteCode_expiresAt_idx")
  @@map("invite_code")
}

model inviteCodeUsage {
  id           String     @id @default(cuid())
  inviteCodeId String
  userId       String
  usedAt       DateTime   @default(now())
  ipAddress    String?    @db.VarChar(45)
  userAgent    String?    @db.Text
  inviteCode   inviteCode @relation(fields: [inviteCodeId], references: [id], onDelete: Cascade)
  user         user       @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([inviteCodeId, userId], map: "InviteCodeUsage_inviteCodeId_userId_key")
  @@index([inviteCodeId], map: "InviteCodeUsage_inviteCodeId_idx")
  @@index([userId], map: "InviteCodeUsage_userId_idx")
  @@map("invite_code_usage")
}

// 支付和财务追踪模型
model paymentAccount {
  id           String   @id @default(cuid())
  userId       String
  provider     String   @db.VarChar(20) // stripe, paypal, alipay, wechat, bank
  accountId    String?  @db.VarChar(100) // 外部账户ID
  accountEmail String?  @db.VarChar(255)
  accountName  String?  @db.VarChar(100)
  isDefault    Boolean  @default(false)
  isVerified   Boolean  @default(false)
  metadata     String?  @db.Text // JSON格式的额外信息
  createdAt    DateTime @default(now())
  updatedAt    DateTime

  user    user     @relation(fields: [userId], references: [id], onDelete: Cascade)
  payouts payout[]

  @@index([userId], map: "PaymentAccount_userId_idx")
  @@index([provider], map: "PaymentAccount_provider_idx")
  @@map("payment_account")
}

model projectRevenue {
  id          String   @id @default(cuid())
  projectId   String
  amount      Float
  currency    String   @default("CNY") @db.VarChar(10)
  source      String   @db.VarChar(50) // manual, api, stripe, etc.
  sourceId    String?  @db.VarChar(100) // 外部收入ID
  description String?  @db.Text
  metadata    String?  @db.Text // JSON格式的额外信息
  period      String   @default("monthly") @db.VarChar(20)
  recordedAt  DateTime @default(now())
  createdBy   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime

  project project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  user    user    @relation(fields: [createdBy], references: [id])

  @@index([projectId], map: "ProjectRevenue_projectId_idx")
  @@index([recordedAt], map: "ProjectRevenue_recordedAt_idx")
  @@index([source], map: "ProjectRevenue_source_idx")
  @@map("project_revenue")
}

model payout {
  id               String    @id @default(cuid())
  sharePayoutId    String
  paymentAccountId String
  amount           Float
  currency         String    @default("CNY") @db.VarChar(10)
  status           String    @default("pending") @db.VarChar(20) // pending, processing, completed, failed, cancelled
  stripePaymentId  String?   @db.VarChar(100)
  failureReason    String?   @db.Text
  processedAt      DateTime?
  createdAt        DateTime  @default(now())
  updatedAt        DateTime

  sharePayout    sharePayout    @relation(fields: [sharePayoutId], references: [id], onDelete: Cascade)
  paymentAccount paymentAccount @relation(fields: [paymentAccountId], references: [id])

  @@index([sharePayoutId], map: "Payout_sharePayoutId_idx")
  @@index([status], map: "Payout_status_idx")
  @@index([processedAt], map: "Payout_processedAt_idx")
}

model platformTransaction {
  id          String   @id @default(cuid())
  type        String   @db.VarChar(20) // commission, fee, refund
  amount      Float
  currency    String   @default("CNY") @db.VarChar(10)
  description String?  @db.Text
  relatedId   String?  @db.VarChar(50) // 关联的项目或用户ID
  relatedType String?  @db.VarChar(20) // project, user, payout
  metadata    String?  @db.Text
  recordedAt  DateTime @default(now())
  createdAt   DateTime @default(now())

  @@index([type], map: "PlatformTransaction_type_idx")
  @@index([recordedAt], map: "PlatformTransaction_recordedAt_idx")
  @@index([relatedId], map: "PlatformTransaction_relatedId_idx")
  @@map("platform_transaction")
}

// 财务科目表 - 标准会计科目
model financialAccount {
  id          String   @id @default(cuid())
  code        String   @unique @db.VarChar(20) // 科目代码，如 1001, 5001
  name        String   @db.VarChar(100) // 科目名称
  type        String   @db.VarChar(20) // 资产、负债、权益、收入、费用
  category    String   @db.VarChar(50) // 具体分类
  parentId    String?  @db.VarChar(50) // 父科目ID，支持多级科目
  level       Int      @default(1) // 科目级别
  isActive    Boolean  @default(true) // 是否启用
  description String?  @db.Text // 科目说明
  createdAt   DateTime @default(now())
  updatedAt   DateTime

  // 关系
  parent       financialAccount?      @relation("AccountHierarchy", fields: [parentId], references: [id])
  children     financialAccount[]     @relation("AccountHierarchy")
  transactions financialTransaction[]
  budgets      projectBudget[]

  @@index([code], map: "FinancialAccount_code_idx")
  @@index([type], map: "FinancialAccount_type_idx")
  @@index([parentId], map: "FinancialAccount_parentId_idx")
  @@map("financial_account")
}

// 项目财务交易记录表 - 核心的收支记录
model financialTransaction {
  id              String    @id @default(cuid())
  projectId       String
  accountId       String // 关联财务科目
  type            String    @db.VarChar(20) // income: 收入, expense: 支出
  amount          Float // 金额
  currency        String    @default("CNY") @db.VarChar(10)
  description     String    @db.Text // 交易描述
  category        String    @db.VarChar(50) // 交易分类
  subCategory     String?   @db.VarChar(50) // 交易子分类
  transactionDate DateTime // 交易发生日期
  invoiceNumber   String?   @db.VarChar(100) // 发票号码
  receiptUrl      String?   @db.VarChar(500) // 收据/发票图片URL
  paymentMethod   String?   @db.VarChar(50) // 支付方式
  vendor          String?   @db.VarChar(200) // 供应商/客户名称
  tags            String?   @db.Text // 标签，JSON格式
  isRecurring     Boolean   @default(false) // 是否为周期性交易
  recurringRule   String?   @db.Text // 周期规则，JSON格式
  status          String    @default("confirmed") @db.VarChar(20) // pending, confirmed, cancelled
  approvedBy      String? // 审批人ID
  approvedAt      DateTime? // 审批时间
  createdBy       String
  createdAt       DateTime  @default(now())
  updatedAt       DateTime

  // 关系
  project  project          @relation(fields: [projectId], references: [id], onDelete: Cascade)
  account  financialAccount @relation(fields: [accountId], references: [id])
  creator  user             @relation("financialTransaction_createdBy", fields: [createdBy], references: [id])
  approver user?            @relation("financialTransaction_approvedBy", fields: [approvedBy], references: [id])

  @@index([projectId], map: "FinancialTransaction_projectId_idx")
  @@index([type], map: "FinancialTransaction_type_idx")
  @@index([transactionDate], map: "FinancialTransaction_transactionDate_idx")
  @@index([status], map: "FinancialTransaction_status_idx")
  @@index([category], map: "FinancialTransaction_category_idx")
  @@index([createdBy], map: "FinancialTransaction_createdBy_idx")
  @@map("financial_transaction")
}

// 项目预算表
model projectBudget {
  id          String   @id @default(cuid())
  projectId   String
  year        Int // 预算年度
  quarter     Int? // 预算季度 (可选)
  month       Int? // 预算月份 (可选)
  accountId   String // 关联财务科目
  budgetType  String   @db.VarChar(20) // revenue: 收入预算, expense: 支出预算
  amount      Float // 预算金额
  currency    String   @default("CNY") @db.VarChar(10)
  description String?  @db.Text // 预算说明
  createdBy   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime

  // 关系
  project project          @relation(fields: [projectId], references: [id], onDelete: Cascade)
  account financialAccount @relation(fields: [accountId], references: [id])
  creator user             @relation(fields: [createdBy], references: [id])

  @@unique([projectId, year, quarter, month, accountId], map: "ProjectBudget_unique")
  @@index([projectId], map: "ProjectBudget_projectId_idx")
  @@index([year], map: "ProjectBudget_year_idx")
  @@map("project_budget")
}

// 财务报表快照 - 缓存计算结果提高性能
model financialReport {
  id         String   @id @default(cuid())
  projectId  String
  reportType String   @db.VarChar(50) // profit_loss: 损益表, cash_flow: 现金流量表
  year       Int
  quarter    Int?
  month      Int?
  data       String   @db.LongText // 报表数据，JSON格式
  createdAt  DateTime @default(now())

  // 关系
  project project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@unique([projectId, reportType, year, quarter, month], map: "FinancialReport_unique")
  @@index([projectId], map: "FinancialReport_projectId_idx")
  @@map("financial_report")
}

// 成本分摊规则表 - 处理共享成本的分摊
model costAllocation {
  id             String   @id @default(cuid())
  name           String   @db.VarChar(100) // 分摊规则名称
  description    String?  @db.Text // 规则描述
  allocationType String   @db.VarChar(20) // percentage: 按比例, fixed: 固定金额, activity: 按活动量
  rules          String   @db.LongText // 分摊规则，JSON格式
  isActive       Boolean  @default(true)
  createdBy      String
  createdAt      DateTime @default(now())
  updatedAt      DateTime

  // 关系
  creator     user                   @relation(fields: [createdBy], references: [id])
  allocations costAllocationDetail[]

  @@map("cost_allocation")
}

// 成本分摊明细表
model costAllocationDetail {
  id            String    @id @default(cuid())
  allocationId  String
  projectId     String
  percentage    Float? // 分摊比例 (0-100)
  fixedAmount   Float? // 固定分摊金额
  activityBase  String?   @db.VarChar(50) // 活动量基础 (hours, users, revenue等)
  activityValue Float? // 活动量数值
  effectiveFrom DateTime // 生效开始日期
  effectiveTo   DateTime? // 生效结束日期
  createdAt     DateTime  @default(now())

  // 关系
  allocation costAllocation @relation(fields: [allocationId], references: [id], onDelete: Cascade)
  project    project        @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@index([allocationId], map: "CostAllocationDetail_allocationId_idx")
  @@index([projectId], map: "CostAllocationDetail_projectId_idx")
  @@map("cost_allocation_detail")
}

// 项目任务表
model projectTask {
  id              String    @id @default(cuid())
  projectId       String
  milestoneId     String?   // 关联里程碑（可选）
  parentTaskId    String?   // 父任务ID，支持子任务
  title           String    @db.VarChar(200)
  description     String?   @db.Text
  status          String    @default("todo") @db.VarChar(20) // todo, in_progress, review, done, cancelled
  priority        String    @default("medium") @db.VarChar(10) // low, medium, high, urgent
  type            String    @default("task") @db.VarChar(20) // task, bug, feature, research
  assigneeId      String?   // 负责人
  reporterId      String    // 创建人
  estimatedHours  Float?    // 预估工时
  actualHours     Float     @default(0) // 实际工时
  progress        Int       @default(0) // 进度百分比 0-100
  tags            String?   @db.Text // JSON格式标签
  labels          String?   @db.Text // JSON格式标签
  startDate       DateTime?
  dueDate         DateTime?
  completedAt     DateTime?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime

  // 关系
  project       project           @relation(fields: [projectId], references: [id], onDelete: Cascade)
  milestone     projectMilestone? @relation(fields: [milestoneId], references: [id], onDelete: SetNull)
  parentTask    projectTask?      @relation("TaskHierarchy", fields: [parentTaskId], references: [id], onDelete: Cascade)
  subTasks      projectTask[]     @relation("TaskHierarchy")
  assignee      user?             @relation("task_assignee", fields: [assigneeId], references: [id], onDelete: SetNull)
  reporter      user              @relation("task_reporter", fields: [reporterId], references: [id])
  
  comments      taskComment[]
  dependencies  taskDependency[]  @relation("TaskDependencies")
  dependents    taskDependency[]  @relation("DependentTasks")
  timeEntries   taskTimeEntry[]
  attachments   taskAttachment[]

  @@index([projectId], map: "ProjectTask_projectId_idx")
  @@index([status], map: "ProjectTask_status_idx")
  @@index([priority], map: "ProjectTask_priority_idx")
  @@index([assigneeId], map: "ProjectTask_assigneeId_idx")
  @@index([reporterId], map: "ProjectTask_reporterId_idx")
  @@index([milestoneId], map: "ProjectTask_milestoneId_idx")
  @@index([parentTaskId], map: "ProjectTask_parentTaskId_idx")
  @@index([dueDate], map: "ProjectTask_dueDate_idx")
  @@map("project_task")
}

// 任务依赖关系表
model taskDependency {
  id           String @id @default(cuid())
  taskId       String // 依赖者
  dependsOnId  String // 被依赖者
  type         String @default("finish_to_start") @db.VarChar(20) // finish_to_start, start_to_start, etc.
  createdAt    DateTime @default(now())

  task      projectTask @relation("TaskDependencies", fields: [taskId], references: [id], onDelete: Cascade)
  dependsOn projectTask @relation("DependentTasks", fields: [dependsOnId], references: [id], onDelete: Cascade)

  @@unique([taskId, dependsOnId], map: "TaskDependency_unique")
  @@index([taskId], map: "TaskDependency_taskId_idx")
  @@index([dependsOnId], map: "TaskDependency_dependsOnId_idx")
  @@map("task_dependency")
}

// 任务评论表
model taskComment {
  id        String   @id @default(cuid())
  taskId    String
  userId    String
  content   String   @db.Text
  createdAt DateTime @default(now())
  updatedAt DateTime

  task projectTask @relation(fields: [taskId], references: [id], onDelete: Cascade)
  user user        @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([taskId], map: "TaskComment_taskId_idx")
  @@index([userId], map: "TaskComment_userId_idx")
  @@map("task_comment")
}

// 任务工时记录表
model taskTimeEntry {
  id          String   @id @default(cuid())
  taskId      String
  userId      String
  hours       Float
  description String?  @db.Text
  date        DateTime
  createdAt   DateTime @default(now())

  task projectTask @relation(fields: [taskId], references: [id], onDelete: Cascade)
  user user        @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([taskId], map: "TaskTimeEntry_taskId_idx")
  @@index([userId], map: "TaskTimeEntry_userId_idx")
  @@index([date], map: "TaskTimeEntry_date_idx")
  @@map("task_time_entry")
}

// 任务附件表
model taskAttachment {
  id        String   @id @default(cuid())
  taskId    String
  userId    String
  filename  String   @db.VarChar(255)
  fileUrl   String   @db.VarChar(500)
  fileSize  Int?
  mimeType  String?  @db.VarChar(100)
  createdAt DateTime @default(now())

  task projectTask @relation(fields: [taskId], references: [id], onDelete: Cascade)
  user user        @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([taskId], map: "TaskAttachment_taskId_idx")
  @@map("task_attachment")
}
