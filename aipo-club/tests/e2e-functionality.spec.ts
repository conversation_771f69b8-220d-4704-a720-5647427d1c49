import { test, expect } from '@playwright/test';

test.describe('AIPO俱乐部端到端功能测试', () => {
  test('测试AIPO俱乐部项目管理系统的核心功能，包括导航、项目管理、财务管理等', async ({ page }) => {
    // 1. 导航到首页
    await page.goto('http://localhost:3000');
    
    // 验证页面标题
    await expect(page).toHaveTitle('AIPO俱乐部 - 数字游民项目管理平台');
    
    // 2. 验证用户已登录
    await expect(page.getByText('欢迎回来，测试用户！')).toBeVisible();
    await expect(page.getByText('您已成功登录 AIPO俱乐部')).toBeVisible();
    
    // 3. 访问控制台页面
    await page.getByRole('link', { name: '进入控制台' }).click();
    await expect(page).toHaveURL('http://localhost:3000/dashboard');
    
    // 4. 检查控制台统计数据
    await expect(page.getByText('控制台')).toBeVisible();
    await expect(page.getByText('欢迎回来，测试用户！')).toBeVisible();
    
    // 验证统计卡片
    await expect(page.getByText('我的项目')).toBeVisible();
    await expect(page.getByText('团队成员')).toBeVisible();
    await expect(page.getByText('总收益')).toBeVisible();
    await expect(page.getByText('活跃项目')).toBeVisible();
    
    // 验证快速操作
    await expect(page.getByText('快速操作')).toBeVisible();
    await expect(page.getByRole('link', { name: '创建项目' })).toBeVisible();
    await expect(page.getByRole('link', { name: '浏览项目' })).toBeVisible();
    
    // 5. 访问项目管理页面
    await page.getByRole('link', { name: '项目管理' }).click();
    await expect(page).toHaveURL('http://localhost:3000/projects');
    
    // 6. 验证项目列表为空
    await expect(page.getByText('项目管理')).toBeVisible();
    await expect(page.getByText('浏览和管理所有项目')).toBeVisible();
    await expect(page.getByText('暂无项目')).toBeVisible();
    
    // 验证搜索和筛选功能
    await expect(page.getByPlaceholder('搜索项目名称或描述')).toBeVisible();
    await expect(page.getByText('状态')).toBeVisible();
    await expect(page.getByText('只显示我的项目')).toBeVisible();
    
    // 7. 访问财务管理页面
    await page.getByRole('link', { name: '财务管理' }).click();
    await expect(page).toHaveURL('http://localhost:3000/dashboard/finance');
    
    // 8. 验证财务概览显示
    await expect(page.getByText('财务管理')).toBeVisible();
    await expect(page.getByText('管理您的支付账户和收益分配')).toBeVisible();
    
    // 验证财务统计卡片
    await expect(page.getByText('待支付')).toBeVisible();
    await expect(page.getByText('¥0.00')).toBeVisible();
    await expect(page.getByText('已收益')).toBeVisible();
    await expect(page.getByText('支付账户')).toBeVisible();
    
    // 验证标签页
    await expect(page.getByRole('tab', { name: '支付账户' })).toBeVisible();
    await expect(page.getByRole('tab', { name: '待支付' })).toBeVisible();
    await expect(page.getByRole('tab', { name: '支付历史' })).toBeVisible();
    
    // 验证支付账户管理
    await expect(page.getByText('支付账户管理')).toBeVisible();
    await expect(page.getByText('添加和管理您的收款账户')).toBeVisible();
    await expect(page.getByRole('button', { name: '添加账户' })).toBeVisible();
    await expect(page.getByText('暂无支付账户')).toBeVisible();
    
    // 9. 测试页面导航功能
    // 测试侧边栏导航
    await expect(page.getByRole('link', { name: '控制台' })).toBeVisible();
    await expect(page.getByRole('link', { name: '项目管理' })).toBeVisible();
    await expect(page.getByRole('link', { name: '财务管理' })).toBeVisible();
    await expect(page.getByRole('link', { name: '个人资料' })).toBeVisible();
    
    // 测试用户信息显示
    await expect(page.getByText('测试用户')).toBeVisible();
    await expect(page.getByText('<EMAIL>')).toBeVisible();
    
    // 返回控制台验证导航
    await page.getByRole('link', { name: '控制台' }).click();
    await expect(page).toHaveURL('http://localhost:3000/dashboard');
    await expect(page.getByText('控制台')).toBeVisible();
  });

  test('测试项目创建表单验证', async ({ page }) => {
    // 导航到项目创建页面
    await page.goto('http://localhost:3000/projects/create');
    
    // 验证表单字段
    await expect(page.getByText('创建新项目')).toBeVisible();
    await expect(page.getByText('填写项目基本信息，开始您的创业之旅')).toBeVisible();
    
    // 验证必填字段
    await expect(page.getByRole('textbox', { name: '项目名称 *' })).toBeVisible();
    await expect(page.getByRole('textbox', { name: '项目编码 *' })).toBeVisible();
    
    // 验证可选字段
    await expect(page.getByRole('textbox', { name: '项目描述' })).toBeVisible();
    await expect(page.getByLabel('项目类型')).toBeVisible();
    await expect(page.getByRole('textbox', { name: '技术栈' })).toBeVisible();
    
    // 验证项目管理信息部分
    await expect(page.getByText('项目管理信息')).toBeVisible();
    await expect(page.getByText('项目Logo')).toBeVisible();
    await expect(page.getByRole('textbox', { name: '访问地址' })).toBeVisible();
    await expect(page.getByRole('textbox', { name: '知识库地址' })).toBeVisible();
    await expect(page.getByRole('textbox', { name: '代码仓库' })).toBeVisible();
    
    // 验证按钮
    await expect(page.getByRole('link', { name: '取消' })).toBeVisible();
    await expect(page.getByRole('button', { name: '创建项目' })).toBeVisible();
  });

  test('测试响应式设计和用户体验', async ({ page }) => {
    // 测试不同页面的加载
    await page.goto('http://localhost:3000');
    
    // 验证首页响应式元素
    await expect(page.getByText('AIPO俱乐部')).toBeVisible();
    await expect(page.getByText('面向数字游民群体打造的公共成就型平台系统')).toBeVisible();
    
    // 测试功能卡片
    await expect(page.getByText('用户中心')).toBeVisible();
    await expect(page.getByText('项目管理')).toBeVisible();
    await expect(page.getByText('分成管理')).toBeVisible();
    await expect(page.getByText('中台系统')).toBeVisible();
    
    // 验证导航栏
    await expect(page.getByRole('link', { name: '控制台' })).toBeVisible();
    await expect(page.getByRole('link', { name: '项目' })).toBeVisible();
    await expect(page.getByRole('link', { name: '个人资料' })).toBeVisible();
    await expect(page.getByRole('link', { name: '退出登录' })).toBeVisible();
  });
});
