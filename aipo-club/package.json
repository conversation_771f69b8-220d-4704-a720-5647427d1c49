{"name": "aipo-club", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "check": "next lint && tsc --noEmit", "db:generate": "prisma migrate dev", "db:migrate": "prisma migrate deploy", "db:push": "prisma db push", "db:studio": "prisma studio", "dev": "next dev --turbo", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "postinstall": "prisma generate", "lint": "next lint", "lint:fix": "next lint --fix", "preview": "next build && node .next/standalone/server.js", "start": "node .next/standalone/server.js", "start:next": "next start", "typecheck": "tsc --noEmit"}, "dependencies": {"@auth/prisma-adapter": "^2.7.2", "@prisma/client": "^6.9.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-tabs": "^1.1.12", "@stripe/stripe-js": "^7.3.1", "@t3-oss/env-nextjs": "^0.12.0", "@tailwindcss/postcss": "^4.0.15", "@tanstack/react-query": "^5.69.0", "@trpc/client": "^11.0.0", "@trpc/react-query": "^11.0.0", "@trpc/server": "^11.0.0", "@types/nodemailer": "^6.4.17", "ali-oss": "^6.23.0", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "lucide-react": "^0.516.0", "mysql2": "^3.14.1", "next": "^15.2.3", "next-auth": "5.0.0-beta.25", "nodemailer": "^6.10.1", "proxy-agent": "^5.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "server-only": "^0.0.1", "stripe": "^18.2.1", "superjson": "^2.2.1", "tailwind-merge": "^3.3.1", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@types/ali-oss": "^6.16.11", "@types/bcryptjs": "^2.4.6", "@types/node": "^20.14.10", "@types/react": "19.1.8", "@types/react-dom": "^19.0.0", "eslint": "^9.23.0", "eslint-config-next": "^15.2.3", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "prisma": "^6.5.0", "tailwindcss": "^4.0.15", "typescript": "^5.8.2", "typescript-eslint": "^8.27.0"}, "ct3aMetadata": {"initVersion": "7.39.3"}, "packageManager": "npm@10.9.2"}