# AIPO俱乐部项目管理系统设计文档

基于脑图分析和现有实现，完善AIPO俱乐部项目管理相关的系统设计和功能。

## 核心数据表设计

### **项目表 (projects)**
```sql
projects:
- id: 主键 (String, cuid)
- parent_id: 父项目ID (支持项目层级结构, String, 可选)
- name: 项目名称 (String, 100字符)
- code: 项目编码 (唯一标识, String, 50字符)
- description: 项目描述 (Text, 可选)
- type: 项目类型 (枚举值见下方, String, 20字符)
- status: 项目状态 (枚举值见下方, String, 20字符)
- stage: 项目阶段 (枚举值见下方, String, 20字符)
- category: 项目分类 (String, 50字符, 可选)
- tags: 标签 (JSON格式, Text, 可选)
- priority: 优先级 (low/medium/high/urgent, String, 10字符)
- po_user_id: 负责人ID (外键关联用户表, String)
- created_by_id: 创建者ID (外键关联用户表, String)

-- 时间管理
- start_date: 开始时间 (DateTime, 可选)
- end_date: 结束时间 (DateTime, 可选)
- deadline: 截止时间 (DateTime, 可选)

-- 财务信息
- budget: 预算 (Float, 可选)
- investment: 投资金额 (Float, 可选)
- current_month_revenue: 当月收益 (Float, 默认0)
- total_revenue: 累计收益 (Float, 默认0)

-- 时间戳
- created_at: 立项时间 (DateTime)
- updated_at: 更新时间 (DateTime)
```

### **项目成员表 (project_members)**
```sql
project_members:
- id: 主键 (String, cuid)
- project_id: 项目ID (外键, String)
- user_id: 用户ID (外键, String)
- role: 角色 (枚举值见下方, String, 20字符)
- status: 状态 (active/inactive/pending/removed, String, 20字符)
- profit_rate: 分成比例 (百分比 0-100, Float, 可选)
- permissions: 权限列表 (JSON格式, Text, 可选)
- joined_at: 加入时间 (DateTime)
- left_at: 离开时间 (DateTime, 可选)
- created_at: 创建时间 (DateTime)
- updated_at: 更新时间 (DateTime)
```


### **项目分成表 (project_shares)**
```sql
project_shares:
- id: 主键 (String, cuid)
- project_id: 项目ID (外键, String)
- user_id: 用户ID (外键, String)
- share_type: 分成类型 (equity/revenue/bonus/commission, String, 20字符)
- percentage: 分成百分比 (0.00-100.00, Float)
- amount: 固定分成金额 (Float, 可选)
- conditions: 分成条件 (JSON格式, Text, 可选)
- rules: 分成规则 (JSON格式, Text, 可选)
- status: 分成状态 (active/inactive/pending/completed, String, 20字符)
- period: 分成周期 (monthly/quarterly/yearly/milestone, String, 20字符)
- start_date: 分成开始时间 (DateTime)
- end_date: 分成结束时间 (DateTime, 可选)
- created_at: 创建时间 (DateTime)
- updated_at: 更新时间 (DateTime)
```

### **项目里程碑表 (project_milestones)**
```sql
project_milestones:
- id: 主键 (String, cuid)
- project_id: 项目ID (外键, String)
- name: 里程碑名称 (String, 100字符)
- description: 里程碑描述 (Text, 可选)
- status: 状态 (pending/in_progress/completed/cancelled, String, 20字符)
- priority: 优先级 (low/medium/high/urgent, String, 10字符)
- due_date: 截止时间 (DateTime, 可选)
- completed_at: 完成时间 (DateTime, 可选)
- created_by_id: 创建者ID (外键, String)
- assigned_to_id: 负责人ID (外键, String, 可选)
- created_at: 创建时间 (DateTime)
- updated_at: 更新时间 (DateTime)
```

## 枚举值定义

### **项目状态 (project_status)**
- `idea`: 创意点 - 项目处于想法阶段
- `approved`: 已立项 - 项目已获得批准
- `planning`: 规划中 - 项目正在制定详细计划
- `developing`: 开发中 - 项目正在开发实施
- `testing`: 测试中 - 项目正在测试验证
- `launched`: 已上线 - 项目已发布上线
- `growing`: 增长期 - 项目处于用户增长阶段
- `profiting`: 盈利期 - 项目已开始盈利
- `maintaining`: 维护期 - 项目处于稳定维护阶段
- `declining`: 衰退期 - 项目用户或收入下降
- `abandoned`: 已放弃 - 项目已停止开发
- `completed`: 已结束 - 项目已完成目标
- `archived`: 已归档 - 项目已归档存储

### **项目阶段 (project_stage)**
- `concept`: 概念阶段 - 项目概念验证
- `analysis`: 分析阶段 - 需求分析和可行性研究
- `prototype`: 原型阶段 - 制作原型和MVP
- `testing`: 测试阶段 - 功能测试和用户测试
- `launch`: 发布阶段 - 产品发布和推广
- `optimization`: 优化阶段 - 性能优化和功能完善
- `mature`: 成熟阶段 - 产品成熟稳定运营

### **项目类型 (project_type)**
- `website`: 网站 - 传统网站项目
- `app`: 移动应用 - iOS/Android应用
- `miniprogram`: 小程序 - 微信/支付宝小程序
- `browser_extension`: 浏览器插件 - Chrome/Firefox插件
- `wechat_official`: 公众号 - 微信公众号项目
- `saas`: SaaS产品 - 软件即服务产品
- `api`: API服务 - 接口服务项目
- `tool`: 工具软件 - 实用工具软件
- `game`: 游戏 - 游戏项目
- `desktop_app`: 桌面应用 - 桌面应用程序
- `other`: 其他 - 其他类型项目

### **成员角色 (member_role)**
- `po`: 产品负责人 - Product Owner，项目整体负责
- `product`: 产品 - 产品经理/产品设计师
- `frontend`: 前端 - 前端开发工程师
- `backend`: 后端 - 后端开发工程师
- `tester`: 测试 - 测试工程师/QA
- `operation`: 运营 - 运营专员/市场推广
- `fullstack`: 全栈 - 全栈开发工程师
- `visitor`: 访客 - 可浏览项目信息
- `other`: 其他 - 其他角色
- `platform`: 平台 - 平台方代表

### **分成支付记录表 (share_payouts)**
```sql
share_payouts:
- id: 主键 (String, cuid)
- share_id: 分成记录ID (外键, String)
- amount: 支付金额 (Float)
- currency: 货币类型 (CNY/USD等, String, 10字符)
- period_start: 分成周期开始 (DateTime)
- period_end: 分成周期结束 (DateTime)
- status: 支付状态 (pending/processing/completed/failed/cancelled, String, 20字符)
- payment_method: 支付方式 (String, 50字符, 可选)
- payment_info: 支付信息 (JSON格式, Text, 可选)
- paid_at: 实际支付时间 (DateTime, 可选)
- notes: 备注 (Text, 可选)
- created_at: 创建时间 (DateTime)
- updated_at: 更新时间 (DateTime)
```

## 关键设计考虑

### **1. 层级结构支持**
- 通过`parent_id`字段支持项目的父子关系
- 支持多级项目嵌套，便于大型项目的模块化管理
- 子项目可以继承父项目的部分属性和权限

### **2. 财务管理完善**
- `current_month_revenue`和`total_revenue`字段支持收益统计
- 通过`budget`和`investment`字段管理项目成本
- 分成系统支持多种分成类型和灵活的分成规则

### **3. 灵活分成机制**
- `profit_rate`字段支持动态分成比例调整
- 支持股权分成、收益分成、奖金分成、佣金分成等多种类型
- 分成条件和规则采用JSON格式，支持复杂的分成逻辑

### **4. 完整状态流转**
- 项目状态(status)管理项目的生命周期
- 项目阶段(stage)管理项目的开发阶段
- 支持状态和阶段的独立管理，更精确地反映项目进展

### **5. 权限管理体系**
- 多层级的角色权限系统
- 项目成员权限采用JSON格式，支持细粒度权限控制
- 支持角色继承和权限组合

### **6. 里程碑管理**
- 支持项目里程碑的创建和跟踪
- 里程碑与项目进度紧密关联
- 支持里程碑的优先级和截止时间管理

### **7. 数据完整性**
- 外键约束确保数据一致性
- 唯一性约束防止重复数据
- 索引优化提高查询性能

### **8. 扩展性设计**
- JSON字段支持灵活的扩展属性
- 预留字段支持未来功能扩展
- 模块化设计便于功能迭代

## 业务流程设计

### **项目创建流程**
1. 用户提交项目创意(idea状态)
2. 平台审核项目可行性
3. 项目获得批准(approved状态)
4. 进入规划阶段(planning状态)
5. 开始开发实施(developing状态)

### **成员管理流程**
1. 项目负责人邀请成员
2. 成员接受邀请(pending状态)
3. 确认加入项目(active状态)
4. 设置角色和权限
5. 配置分成比例

### **分成计算流程**
1. 项目产生收益
2. 系统按照分成规则计算各成员分成
3. 生成分成支付记录
4. 执行支付流程
5. 更新支付状态

这个设计完全基于脑图中的业务需求，既支持复杂的项目管理功能，又为后续的分成计算和财务统计提供了完整的数据基础。
