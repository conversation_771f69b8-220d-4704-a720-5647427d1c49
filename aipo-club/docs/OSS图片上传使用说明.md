# OSS图片上传功能使用说明

## 📋 功能概述

项目现已支持OSS（对象存储服务）图片上传功能，用于项目Logo和用户头像的管理。支持多种云服务商，包括阿里云OSS、腾讯云COS、AWS S3等。

## 🎯 主要特性

### ✅ 支持的功能
- **多格式支持**: JPG、PNG、GIF、WebP
- **文件大小限制**: 最大5MB
- **自动压缩**: 可选的图片压缩功能
- **缩略图生成**: 自动生成不同尺寸的缩略图
- **拖拽上传**: 支持拖拽文件上传
- **实时预览**: 上传前后的图片预览
- **错误处理**: 完善的错误提示和处理

### 🎨 组件类型
1. **ProjectLogoUpload**: 项目Logo上传（方形预览）
2. **AvatarUpload**: 用户头像上传（圆形预览）
3. **ImageUpload**: 通用图片上传组件

## 🔧 配置说明

### 1. 环境变量配置

在`.env`文件中添加OSS配置：

```bash
# 阿里云OSS配置
OSS_ACCESS_KEY_ID="your-access-key-id"
OSS_ACCESS_KEY_SECRET="your-access-key-secret"
OSS_BUCKET="your-bucket-name"
OSS_REGION="oss-cn-hangzhou"
NEXT_PUBLIC_OSS_ENDPOINT="oss-cn-hangzhou.aliyuncs.com"
NEXT_PUBLIC_OSS_BASE_URL="https://your-bucket.oss-cn-hangzhou.aliyuncs.com"
```

### 2. 云服务商配置

#### 阿里云OSS
```bash
OSS_ACCESS_KEY_ID="LTAI5t..."
OSS_ACCESS_KEY_SECRET="xxx..."
OSS_BUCKET="my-project-bucket"
OSS_REGION="oss-cn-hangzhou"
NEXT_PUBLIC_OSS_ENDPOINT="oss-cn-hangzhou.aliyuncs.com"
NEXT_PUBLIC_OSS_BASE_URL="https://my-project-bucket.oss-cn-hangzhou.aliyuncs.com"
```

#### 腾讯云COS
```bash
COS_SECRET_ID="AKIDxxx..."
COS_SECRET_KEY="xxx..."
COS_BUCKET="my-project-bucket"
COS_REGION="ap-beijing"
NEXT_PUBLIC_COS_BASE_URL="https://my-project-bucket.cos.ap-beijing.myqcloud.com"
```

#### AWS S3
```bash
AWS_ACCESS_KEY_ID="AKIAIOSFODNN7EXAMPLE"
AWS_SECRET_ACCESS_KEY="wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
AWS_BUCKET="my-project-bucket"
AWS_REGION="us-east-1"
NEXT_PUBLIC_S3_BASE_URL="https://my-project-bucket.s3.amazonaws.com"
```

## 💻 使用方法

### 1. 在项目创建页面

项目Logo上传组件已集成到项目创建表单中：

```tsx
<ProjectLogoUpload
  value={formData.logo}
  onChange={(url) => setFormData(prev => ({ ...prev, logo: url }))}
  onError={(error) => setErrors(prev => ({ ...prev, logo: error }))}
  disabled={isLoading}
/>
```

### 2. 在项目编辑页面

同样支持Logo的更新和替换：

```tsx
<ProjectLogoUpload
  value={formData.logo}
  onChange={(url) => {
    setFormData(prev => ({ ...prev, logo: url }));
    if (errors.logo) {
      setErrors(prev => ({ ...prev, logo: "" }));
    }
  }}
  onError={(error) => setErrors(prev => ({ ...prev, logo: error }))}
  disabled={isLoading}
/>
```

### 3. 在项目详情页面

优化的Logo显示，支持缩略图和原图查看：

```tsx
<ProjectManagementFields 
  project={project}
  showLabels={true}
/>
```

## 🎨 界面优化

### 项目Logo展示优化

1. **缩略图显示**: 使用48x48像素的缩略图提升加载速度
2. **圆角设计**: 12px圆角，更现代的视觉效果
3. **边框装饰**: 灰色边框增强视觉层次
4. **文件名显示**: 显示文件名便于识别
5. **链接功能**: 点击可查看原图

### 上传组件特性

1. **拖拽上传**: 支持文件拖拽到上传区域
2. **进度指示**: 上传过程中显示加载动画
3. **预览功能**: 上传前后都有图片预览
4. **操作按钮**: 悬停显示更换和删除按钮
5. **错误提示**: 详细的错误信息和解决建议

## 🔒 安全考虑

### 1. 文件验证
- 文件类型检查（仅允许图片格式）
- 文件大小限制（最大5MB）
- 文件名安全处理

### 2. 权限控制
- 用户身份验证
- 只能删除自己上传的文件
- API访问权限控制

### 3. 存储安全
- 唯一文件名生成（防止冲突）
- 路径前缀分类（project-logos、avatars等）
- CDN加速和缓存控制

## 🚀 部署说明

### 1. 开发环境

在开发环境中，上传API会模拟OSS上传过程，返回模拟的URL。

### 2. 生产环境

需要配置真实的OSS服务：

1. **创建OSS Bucket**
2. **配置CORS策略**
3. **设置访问权限**
4. **配置CDN（可选）**
5. **更新环境变量**

### 3. API实现

当前`/api/upload`路由提供了基础框架，需要根据选择的云服务商实现具体的上传逻辑：

```typescript
// 示例：阿里云OSS实现
import OSS from 'ali-oss';

const client = new OSS({
  region: process.env.OSS_REGION,
  accessKeyId: process.env.OSS_ACCESS_KEY_ID,
  accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET,
  bucket: process.env.OSS_BUCKET,
});

const result = await client.put(key, buffer);
```

## 📊 性能优化

### 1. 图片压缩
- 自动压缩大尺寸图片
- 可配置压缩质量
- 保持合理的文件大小

### 2. 缩略图生成
- 多尺寸缩略图支持
- 按需加载不同尺寸
- CDN缓存优化

### 3. 加载优化
- Next.js Image组件优化
- 懒加载支持
- 错误降级处理

## 🔧 故障排除

### 常见问题

1. **上传失败**
   - 检查网络连接
   - 验证OSS配置
   - 确认文件格式和大小

2. **图片不显示**
   - 检查URL是否正确
   - 验证CORS配置
   - 确认访问权限

3. **缩略图加载失败**
   - 检查OSS图片处理服务
   - 验证参数格式
   - 降级到原图显示

### 调试方法

1. 查看浏览器控制台错误
2. 检查网络请求状态
3. 验证环境变量配置
4. 测试OSS连接性

## 📈 后续扩展

### 计划功能
1. **批量上传**: 支持多文件同时上传
2. **图片编辑**: 在线裁剪和滤镜
3. **水印添加**: 自动添加项目水印
4. **格式转换**: 自动转换为WebP格式
5. **智能压缩**: AI驱动的图片优化

### 集成建议
1. **用户头像**: 扩展到用户资料页面
2. **项目封面**: 支持项目封面图片
3. **文档图片**: 集成到富文本编辑器
4. **团队Logo**: 支持团队/组织Logo管理
