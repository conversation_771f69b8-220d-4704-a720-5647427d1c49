# API变更记录 - 用户管理重构

**变更时间**: 2025-01-22  
**变更版本**: v2.0  
**变更类型**: 功能增强 + 权限优化

---

## 📋 变更概述

本次重构主要针对用户管理相关的API进行了优化和扩展，实现了通用用户功能和管理员功能的分离，同时保持了向后兼容性。

## 🆕 新增API端点

### 1. getPublicUsers
**路径**: `api.user.getPublicUsers`  
**类型**: Query  
**权限**: 所有登录用户  
**功能**: 获取公开用户列表，基于权限返回不同数据

#### 输入参数
```typescript
{
  page: number;           // 页码，默认1
  limit: number;          // 每页数量，默认12，最大50
  search?: string;        // 搜索关键词（昵称、简介）
  skills?: string;        // 技能筛选
  location?: string;      // 地区筛选
}
```

#### 返回数据
```typescript
{
  users: Array<{
    id: string;
    name: string;
    avatar: string | null;
    bio: string | null;
    skills: string[];
    role: string;
    createdAt: Date;
    // 管理员额外可见字段
    email?: string;         // 仅管理员可见
    realname?: string;      // 仅管理员可见
    status?: string;        // 仅管理员可见
    lastLoginAt?: Date;     // 仅管理员可见
  }>;
  total: number;
  pages: number;
  currentPage: number;
}
```

#### 权限控制
- **普通用户**: 只能看到活跃用户的基本信息
- **管理员**: 可以看到所有用户和完整信息

### 2. getPublicUserById
**路径**: `api.user.getPublicUserById`  
**类型**: Query  
**权限**: 所有登录用户  
**功能**: 获取指定用户的公开详情，基于权限控制显示内容

#### 输入参数
```typescript
{
  id: string;             // 用户ID
}
```

#### 返回数据
```typescript
{
  id: string;
  name: string;
  avatar: string | null;
  bio: string | null;
  skills: string[];
  socialLinks: {
    github: string;
    linkedin: string;
    twitter: string;
    website: string;
  };
  role: string;
  status: string;
  createdAt: Date;
  // 条件性包含的敏感信息
  realname?: string;      // 管理员或本人可见
  email?: string;         // 管理员或本人可见
  phone?: string;         // 管理员或本人可见
}
```

#### 权限控制
- **所有用户**: 基本信息（昵称、头像、技能、社交链接）
- **管理员或本人**: 敏感信息（真实姓名、邮箱、手机）
- **非活跃用户**: 仅管理员可查看

## 🔄 修改的API端点

### 1. getUserProjects
**路径**: `api.project.getUserProjects`  
**变更**: 从管理员专用改为公开访问，权限控制显示内容

#### 变更前
```typescript
// 仅管理员可访问
if (!currentUser || (currentUser.role !== 'admin' && currentUser.role !== 'manager')) {
  throw new Error("权限不足：只有管理员或经理可以查看用户项目");
}
```

#### 变更后
```typescript
// 所有用户可访问，权限控制显示内容
const isAdmin = currentUser && ["admin", "manager"].includes(currentUser.role);
const isOwnProfile = currentUser?.id === input.userId;

// 根据权限显示不同项目信息
const where: Prisma.projectWhereInput = {
  AND: [
    { OR: baseConditions },
    // 未来可以添加隐私控制
  ],
};
```

## 📊 API使用对比

### 重构前的API使用

#### 用户列表（仅管理员）
```typescript
// 只有管理员可以调用
const { data } = api.user.getUsers.useQuery({
  page: 1,
  limit: 10,
  status: "active"
});
```

#### 用户详情（仅管理员）
```typescript
// 只有管理员可以调用
const { data } = api.user.getUserById.useQuery({
  id: "user-id"
});
```

### 重构后的API使用

#### 用户列表（所有用户）
```typescript
// 所有用户可以调用，返回数据根据权限不同
const { data } = api.user.getPublicUsers.useQuery({
  page: 1,
  limit: 12,
  search: "前端",
  skills: "React"
});
```

#### 用户详情（所有用户）
```typescript
// 所有用户可以调用，敏感信息权限控制
const { data } = api.user.getPublicUserById.useQuery({
  id: "user-id"
});
```

#### 管理员专用API（保持不变）
```typescript
// 管理员专用功能保持不变
const { data } = api.user.getUsers.useQuery({
  page: 1,
  limit: 10,
  status: "pending",
  role: "member"
});
```

## 🔒 权限控制策略

### 数据访问权限矩阵

| 数据字段 | 普通用户 | 项目成员 | 本人 | 管理员 |
|----------|----------|----------|------|--------|
| 基本信息（昵称、头像） | ✅ | ✅ | ✅ | ✅ |
| 技能标签 | ✅ | ✅ | ✅ | ✅ |
| 个人简介 | ✅ | ✅ | ✅ | ✅ |
| 社交链接 | ✅ | ✅ | ✅ | ✅ |
| 邮箱地址 | ❌ | 🔄* | ✅ | ✅ |
| 真实姓名 | ❌ | 🔄* | ✅ | ✅ |
| 手机号码 | ❌ | ❌ | ✅ | ✅ |
| 用户状态 | ❌ | ❌ | ✅ | ✅ |
| 角色信息 | ❌ | ❌ | ✅ | ✅ |
| 登录记录 | ❌ | ❌ | ✅ | ✅ |

*🔄 表示未来可能实现的功能（同项目成员可见联系方式）

### 权限检查实现

```typescript
// 服务端权限检查
const checkUserPermission = async (requesterId: string, targetUserId: string) => {
  const requester = await db.user.findUnique({
    where: { id: requesterId },
    select: { role: true }
  });
  
  const isAdmin = requester && ["admin", "manager"].includes(requester.role);
  const isOwnProfile = requesterId === targetUserId;
  
  return { isAdmin, isOwnProfile };
};

// 数据过滤
const filterUserData = (user: User, permissions: UserPermissions) => {
  const publicData = {
    id: user.id,
    name: user.name,
    avatar: user.avatar,
    bio: user.bio,
    skills: user.skills,
    socialLinks: user.socialLinks,
  };
  
  if (permissions.isAdmin || permissions.isOwnProfile) {
    return {
      ...publicData,
      email: user.email,
      realname: user.realname,
      phone: user.phone,
      status: user.status,
      role: user.role,
    };
  }
  
  return publicData;
};
```

## 🔄 迁移指南

### 前端代码迁移

#### 用户列表页面
```typescript
// 旧代码（仅管理员可用）
const { data } = api.user.getUsers.useQuery({
  page: 1,
  limit: 10,
  status: "active"
});

// 新代码（所有用户可用）
const { data } = api.user.getPublicUsers.useQuery({
  page: 1,
  limit: 12,
  search: searchTerm,
  skills: skillsFilter
});
```

#### 用户详情页面
```typescript
// 旧代码（仅管理员可用）
const { data } = api.user.getUserById.useQuery({
  id: userId
});

// 新代码（所有用户可用）
const { data } = api.user.getPublicUserById.useQuery({
  id: userId
});
```

### 权限检查迁移

```typescript
// 旧代码（页面级权限检查）
if (!isAdmin) {
  return <AccessDenied />;
}

// 新代码（字段级权限显示）
{isAdmin && <AdminOnlyContent />}
{(isAdmin || isOwnProfile) && <SensitiveInfo />}
```

## 📈 性能优化

### 查询优化
1. **索引优化**: 为搜索字段添加数据库索引
2. **分页优化**: 限制每页最大数量为50
3. **缓存策略**: 公开数据缓存60秒，管理员数据缓存30秒

### 数据传输优化
1. **字段选择**: 根据权限只返回必要字段
2. **数据压缩**: 技能标签等数组数据进行处理
3. **响应格式**: 统一的响应格式，便于前端处理

## 🧪 测试建议

### 权限测试
1. **普通用户**: 验证只能看到公开信息
2. **管理员**: 验证可以看到完整信息
3. **本人**: 验证可以看到自己的敏感信息
4. **非活跃用户**: 验证权限控制正确

### 功能测试
1. **搜索功能**: 验证各种搜索条件
2. **分页功能**: 验证分页逻辑正确
3. **数据一致性**: 验证新旧API返回数据一致
4. **性能测试**: 验证响应时间在可接受范围

---

**总结**: 本次API重构成功实现了用户管理功能的开放化，在保证安全性的前提下，为所有用户提供了丰富的用户浏览和搜索功能，同时保持了管理员的完整管理能力。
