# 数据库连接池优化指南

## 问题描述

在高并发测试过程中，发现数据库连接池出现超时问题：
```
Timed out fetching a new connection from the connection pool
```

## 解决方案

### 1. 优化DATABASE_URL配置

在`.env`文件中，为DATABASE_URL添加连接池参数：

```bash
# 基础配置
DATABASE_URL="mysql://user:password@localhost:3306/database"

# 优化后的配置（推荐）
DATABASE_URL="mysql://user:password@localhost:3306/database?connection_limit=20&pool_timeout=10&socket_timeout=60&connect_timeout=10"
```

### 2. 连接池参数说明

| 参数 | 说明 | 推荐值 | 默认值 |
|------|------|--------|--------|
| `connection_limit` | 最大连接数 | 20-50 | 10 |
| `pool_timeout` | 获取连接超时时间(秒) | 10-30 | 10 |
| `socket_timeout` | Socket超时时间(秒) | 60 | 60 |
| `connect_timeout` | 连接超时时间(秒) | 10 | 5 |

### 3. 根据环境调整配置

#### 开发环境
```bash
DATABASE_URL="mysql://user:password@localhost:3306/database?connection_limit=10&pool_timeout=10"
```

#### 测试环境
```bash
DATABASE_URL="mysql://user:password@localhost:3306/database?connection_limit=20&pool_timeout=15"
```

#### 生产环境
```bash
DATABASE_URL="mysql://user:password@localhost:3306/database?connection_limit=50&pool_timeout=30&socket_timeout=120"
```

## 代码层面优化

### 1. 确保正确关闭连接

在测试脚本中，始终在finally块中关闭Prisma连接：

```javascript
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testFunction() {
  try {
    // 测试逻辑
    await prisma.user.findMany();
  } catch (error) {
    console.error('测试失败:', error);
  } finally {
    // 重要：确保关闭连接
    await prisma.$disconnect();
  }
}
```

### 2. 避免创建多个Prisma实例

使用单例模式确保整个应用只有一个Prisma实例：

```typescript
// src/server/db.ts
import { PrismaClient } from "@prisma/client";
import { env } from "~/env";

const createPrismaClient = () =>
  new PrismaClient({
    log: env.NODE_ENV === "development" ? ["query", "error", "warn"] : ["error"],
  });

const globalForPrisma = globalThis as unknown as {
  prisma: ReturnType<typeof createPrismaClient> | undefined;
};

export const db = globalForPrisma.prisma ?? createPrismaClient();

if (env.NODE_ENV !== "production") globalForPrisma.prisma = db;
```

### 3. 使用连接池监控

添加连接池状态监控：

```typescript
// 监控连接池状态
export async function getConnectionPoolStatus() {
  try {
    const result = await db.$queryRaw`SHOW STATUS LIKE 'Threads_connected'`;
    console.log('当前连接数:', result);
    return result;
  } catch (error) {
    console.error('获取连接池状态失败:', error);
  }
}
```

## 测试脚本优化

### 1. 批量操作优化

使用事务和批量操作减少连接使用：

```javascript
// 不推荐：多次单独操作
for (const user of users) {
  await prisma.user.create({ data: user });
}

// 推荐：批量操作
await prisma.user.createMany({
  data: users,
  skipDuplicates: true,
});
```

### 2. 使用事务

将相关操作包装在事务中：

```javascript
await prisma.$transaction(async (tx) => {
  const user = await tx.user.create({ data: userData });
  const project = await tx.project.create({ 
    data: { ...projectData, createdById: user.id } 
  });
  return { user, project };
});
```

### 3. 控制并发数量

在测试中控制并发操作数量：

```javascript
// 使用Promise.all控制并发
const batchSize = 5;
for (let i = 0; i < data.length; i += batchSize) {
  const batch = data.slice(i, i + batchSize);
  await Promise.all(batch.map(item => processItem(item)));
}
```

## 监控和调试

### 1. 启用查询日志

在开发环境启用详细日志：

```typescript
const prisma = new PrismaClient({
  log: [
    { emit: 'event', level: 'query' },
    { emit: 'event', level: 'error' },
    { emit: 'event', level: 'info' },
    { emit: 'event', level: 'warn' },
  ],
});

prisma.$on('query', (e) => {
  console.log('Query: ' + e.query);
  console.log('Duration: ' + e.duration + 'ms');
});
```

### 2. 连接池健康检查

定期检查连接池健康状态：

```typescript
export async function healthCheck() {
  try {
    await db.$queryRaw`SELECT 1`;
    return { status: 'healthy', timestamp: new Date() };
  } catch (error) {
    return { status: 'unhealthy', error: error.message, timestamp: new Date() };
  }
}
```

## 最佳实践

1. **合理设置连接池大小**：根据应用负载和数据库服务器配置调整
2. **及时关闭连接**：在所有数据库操作完成后调用`$disconnect()`
3. **使用事务**：将相关操作组合在一起减少连接使用
4. **监控连接使用**：定期检查连接池状态和性能指标
5. **避免长时间持有连接**：尽快完成数据库操作并释放连接
6. **使用连接池预热**：应用启动时预先建立一些连接

## 故障排除

### 常见错误和解决方案

1. **连接池超时**
   - 增加`pool_timeout`值
   - 检查是否有未关闭的连接
   - 优化查询性能

2. **连接数过多**
   - 减少`connection_limit`值
   - 检查并发操作数量
   - 使用连接池监控

3. **连接泄漏**
   - 确保所有操作都在try-finally中
   - 检查异步操作是否正确处理
   - 使用连接池监控工具

通过以上优化措施，可以有效解决数据库连接池超时问题，提高应用的稳定性和性能。
