# Prisma 模型重命名总结

## 概述
本次更新实现了数据库表名使用蛇形命名法（snake_case），而代码中的模型名保持驼峰命名法（camelCase）的最佳实践。通过 Prisma 的 `@@map` 指令实现了这种分离，确保数据库规范和代码可读性的完美结合。

## 重命名的模型

### 已重命名的模型列表：

1. **`passwordresettoken` → `passwordResetToken`**
   - 数据库表名：`password_reset_token`（通过 `@@map` 指令）
   - 模型名称：`passwordResetToken`（代码中使用）

2. **`projectmember` → `projectMember`**
   - 数据库表名：`project_member`（通过 `@@map` 指令）
   - 模型名称：`projectMember`（代码中使用）

3. **`projectshare` → `projectShare`**
   - 数据库表名：`project_share`（通过 `@@map` 指令）
   - 模型名称：`projectShare`（代码中使用）

4. **`sharepayout` → `sharePayout`**
   - 数据库表名：`share_payout`（通过 `@@map` 指令）
   - 模型名称：`sharePayout`（代码中使用）

5. **`verificationtoken` → `verificationToken`**
   - 数据库表名：`verification_token`（通过 `@@map` 指令）
   - 模型名称：`verificationToken`（代码中使用）

6. **`projectmilestone` → `projectMilestone`**
   - 数据库表名：`project_milestone`（通过 `@@map` 指令）
   - 模型名称：`projectMilestone`（代码中使用）

### 保持不变的模型：
- `account`
- `post`
- `project`
- `session`
- `user`

## 关系字段更新

### 在 `project` 模型中：
- `projectmember` → `projectMember`
- `projectmilestone` → `projectMilestone`
- `projectshare` → `projectShare`

### 在 `user` 模型中：
- `projectmember` → `projectMember`
- `projectmilestone_projectmilestone_assignedToIdTouser` → `projectMilestone_projectMilestone_assignedToIdTouser`
- `projectmilestone_projectmilestone_createdByIdTouser` → `projectMilestone_projectMilestone_createdByIdTouser`
- `projectshare` → `projectShare`

### 在其他模型中：
- `sharePayout` 模型中的 `projectshare` → `projectShare`
- `projectShare` 模型中的 `sharepayout` → `sharePayout`

## 技术实现

### 使用 `@@map` 指令分离命名
使用 Prisma 的 `@@map` 指令实现数据库表名和模型名的分离：

```prisma
model passwordResetToken {
  // 字段定义...
  @@map("password_reset_token")
}
```

这样做的好处：
1. **数据库规范**：数据库表名使用标准的蛇形命名法
2. **代码可读性**：TypeScript 代码中使用驼峰命名法，符合 JavaScript/TypeScript 惯例
3. **最佳实践**：遵循各自领域的命名规范

### 生成的 TypeScript 类型
Prisma 客户端现在生成的类型使用驼峰命名法：

```typescript
// 使用示例
const passwordResetToken = await prisma.passwordResetToken.findUnique({...});
const projectMember = await prisma.projectMember.findMany({...});
const projectShare = await prisma.projectShare.create({...});
```

## 迁移文件
创建了迁移文件：`20250607133220_init_with_snake_case_tables/migration.sql`

此迁移包含了完整的数据库重建，所有表都使用蛇形命名法：
- `password_reset_token`
- `project_member`
- `project_share`
- `share_payout`
- `verification_token`
- `project_milestone`

**注意**：由于执行了 `prisma migrate reset`，原有数据已被清空。

## 验证
- ✅ Prisma schema 语法验证通过
- ✅ Prisma 客户端生成成功
- ✅ 所有模型名称已更新为驼峰命名法
- ✅ 所有关系字段已正确更新
- ✅ 数据库表名已更新为蛇形命名法
- ✅ `@@map` 指令正确映射表名
- ✅ TypeScript 类型生成正确

## 影响范围
此更改影响：
1. **Prisma 客户端 API**：所有模型访问方式使用驼峰命名法
2. **TypeScript 类型**：所有相关类型定义使用驼峰命名法
3. **数据库表名**：使用蛇形命名法，符合数据库命名规范

## 后续步骤
1. ✅ 已更新 Prisma schema 中的模型名称和 `@@map` 指令
2. ✅ 已重新生成 Prisma 客户端
3. 🔄 正在更新应用代码中的模型引用
4. ⏳ 运行测试确保所有功能正常
5. ⏳ 更新文档和 API 规范
