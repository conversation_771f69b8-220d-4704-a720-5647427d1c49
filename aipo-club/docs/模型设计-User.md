# User 模型文档

## 概述

User 模型是 AIPO俱乐部项目管理系统的核心用户数据模型，负责管理用户的基本信息、认证数据、权限控制和个人资料。

## 数据库模型

### 基础字段

| 字段名 | 类型 | 必填 | 默认值 | 描述 | 验证规则 |
|--------|------|------|--------|----|----------|
| `name` | String | ✅ | - | 昵称 | 1-50字符，自动去除空格 |
| `realname` | String | ✅ | - | 姓名 | 1-50字符，自动去除空格 |
| `email` | String | ✅ | - | 邮箱 | 邮箱格式，自动转小写，唯一索引 |
| `phone` | String | ✅ | - | 手机 | 中国手机号格式 (1[3-9]xxxxxxxxx) |
| `password` | String | ✅ | - | 密码 | 最少6字符，自动加密，查询时默认不返回 |

### 个人信息字段

| 字段名 | 类型 | 必填 | 默认值 | 描述 | 验证规则 |
|--------|------|------|--------|------|----------|
| `avatar` | String | ❌ | - | 头像URL | URL格式 |
| `birthday` | Date | ❌ | - | 生日 | 日期格式 |
| `gender` | String | ❌ | - | 性别 | `male`, `female`, `unknown` |
| `country` | String | ❌ | - | 国家 | 最多50字符 |
| `city` | String | ❌ | - | 城市 | 最多50字符 |
| `bio` | String | ❌ | - | 个人简介 | 最多500字符 |
| `occupation` | String | ❌ | - | 职业 | 最多100字符 |
| `education` | String | ❌ | null | 教育信息 | 最大500字符 |
| `inviteCode` | String | ❌ | null | 使用的邀请码 | 6-20字符 |
| `invitedBy` | String | ❌ | null | 邀请人ID | 外键关联user表 |

### 社交链接字段

| 字段名 | 类型 | 必填 | 默认值 | 描述 | 验证规则 |
|--------|------|------|--------|------|----------|
| `github` | String | ❌ | - | GitHub链接 | GitHub URL格式 |
| `linkedin` | String | ❌ | - | LinkedIn链接 | LinkedIn URL格式 |
| `twitter` | String | ❌ | - | Twitter/X链接 | Twitter/X URL格式 |
| `website` | String | ❌ | - | 个人网站 | HTTP/HTTPS URL格式 |

### 技能和兴趣字段

| 字段名 | 类型 | 必填 | 默认值 | 描述 | 验证规则 |
|--------|------|------|--------|------|----------|
| `skills` | String | ❌ | - | 技能描述 | 最多500字符，逗号分隔 |
| `interests` | String | ❌ | - | 兴趣爱好列表 | 最多500字符，逗号分隔 |

### 系统字段

| 字段名 | 类型 | 必填 | 默认值 | 描述 | 枚举值 |
|--------|------|------|--------|------|--------|
| `level` | String | ✅ | `bronze` | 用户等级 | `bronze`, `silver`, `gold`, `platinum`, `diamond` |
| `subscriptionStatus` | String | ✅ | `trial` | 订阅状态 | `active`, `expired`, `cancelled`, `trial` |
| `subscriptionExpiry` | DateTime | ❌ | null | 订阅到期时间 | 订阅过期日期 |
| `role` | String | ✅ | `member` | 用户角色 | `admin`, `manager`, `member` |
| `status` | String | ✅ | `pending` | 用户状态 | `pending`, `active`, `inactive`, `rejected` |
| `tags` | [String] | ❌ | `[]` | 用户标签 | `developer`, `designer`, `analyst`, `operator`, `investor` |

### 认证和安全字段

| 字段名 | 类型 | 必填 | 默认值 | 描述 | 特殊说明 |
|--------|------|------|--------|------|----------|
| `refreshToken` | String | ❌ | - | 刷新令牌 | 查询时默认不返回 |
| `lastLoginAt` | Date | ❌ | - | 最后登录时间 | 自动更新 |
| `loginAttempts` | Number | ❌ | `0` | 登录尝试次数 | 用于账户锁定 |
| `lockUntil` | Date | ❌ | - | 账户锁定截止时间 | 安全机制 |

### 时间戳字段

| 字段名 | 类型 | 必填 | 默认值 | 描述 |
|--------|------|------|--------|------|
| `joinDate` | Date | ✅ | `Date.now` | 加入时间 |
| `createdAt` | Date | ✅ | 自动生成 | 创建时间 |
| `updatedAt` | Date | ✅ | 自动更新 | 更新时间 |

## 用户角色系统

### 角色定义

| 角色 | 中文名 | 权限描述 |
|------|--------|----------|
| `admin` | 超管 | 系统最高权限，可管理所有用户和系统设置 |
| `manager` | 经理 | 可管理项目和审核用户，但不能修改系统设置 |
| `member` | 成员 | 基础用户权限，可参与项目但不能管理其他用户 |

### 用户状态

| 状态 | 中文名 | 描述 |
|------|--------|------|
| `pending` | 待审核 | 新注册用户，等待管理员审核 |
| `active` | 活跃 | 正常使用状态，可以正常登录和使用系统 |
| `inactive` | 非活跃 | 暂时停用，不能登录但保留数据 |
| `rejected` | 已拒绝 | 审核被拒绝，不能登录系统 |

### 用户等级

| 等级 | 中文名 | 描述 | 图标 |
|------|--------|------|------|
| `bronze` | 青铜 | 新手用户，基础功能 | 🥉 |
| `silver` | 白银 | 进阶用户，更多权限 | 🥈 |
| `gold` | 黄金 | 高级用户，高级功能 | 🥇 |
| `platinum` | 铂金 | 专业用户，专业功能 | 💎 |
| `diamond` | 钻石 | 顶级用户，全部功能 | 💠 |

### 订阅状态

| 状态 | 中文名 | 描述 |
|------|--------|------|
| `trial` | 试用 | 试用期用户，功能受限 |
| `active` | 活跃 | 订阅正常，享受完整功能 |
| `expired` | 已过期 | 订阅已过期，功能受限 |
| `cancelled` | 已取消 | 订阅已取消，功能受限 |

### 用户标签

| 标签 | 中文名 | 描述 |
|------|--------|------|
| `developer` | 开发者 | 软件开发相关技能 |
| `designer` | 设计师 | 设计相关技能 |
| `analyst` | 分析师 | 数据分析相关技能 |
| `operator` | 运营 | 运营相关技能 |
| `investor` | 投资人 | 投资相关背景 |

## 数据库索引

```javascript
// 唯一索引
{ email: 1 } // 邮箱唯一性
{ phone: 1 } // 邮箱唯一性

// 查询优化索引
{ role: 1 }      // 按角色查询
{ status: 1 }    // 按状态查询  
{ tags: 1 }      // 按标签查询
{ createdAt: -1 } // 按创建时间倒序
```


## 邀请码系统

### 邀请码表 (invite_code)

| 字段名 | 类型 | 必填 | 默认值 | 描述 |
|--------|------|------|--------|------|
| `id` | String | ✅ | cuid() | 主键ID |
| `code` | String | ✅ | - | 邀请码（6-20字符，唯一） |
| `createdBy` | String | ✅ | - | 创建者ID |
| `maxUses` | Int | ✅ | 1 | 最大使用次数 |
| `usedCount` | Int | ✅ | 0 | 已使用次数 |
| `expiresAt` | DateTime | ❌ | null | 过期时间 |
| `isActive` | Boolean | ✅ | true | 是否激活 |
| `description` | String | ❌ | null | 描述信息 |
| `createdAt` | DateTime | ✅ | now() | 创建时间 |
| `updatedAt` | DateTime | ✅ | 自动更新 | 更新时间 |

### 邀请码使用记录表 (invite_code_usage)

| 字段名 | 类型 | 必填 | 默认值 | 描述 |
|--------|------|------|--------|------|
| `id` | String | ✅ | cuid() | 主键ID |
| `inviteCodeId` | String | ✅ | - | 邀请码ID |
| `userId` | String | ✅ | - | 使用者ID |
| `usedAt` | DateTime | ✅ | now() | 使用时间 |
| `ipAddress` | String | ❌ | null | 使用者IP地址 |
| `userAgent` | String | ❌ | null | 使用者浏览器信息 |

### 邀请码功能特性

1. **邀请码生成**: 用户可以生成自己的邀请码
2. **使用次数限制**: 每个邀请码可设置最大使用次数
3. **过期时间**: 可设置邀请码的过期时间
4. **使用记录**: 记录每次邀请码的使用情况
5. **邀请关系**: 建立邀请人和被邀请人的关系
6. **验证机制**: 注册时验证邀请码的有效性

## 注意事项

1. **邮箱唯一性**: 邮箱地址在系统中必须唯一
2. **密码安全**: 密码会自动加密，不要手动处理
3. **状态管理**: 新用户默认为 `pending` 状态，需要管理员审核
4. **角色权限**: 不同角色有不同的系统权限
5. **邀请码安全**: 邀请码验证包含过期时间、使用次数等多重检查
6. **索引优化**: 根据查询模式建立了相应的数据库索引
