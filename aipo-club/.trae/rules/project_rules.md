# AIPO俱乐部项目开发规则

## 📋 项目概述

本文档定义了AIPO俱乐部项目的开发规则、代码规范、架构原则和最佳实践。所有开发人员必须遵循这些规则，以确保代码质量、项目一致性和团队协作效率。

## 🏗️ 架构原则

### 1. 设计原则
- **DRY (Don't Repeat Yourself)**: 避免代码重复，提取公共逻辑
- **SRP (Single Responsibility Principle)**: 每个模块/函数只负责一个功能
- **Clean Code**: 编写清晰、可读、易维护的代码
- **SOLID原则**: 遵循面向对象设计的五大原则

### 2. 技术栈约束
- **前端**: 必须使用Next.js 15 + React 19 + TypeScript + Tailwind CSS
- **后端**: 必须使用tRPC + Prisma + NextAuth.js v5
- **数据库**: 使用MySQL 8.0，通过Prisma ORM操作
- **样式**: 优先使用Tailwind CSS，避免自定义CSS
- **状态管理**: 使用React内置状态管理，复杂状态考虑Zustand

### 3. 目录结构规范
```
src/
├── app/                    # Next.js App Router页面
│   ├── (auth)/            # 认证相关页面组
│   ├── admin/             # 管理员页面
│   ├── dashboard/         # 用户控制台
│   └── api/               # API路由
├── components/            # React组件
│   ├── ui/               # 通用UI组件
│   ├── project/          # 项目专用组件
│   └── layout/           # 布局组件
├── server/               # 服务端代码
│   ├── api/routers/      # tRPC路由
│   ├── auth/             # 认证配置
│   └── db.ts             # 数据库连接
├── lib/                  # 工具库
├── hooks/                # React Hooks
├── types/                # TypeScript类型
├── config/               # 配置文件
└── styles/               # 全局样式
```

## 💻 代码规范

### 1. TypeScript规范

#### 类型定义
```typescript
// ✅ 正确：使用interface定义对象类型
interface User {
  id: string;
  name: string;
  email: string;
  createdAt: Date;
}

// ✅ 正确：使用type定义联合类型
type UserRole = 'ADMIN' | 'MANAGER' | 'MEMBER';

// ❌ 错误：避免使用any类型
const userData: any = {}; // 禁止

// ✅ 正确：使用具体类型
const userData: User = {
  id: '1',
  name: 'John',
  email: '<EMAIL>',
  createdAt: new Date()
};
```

#### 函数定义
```typescript
// ✅ 正确：明确的参数和返回类型
function createUser(userData: CreateUserInput): Promise<User> {
  return api.user.create.mutate(userData);
}

// ✅ 正确：使用箭头函数的简洁形式
const validateEmail = (email: string): boolean => {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
};
```

### 2. React组件规范

#### 组件定义
```typescript
// ✅ 正确：使用interface定义Props
interface UserCardProps {
  user: User;
  onEdit?: (user: User) => void;
  className?: string;
}

// ✅ 正确：使用函数组件
export function UserCard({ user, onEdit, className }: UserCardProps) {
  return (
    <div className={cn("p-4 border rounded-lg", className)}>
      <h3 className="text-lg font-semibold">{user.name}</h3>
      <p className="text-gray-600">{user.email}</p>
      {onEdit && (
        <button onClick={() => onEdit(user)}>编辑</button>
      )}
    </div>
  );
}
```

#### Hooks使用
```typescript
// ✅ 正确：自定义Hook命名以use开头
function useUserManagement() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  
  const fetchUsers = useCallback(async () => {
    setLoading(true);
    try {
      const data = await api.user.list.query();
      setUsers(data);
    } finally {
      setLoading(false);
    }
  }, []);
  
  return { users, loading, fetchUsers };
}
```

### 3. tRPC API规范

#### 路由定义
```typescript
// ✅ 正确：使用Zod进行输入验证
export const userRouter = createTRPCRouter({
  create: protectedProcedure
    .input(z.object({
      name: z.string().min(1).max(100),
      email: z.string().email(),
      role: z.enum(['ADMIN', 'MANAGER', 'MEMBER'])
    }))
    .mutation(async ({ input, ctx }) => {
      return ctx.db.user.create({
        data: input
      });
    }),
    
  list: protectedProcedure
    .input(z.object({
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(100).default(10)
    }))
    .query(async ({ input, ctx }) => {
      return ctx.db.user.findMany({
        skip: (input.page - 1) * input.limit,
        take: input.limit
      });
    })
});
```

### 4. 数据库规范

#### Prisma Schema
```prisma
// ✅ 正确：使用snake_case命名表和字段
model user {
  id         String   @id @default(cuid())
  name       String
  email      String   @unique
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  
  // 关系定义
  projects project_member[]
  
  @@map("users")
}

model project {
  id          String   @id @default(cuid())
  name        String
  description String?
  status      String   @default("IDEATION")
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt
  
  // 关系定义
  members project_member[]
  
  @@map("projects")
}
```

#### 数据库查询
```typescript
// ✅ 正确：使用事务处理复杂操作
async function createProjectWithMembers(projectData: CreateProjectInput) {
  return await db.$transaction(async (tx) => {
    const project = await tx.project.create({
      data: projectData
    });
    
    await tx.projectMember.create({
      data: {
        projectId: project.id,
        userId: projectData.ownerId,
        role: 'OWNER'
      }
    });
    
    return project;
  });
}
```

## 🎨 UI/UX规范

### 1. 组件设计原则
- **一致性**: 使用统一的设计系统和组件库
- **可复用性**: 创建可复用的UI组件
- **响应式**: 支持移动端和桌面端
- **可访问性**: 遵循WCAG 2.1 AA标准

### 2. Tailwind CSS使用规范
```typescript
// ✅ 正确：使用cn工具函数合并类名
import { cn } from "@/lib/utils";

function Button({ className, variant = "default", ...props }) {
  return (
    <button
      className={cn(
        "px-4 py-2 rounded-md font-medium transition-colors",
        {
          "bg-blue-600 text-white hover:bg-blue-700": variant === "default",
          "bg-red-600 text-white hover:bg-red-700": variant === "destructive",
          "border border-gray-300 hover:bg-gray-50": variant === "outline"
        },
        className
      )}
      {...props}
    />
  );
}
```

### 3. 响应式设计
```typescript
// ✅ 正确：使用Tailwind响应式前缀
<div className="
  grid grid-cols-1 gap-4
  md:grid-cols-2 md:gap-6
  lg:grid-cols-3 lg:gap-8
">
  {/* 内容 */}
</div>
```

## 🔒 安全规范

### 1. 认证和授权
```typescript
// ✅ 正确：使用protectedProcedure保护API
export const adminRouter = createTRPCRouter({
  deleteUser: protectedProcedure
    .input(z.object({ userId: z.string() }))
    .use(requireRole(['ADMIN'])) // 角色检查中间件
    .mutation(async ({ input, ctx }) => {
      // 只有管理员可以删除用户
      return ctx.db.user.delete({
        where: { id: input.userId }
      });
    })
});
```

### 2. 数据验证
```typescript
// ✅ 正确：前后端都进行数据验证
const createUserSchema = z.object({
  name: z.string().min(1, "姓名不能为空").max(100, "姓名过长"),
  email: z.string().email("邮箱格式不正确"),
  password: z.string().min(8, "密码至少8位").regex(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
    "密码必须包含大小写字母和数字"
  )
});
```

### 3. 环境变量管理
```typescript
// ✅ 正确：使用env.js统一管理环境变量
import { createEnv } from "@t3-oss/env-nextjs";
import { z } from "zod";

export const env = createEnv({
  server: {
    DATABASE_URL: z.string().url(),
    AUTH_SECRET: z.string().min(1),
    SMTP_HOST: z.string().min(1)
  },
  client: {
    NEXT_PUBLIC_APP_URL: z.string().url()
  },
  runtimeEnv: {
    DATABASE_URL: process.env.DATABASE_URL,
    AUTH_SECRET: process.env.AUTH_SECRET,
    NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL
  }
});
```

## 🧪 测试规范

### 1. 单元测试
```typescript
// ✅ 正确：为工具函数编写单元测试
import { validateEmail } from '@/lib/utils';

describe('validateEmail', () => {
  it('should return true for valid email', () => {
    expect(validateEmail('<EMAIL>')).toBe(true);
  });
  
  it('should return false for invalid email', () => {
    expect(validateEmail('invalid-email')).toBe(false);
  });
});
```

### 2. API测试
```typescript
// ✅ 正确：测试tRPC路由
import { createTRPCMsw } from 'msw-trpc';
import { appRouter } from '@/server/api/root';

const trpcMsw = createTRPCMsw(appRouter);

describe('User API', () => {
  it('should create user successfully', async () => {
    const userData = {
      name: 'Test User',
      email: '<EMAIL>',
      role: 'MEMBER' as const
    };
    
    const result = await caller.user.create(userData);
    expect(result.name).toBe(userData.name);
  });
});
```

### 3. E2E测试
```typescript
// ✅ 正确：使用Playwright进行E2E测试
import { test, expect } from '@playwright/test';

test('user can login and access dashboard', async ({ page }) => {
  await page.goto('/login');
  
  await page.fill('[name="email"]', '<EMAIL>');
  await page.fill('[name="password"]', 'password123');
  await page.click('button[type="submit"]');
  
  await expect(page).toHaveURL('/dashboard');
  await expect(page.locator('h1')).toContainText('控制台');
});
```

## 📝 文档规范

### 1. 代码注释
```typescript
/**
 * 创建新项目并分配初始成员
 * @param projectData 项目基础信息
 * @param ownerId 项目所有者ID
 * @returns 创建的项目信息
 * @throws {TRPCError} 当用户权限不足时抛出错误
 */
async function createProject(
  projectData: CreateProjectInput,
  ownerId: string
): Promise<Project> {
  // 实现逻辑
}
```

### 2. README文档
- 每个主要功能模块都应有README.md
- 包含功能说明、使用方法、API文档
- 提供代码示例和最佳实践

### 3. API文档
- 使用tRPC自动生成的类型文档
- 为复杂API提供使用示例
- 说明错误处理和边界情况

## 🚀 部署规范

### 1. 环境配置
```bash
# 开发环境
npm run dev

# 构建检查
npm run build
npm run typecheck
npm run lint

# 数据库操作
npm run db:push      # 开发环境
npm run db:migrate   # 生产环境
```

### 2. Docker配置
```dockerfile
# ✅ 正确：多阶段构建优化镜像大小
FROM node:18-alpine AS deps
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS builder
WORKDIR /app
COPY . .
RUN npm run build

FROM node:18-alpine AS runner
WORKDIR /app
COPY --from=builder /app/.next/standalone ./
EXPOSE 3000
CMD ["node", "server.js"]
```

### 3. CI/CD流程
```yaml
# ✅ 正确：GitHub Actions配置
name: CI/CD
on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
      - run: npm ci
      - run: npm run typecheck
      - run: npm run lint
      - run: npm run test
```

## 🔧 开发工具配置

### 1. VSCode配置
```json
// .vscode/settings.json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  }
}
```

### 2. Git规范
```bash
# ✅ 正确：提交信息格式
git commit -m "feat: 添加用户管理功能"
git commit -m "fix: 修复登录页面样式问题"
git commit -m "docs: 更新API文档"
git commit -m "refactor: 重构项目状态管理"
```

### 3. 分支管理
- `main`: 生产环境分支
- `develop`: 开发环境分支
- `feature/*`: 功能开发分支
- `hotfix/*`: 紧急修复分支

## ⚡ 性能优化规范

### 1. 前端优化
```typescript
// ✅ 正确：使用React.memo优化组件渲染
const UserCard = React.memo(({ user }: { user: User }) => {
  return (
    <div className="p-4 border rounded">
      <h3>{user.name}</h3>
      <p>{user.email}</p>
    </div>
  );
});

// ✅ 正确：使用useMemo缓存计算结果
const expensiveValue = useMemo(() => {
  return heavyCalculation(data);
}, [data]);

// ✅ 正确：使用useCallback缓存函数
const handleClick = useCallback((id: string) => {
  onItemClick(id);
}, [onItemClick]);
```

### 2. 数据库优化
```typescript
// ✅ 正确：使用select减少数据传输
const users = await db.user.findMany({
  select: {
    id: true,
    name: true,
    email: true
  },
  where: {
    status: 'ACTIVE'
  }
});

// ✅ 正确：使用include预加载关联数据
const projects = await db.project.findMany({
  include: {
    members: {
      select: {
        user: {
          select: { name: true, email: true }
        },
        role: true
      }
    }
  }
});
```

### 3. 图片优化
```typescript
// ✅ 正确：使用Next.js Image组件
import Image from 'next/image';

<Image
  src={user.avatar}
  alt={user.name}
  width={64}
  height={64}
  className="rounded-full"
  priority={false}
  placeholder="blur"
  blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
/>
```

## 🚨 错误处理规范

### 1. API错误处理
```typescript
// ✅ 正确：统一的错误处理
import { TRPCError } from '@trpc/server';

export const userRouter = createTRPCRouter({
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      const user = await ctx.db.user.findUnique({
        where: { id: input.id }
      });
      
      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '用户不存在'
        });
      }
      
      if (user.role === 'ADMIN' && ctx.session.user.role !== 'ADMIN') {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: '权限不足'
        });
      }
      
      return ctx.db.user.delete({
        where: { id: input.id }
      });
    })
});
```

### 2. 前端错误处理
```typescript
// ✅ 正确：使用Error Boundary
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }
  
  static getDerivedStateFromError(error) {
    return { hasError: true };
  }
  
  componentDidCatch(error, errorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }
  
  render() {
    if (this.state.hasError) {
      return <ErrorFallback />;
    }
    
    return this.props.children;
  }
}
```

## 📊 监控和日志规范

### 1. 日志记录
```typescript
// ✅ 正确：结构化日志
import { logger } from '@/lib/logger';

export async function createProject(data: CreateProjectInput) {
  logger.info('Creating project', {
    userId: data.ownerId,
    projectName: data.name,
    timestamp: new Date().toISOString()
  });
  
  try {
    const project = await db.project.create({ data });
    
    logger.info('Project created successfully', {
      projectId: project.id,
      userId: data.ownerId
    });
    
    return project;
  } catch (error) {
    logger.error('Failed to create project', {
      error: error.message,
      userId: data.ownerId,
      projectName: data.name
    });
    
    throw error;
  }
}
```

### 2. 性能监控
```typescript
// ✅ 正确：API性能监控
export const withTiming = <T extends (...args: any[]) => any>(
  fn: T,
  name: string
): T => {
  return ((...args: any[]) => {
    const start = Date.now();
    const result = fn(...args);
    
    if (result instanceof Promise) {
      return result.finally(() => {
        const duration = Date.now() - start;
        logger.info(`${name} completed`, { duration });
      });
    }
    
    const duration = Date.now() - start;
    logger.info(`${name} completed`, { duration });
    return result;
  }) as T;
};
```

## 🔄 版本控制规范

### 1. 语义化版本
- **主版本号**: 不兼容的API修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

### 2. 发布流程
```bash
# 1. 更新版本号
npm version patch  # 修复版本
npm version minor  # 功能版本
npm version major  # 重大版本

# 2. 生成变更日志
npm run changelog

# 3. 创建发布标签
git tag -a v1.2.3 -m "Release version 1.2.3"

# 4. 推送到远程仓库
git push origin main --tags
```

## 📋 代码审查规范

### 1. Pull Request要求
- **标题**: 清晰描述变更内容
- **描述**: 详细说明变更原因和影响
- **测试**: 包含相关测试用例
- **文档**: 更新相关文档

### 2. 审查检查清单
- [ ] 代码符合项目规范
- [ ] 包含适当的测试
- [ ] 文档已更新
- [ ] 性能影响已评估
- [ ] 安全问题已考虑
- [ ] 向后兼容性已确认

## 🎯 总结

遵循这些开发规则将确保：

1. **代码质量**: 统一的代码风格和最佳实践
2. **团队协作**: 清晰的规范和流程
3. **项目维护**: 易于理解和修改的代码
4. **性能优化**: 高效的应用程序
5. **安全保障**: 安全的代码和部署
6. **可扩展性**: 支持项目长期发展

所有开发人员都应该熟悉并严格遵循这些规则。如有疑问或建议，请及时与团队沟通。

---

**文档版本**: v1.0  
**最后更新**: 2024年12月  
**适用范围**: AIPO俱乐部项目全体开发人员