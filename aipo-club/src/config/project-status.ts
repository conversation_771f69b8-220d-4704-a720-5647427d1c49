/**
 * 项目管理配置中心
 *
 * 所有项目管理相关的枚举和配置都在这里统一管理
 * 任何配置变更只需要修改这一个文件
 */

// 项目状态枚举
export const PROJECT_STATUS = {
  IDEATION: 'ideation',
  PLANNING: 'planning',
  DEVELOPING: 'developing',
  TESTING: 'testing',
  LAUNCHING: 'launching',
  PROMOTING: 'promoting',
  PROFITING: 'profiting',
  MAINTAINING: 'maintaining',
  DECLINING: 'declining',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
} as const;

export type ProjectStatus = typeof PROJECT_STATUS[keyof typeof PROJECT_STATUS];

// 状态配置接口
interface StatusConfig {
  label: string;
  description: string;
  color: string;
  variant: 'default' | 'info' | 'warning' | 'success' | 'error' | 'purple';
  activities: string[];
  icon: string;
}

// 统一状态配置
export const PROJECT_STATUS_CONFIG: Record<ProjectStatus, StatusConfig> = {
  [PROJECT_STATUS.IDEATION]: {
    label: '创意期',
    description: '项目想法阶段，收集和评估创意',
    color: 'bg-gray-100 text-gray-800',
    variant: 'default',
    activities: ['头脑风暴', '市场调研', '可行性分析'],
    icon: '🔍'
  },
  [PROJECT_STATUS.PLANNING]: {
    label: '规划中',
    description: '制定详细计划和方案',
    color: 'bg-blue-100 text-blue-800',
    variant: 'info',
    activities: ['需求分析', '架构设计', '计划制定'],
    icon: '📋'
  },
  [PROJECT_STATUS.DEVELOPING]: {
    label: '开发中',
    description: '项目正在开发实施',
    color: 'bg-orange-100 text-orange-800',
    variant: 'warning',
    activities: ['编码开发', '功能实现', '集成测试'],
    icon: '💻'
  },
  [PROJECT_STATUS.TESTING]: {
    label: '测试中',
    description: '测试验证和优化',
    color: 'bg-purple-100 text-purple-800',
    variant: 'purple',
    activities: ['功能测试', '性能测试', '用户测试'],
    icon: '🧪'
  },
  [PROJECT_STATUS.LAUNCHING]: {
    label: '上线中',
    description: '项目正在发布上线',
    color: 'bg-green-100 text-green-800',
    variant: 'success',
    activities: ['产品发布', '环境部署', '上线配置'],
    icon: '🚀'
  },
  [PROJECT_STATUS.PROMOTING]: {
    label: '推广期',
    description: '推广产品，获取用户',
    color: 'bg-emerald-100 text-emerald-800',
    variant: 'success',
    activities: ['用户推广', '市场营销', '用户获取'],
    icon: '📈'
  },
  [PROJECT_STATUS.PROFITING]: {
    label: '盈利期',
    description: '项目开始产生收益',
    color: 'bg-yellow-100 text-yellow-800',
    variant: 'warning',
    activities: ['收益优化', '商业化', '盈利增长'],
    icon: '💰'
  },
  [PROJECT_STATUS.MAINTAINING]: {
    label: '维护期',
    description: '稳定维护和持续优化',
    color: 'bg-teal-100 text-teal-800',
    variant: 'info',
    activities: ['日常维护', '功能优化', '稳定运营'],
    icon: '🔄'
  },
  [PROJECT_STATUS.DECLINING]: {
    label: '衰退期',
    description: '用户或收入下降',
    color: 'bg-red-100 text-red-800',
    variant: 'error',
    activities: ['问题诊断', '策略调整', '转型决策'],
    icon: '📉'
  },
  [PROJECT_STATUS.COMPLETED]: {
    label: '已完成',
    description: '项目达成目标并结束',
    color: 'bg-green-100 text-green-800',
    variant: 'success',
    activities: ['目标达成', '项目总结', '资料归档'],
    icon: '✅'
  },
  [PROJECT_STATUS.CANCELLED]: {
    label: '已取消',
    description: '项目被取消或放弃',
    color: 'bg-red-100 text-red-800',
    variant: 'error',
    activities: ['资源重新分配', '经验总结'],
    icon: '❌'
  },
};

// 状态流转规则
export const STATUS_TRANSITIONS: Record<ProjectStatus, ProjectStatus[]> = {
  [PROJECT_STATUS.IDEATION]: [PROJECT_STATUS.PLANNING, PROJECT_STATUS.CANCELLED],
  [PROJECT_STATUS.PLANNING]: [PROJECT_STATUS.DEVELOPING, PROJECT_STATUS.IDEATION, PROJECT_STATUS.CANCELLED],
  [PROJECT_STATUS.DEVELOPING]: [PROJECT_STATUS.TESTING, PROJECT_STATUS.PLANNING, PROJECT_STATUS.CANCELLED],
  [PROJECT_STATUS.TESTING]: [PROJECT_STATUS.LAUNCHING, PROJECT_STATUS.DEVELOPING, PROJECT_STATUS.CANCELLED],
  [PROJECT_STATUS.LAUNCHING]: [PROJECT_STATUS.PROMOTING, PROJECT_STATUS.DECLINING, PROJECT_STATUS.MAINTAINING, PROJECT_STATUS.CANCELLED],
  [PROJECT_STATUS.PROMOTING]: [PROJECT_STATUS.PROFITING, PROJECT_STATUS.MAINTAINING, PROJECT_STATUS.DECLINING],
  [PROJECT_STATUS.PROFITING]: [PROJECT_STATUS.MAINTAINING, PROJECT_STATUS.DECLINING],
  [PROJECT_STATUS.MAINTAINING]: [PROJECT_STATUS.DECLINING, PROJECT_STATUS.COMPLETED],
  [PROJECT_STATUS.DECLINING]: [PROJECT_STATUS.MAINTAINING, PROJECT_STATUS.CANCELLED, PROJECT_STATUS.COMPLETED],
  [PROJECT_STATUS.COMPLETED]: [],
  [PROJECT_STATUS.CANCELLED]: [],
};

// 工具函数
export function getStatusConfig(status: string): StatusConfig {
  return PROJECT_STATUS_CONFIG[status as ProjectStatus] || PROJECT_STATUS_CONFIG[PROJECT_STATUS.IDEATION];
}

export function getStatusLabel(status: string): string {
  const config = getStatusConfig(status);
  return `${config.icon} ${config.label}`;
}

export function getStatusOptions(): Array<{ value: ProjectStatus; label: string }> {
  return Object.values(PROJECT_STATUS).map(status => ({
    value: status,
    label: getStatusLabel(status)
  }));
}

export function getAvailableTransitions(currentStatus: string): ProjectStatus[] {
  return STATUS_TRANSITIONS[currentStatus as ProjectStatus] || [];
}

export function isValidTransition(from: string, to: string): boolean {
  const availableTransitions = getAvailableTransitions(from);
  return availableTransitions.includes(to as ProjectStatus);
}

// 用于后端API的枚举数组
export const PROJECT_STATUS_VALUES = Object.values(PROJECT_STATUS);

// 用于前端表单的选项数组
export const PROJECT_STATUS_OPTIONS = getStatusOptions();
