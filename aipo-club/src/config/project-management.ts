/**
 * 项目管理配置中心
 * 
 * 所有项目管理相关的枚举和配置都在这里统一管理
 * 任何配置变更只需要修改这一个文件
 */

// ==================== 项目状态 ====================

export const PROJECT_STATUS = {
  IDEATION: 'ideation',
  PLANNING: 'planning',
  DEVELOPING: 'developing',
  TESTING: 'testing',
  LAUNCHING: 'launching',
  PROMOTING: 'promoting',
  PROFITING: 'profiting',
  MAINTAINING: 'maintaining',
  DECLINING: 'declining',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
} as const;

export type ProjectStatus = typeof PROJECT_STATUS[keyof typeof PROJECT_STATUS];

interface StatusConfig {
  label: string;
  description: string;
  color: string;
  variant: 'default' | 'info' | 'warning' | 'success' | 'error' | 'purple';
  activities: string[];
  icon: string;
}

export const PROJECT_STATUS_CONFIG: Record<ProjectStatus, StatusConfig> = {
  [PROJECT_STATUS.IDEATION]: {
    label: '创意期',
    description: '项目想法阶段，收集和评估创意',
    color: 'bg-gray-100 text-gray-800',
    variant: 'default',
    activities: ['头脑风暴', '市场调研', '可行性分析'],
    icon: '🔍'
  },
  [PROJECT_STATUS.PLANNING]: {
    label: '规划中',
    description: '制定详细计划和方案',
    color: 'bg-blue-100 text-blue-800',
    variant: 'info',
    activities: ['需求分析', '架构设计', '计划制定'],
    icon: '📋'
  },
  [PROJECT_STATUS.DEVELOPING]: {
    label: '开发中',
    description: '项目正在开发实施',
    color: 'bg-orange-100 text-orange-800',
    variant: 'warning',
    activities: ['编码开发', '功能实现', '集成测试'],
    icon: '💻'
  },
  [PROJECT_STATUS.TESTING]: {
    label: '测试中',
    description: '测试验证和优化',
    color: 'bg-purple-100 text-purple-800',
    variant: 'purple',
    activities: ['功能测试', '性能测试', '用户测试'],
    icon: '🧪'
  },
  [PROJECT_STATUS.LAUNCHING]: {
    label: '上线中',
    description: '项目正在发布上线',
    color: 'bg-green-100 text-green-800',
    variant: 'success',
    activities: ['产品发布', '环境部署', '上线配置'],
    icon: '🚀'
  },
  [PROJECT_STATUS.PROMOTING]: {
    label: '推广期',
    description: '推广产品，获取用户',
    color: 'bg-emerald-100 text-emerald-800',
    variant: 'success',
    activities: ['用户推广', '市场营销', '用户获取'],
    icon: '📈'
  },
  [PROJECT_STATUS.PROFITING]: {
    label: '盈利期',
    description: '项目开始产生收益',
    color: 'bg-yellow-100 text-yellow-800',
    variant: 'warning',
    activities: ['收益优化', '商业化', '盈利增长'],
    icon: '💰'
  },
  [PROJECT_STATUS.MAINTAINING]: {
    label: '维护期',
    description: '稳定维护和持续优化',
    color: 'bg-teal-100 text-teal-800',
    variant: 'info',
    activities: ['日常维护', '功能优化', '稳定运营'],
    icon: '🔄'
  },
  [PROJECT_STATUS.DECLINING]: {
    label: '衰退期',
    description: '用户或收入下降',
    color: 'bg-red-100 text-red-800',
    variant: 'error',
    activities: ['问题诊断', '策略调整', '转型决策'],
    icon: '📉'
  },
  [PROJECT_STATUS.COMPLETED]: {
    label: '已完成',
    description: '项目达成目标并结束',
    color: 'bg-green-100 text-green-800',
    variant: 'success',
    activities: ['目标达成', '项目总结', '资料归档'],
    icon: '✅'
  },
  [PROJECT_STATUS.CANCELLED]: {
    label: '已取消',
    description: '项目被取消或放弃',
    color: 'bg-red-100 text-red-800',
    variant: 'error',
    activities: ['资源重新分配', '经验总结'],
    icon: '❌'
  },
};

// ==================== 项目类型 ====================

export const PROJECT_TYPE = {
  WEBSITE: 'website',
  APP: 'app',
  MINIPROGRAM: 'miniprogram',
  BROWSER_EXTENSION: 'browser_extension',
  WECHAT_OFFICIAL: 'wechat_official',
  SAAS: 'saas',
  API: 'api',
  TOOL: 'tool',
  GAME: 'game',
  DESKTOP_APP: 'desktop_app',
  OTHER: 'other',
} as const;

export type ProjectType = typeof PROJECT_TYPE[keyof typeof PROJECT_TYPE];

interface TypeConfig {
  label: string;
  description: string;
  icon: string;
}

export const PROJECT_TYPE_CONFIG: Record<ProjectType, TypeConfig> = {
  [PROJECT_TYPE.WEBSITE]: {
    label: '网站',
    description: '传统网站项目',
    icon: '🌐'
  },
  [PROJECT_TYPE.APP]: {
    label: '移动应用',
    description: 'iOS/Android应用',
    icon: '📱'
  },
  [PROJECT_TYPE.MINIPROGRAM]: {
    label: '小程序',
    description: '微信/支付宝小程序',
    icon: '📲'
  },
  [PROJECT_TYPE.BROWSER_EXTENSION]: {
    label: '浏览器插件',
    description: 'Chrome/Firefox插件',
    icon: '🧩'
  },
  [PROJECT_TYPE.WECHAT_OFFICIAL]: {
    label: '公众号',
    description: '微信公众号项目',
    icon: '💬'
  },
  [PROJECT_TYPE.SAAS]: {
    label: 'SaaS产品',
    description: '软件即服务产品',
    icon: '☁️'
  },
  [PROJECT_TYPE.API]: {
    label: 'API服务',
    description: '接口服务项目',
    icon: '🔌'
  },
  [PROJECT_TYPE.TOOL]: {
    label: '工具软件',
    description: '实用工具软件',
    icon: '🛠️'
  },
  [PROJECT_TYPE.GAME]: {
    label: '游戏',
    description: '游戏项目',
    icon: '🎮'
  },
  [PROJECT_TYPE.DESKTOP_APP]: {
    label: '桌面应用',
    description: '桌面应用程序',
    icon: '💻'
  },
  [PROJECT_TYPE.OTHER]: {
    label: '其他',
    description: '其他类型项目',
    icon: '📦'
  },
};

// ==================== 项目优先级 ====================

export const PROJECT_PRIORITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  URGENT: 'urgent',
} as const;

export type ProjectPriority = typeof PROJECT_PRIORITY[keyof typeof PROJECT_PRIORITY];

interface PriorityConfig {
  label: string;
  description: string;
  color: string;
  variant: 'default' | 'info' | 'warning' | 'success' | 'error';
  icon: string;
}

export const PROJECT_PRIORITY_CONFIG: Record<ProjectPriority, PriorityConfig> = {
  [PROJECT_PRIORITY.LOW]: {
    label: '低',
    description: '低优先级，可以延后处理',
    color: 'bg-gray-100 text-gray-800',
    variant: 'default',
    icon: '🔵'
  },
  [PROJECT_PRIORITY.MEDIUM]: {
    label: '中',
    description: '中等优先级，正常处理',
    color: 'bg-blue-100 text-blue-800',
    variant: 'info',
    icon: '🟡'
  },
  [PROJECT_PRIORITY.HIGH]: {
    label: '高',
    description: '高优先级，需要优先处理',
    color: 'bg-orange-100 text-orange-800',
    variant: 'warning',
    icon: '🟠'
  },
  [PROJECT_PRIORITY.URGENT]: {
    label: '紧急',
    description: '紧急优先级，需要立即处理',
    color: 'bg-red-100 text-red-800',
    variant: 'error',
    icon: '🔴'
  },
};

// ==================== 成员角色 ====================

export const MEMBER_ROLE = {
  PO: 'po',
  PRODUCT: 'product',
  FRONTEND: 'frontend',
  BACKEND: 'backend',
  TESTER: 'tester',
  OPERATION: 'operation',
  FULLSTACK: 'fullstack',
  VISITOR: 'visitor',
  OTHER: 'other',
  PLATFORM: 'platform',
} as const;

export type MemberRole = typeof MEMBER_ROLE[keyof typeof MEMBER_ROLE];

interface RoleConfig {
  label: string;
  description: string;
  color: string;
  permissions: string[];
  icon: string;
}

export const MEMBER_ROLE_CONFIG: Record<MemberRole, RoleConfig> = {
  [MEMBER_ROLE.PO]: {
    label: 'PO',
    description: 'Product Owner，项目整体负责',
    color: 'bg-purple-100 text-purple-800',
    permissions: ['全部权限'],
    icon: '👑'
  },
  [MEMBER_ROLE.PRODUCT]: {
    label: '产品',
    description: '产品经理/产品设计师',
    color: 'bg-blue-100 text-blue-800',
    permissions: ['产品设计', '需求管理', '用户研究'],
    icon: '📋'
  },
  [MEMBER_ROLE.FRONTEND]: {
    label: '前端',
    description: '前端开发工程师',
    color: 'bg-green-100 text-green-800',
    permissions: ['前端开发', '界面实现', '用户交互'],
    icon: '💻'
  },
  [MEMBER_ROLE.BACKEND]: {
    label: '后端',
    description: '后端开发工程师',
    color: 'bg-emerald-100 text-emerald-800',
    permissions: ['后端开发', '数据库设计', 'API开发'],
    icon: '⚙️'
  },
  [MEMBER_ROLE.TESTER]: {
    label: '测试',
    description: '测试工程师/QA',
    color: 'bg-yellow-100 text-yellow-800',
    permissions: ['测试执行', '质量保证', '缺陷报告'],
    icon: '🧪'
  },
  [MEMBER_ROLE.OPERATION]: {
    label: '运营',
    description: '运营专员/市场推广',
    color: 'bg-pink-100 text-pink-800',
    permissions: ['运营推广', '用户增长', '数据分析'],
    icon: '📈'
  },
  [MEMBER_ROLE.FULLSTACK]: {
    label: '全栈',
    description: '全栈开发工程师',
    color: 'bg-indigo-100 text-indigo-800',
    permissions: ['全栈开发', '架构设计', '技术选型'],
    icon: '🚀'
  },
  [MEMBER_ROLE.VISITOR]: {
    label: '访客',
    description: '可浏览项目信息',
    color: 'bg-gray-100 text-gray-800',
    permissions: ['只读查看'],
    icon: '👁️'
  },
  [MEMBER_ROLE.OTHER]: {
    label: '其他',
    description: '其他角色',
    color: 'bg-gray-100 text-gray-800',
    permissions: ['基本查看', '任务执行'],
    icon: '👥'
  },
  [MEMBER_ROLE.PLATFORM]: {
    label: '平台',
    description: '平台方代表',
    color: 'bg-slate-100 text-slate-800',
    permissions: ['平台监督', '数据查看'],
    icon: '🏢'
  },
};

// ==================== 成员状态 ====================

export const MEMBER_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  PENDING: 'pending',
  REMOVED: 'removed',
} as const;

export type MemberStatus = typeof MEMBER_STATUS[keyof typeof MEMBER_STATUS];

interface MemberStatusConfig {
  label: string;
  description: string;
  color: string;
  icon: string;
}

export const MEMBER_STATUS_CONFIG: Record<MemberStatus, MemberStatusConfig> = {
  [MEMBER_STATUS.ACTIVE]: {
    label: '活跃',
    description: '正常参与项目',
    color: 'bg-green-100 text-green-800',
    icon: '✅'
  },
  [MEMBER_STATUS.INACTIVE]: {
    label: '不活跃',
    description: '暂时不参与项目',
    color: 'bg-yellow-100 text-yellow-800',
    icon: '⏸️'
  },
  [MEMBER_STATUS.PENDING]: {
    label: '待确认',
    description: '等待确认加入',
    color: 'bg-blue-100 text-blue-800',
    icon: '⏳'
  },
  [MEMBER_STATUS.REMOVED]: {
    label: '已移除',
    description: '已从项目中移除',
    color: 'bg-red-100 text-red-800',
    icon: '❌'
  },
};

// ==================== 里程碑状态 ====================

export const MILESTONE_STATUS = {
  PENDING: 'pending',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
} as const;

export type MilestoneStatus = typeof MILESTONE_STATUS[keyof typeof MILESTONE_STATUS];

interface MilestoneStatusConfig {
  label: string;
  description: string;
  color: string;
  icon: string;
}

export const MILESTONE_STATUS_CONFIG: Record<MilestoneStatus, MilestoneStatusConfig> = {
  [MILESTONE_STATUS.PENDING]: {
    label: '待开始',
    description: '里程碑尚未开始',
    color: 'bg-gray-100 text-gray-800',
    icon: '⏳'
  },
  [MILESTONE_STATUS.IN_PROGRESS]: {
    label: '进行中',
    description: '里程碑正在进行',
    color: 'bg-blue-100 text-blue-800',
    icon: '🔄'
  },
  [MILESTONE_STATUS.COMPLETED]: {
    label: '已完成',
    description: '里程碑已完成',
    color: 'bg-green-100 text-green-800',
    icon: '✅'
  },
  [MILESTONE_STATUS.CANCELLED]: {
    label: '已取消',
    description: '里程碑已取消',
    color: 'bg-red-100 text-red-800',
    icon: '❌'
  },
};

// ==================== 分成类型 ====================

export const SHARE_TYPE = {
  EQUITY: 'equity',
  REVENUE: 'revenue',
  BONUS: 'bonus',
  COMMISSION: 'commission',
} as const;

export type ShareType = typeof SHARE_TYPE[keyof typeof SHARE_TYPE];

interface ShareTypeConfig {
  label: string;
  description: string;
  icon: string;
}

export const SHARE_TYPE_CONFIG: Record<ShareType, ShareTypeConfig> = {
  [SHARE_TYPE.EQUITY]: {
    label: '股权分成',
    description: '按股权比例分配收益',
    icon: '📊'
  },
  [SHARE_TYPE.REVENUE]: {
    label: '收入分成',
    description: '按收入比例分配',
    icon: '💰'
  },
  [SHARE_TYPE.BONUS]: {
    label: '奖金',
    description: '一次性奖励',
    icon: '🎁'
  },
  [SHARE_TYPE.COMMISSION]: {
    label: '佣金',
    description: '按业绩提成',
    icon: '💸'
  },
};

// ==================== 状态流转规则 ====================

export const STATUS_TRANSITIONS: Record<ProjectStatus, ProjectStatus[]> = {
  [PROJECT_STATUS.IDEATION]: [PROJECT_STATUS.PLANNING, PROJECT_STATUS.CANCELLED],
  [PROJECT_STATUS.PLANNING]: [PROJECT_STATUS.DEVELOPING, PROJECT_STATUS.IDEATION, PROJECT_STATUS.CANCELLED],
  [PROJECT_STATUS.DEVELOPING]: [PROJECT_STATUS.TESTING, PROJECT_STATUS.PLANNING, PROJECT_STATUS.CANCELLED],
  [PROJECT_STATUS.TESTING]: [PROJECT_STATUS.LAUNCHING, PROJECT_STATUS.DEVELOPING, PROJECT_STATUS.CANCELLED],
  [PROJECT_STATUS.LAUNCHING]: [PROJECT_STATUS.PROMOTING, PROJECT_STATUS.DECLINING, PROJECT_STATUS.MAINTAINING, PROJECT_STATUS.CANCELLED],
  [PROJECT_STATUS.PROMOTING]: [PROJECT_STATUS.PROFITING, PROJECT_STATUS.MAINTAINING, PROJECT_STATUS.DECLINING],
  [PROJECT_STATUS.PROFITING]: [PROJECT_STATUS.MAINTAINING, PROJECT_STATUS.DECLINING],
  [PROJECT_STATUS.MAINTAINING]: [PROJECT_STATUS.DECLINING, PROJECT_STATUS.COMPLETED],
  [PROJECT_STATUS.DECLINING]: [PROJECT_STATUS.MAINTAINING, PROJECT_STATUS.CANCELLED, PROJECT_STATUS.COMPLETED],
  [PROJECT_STATUS.COMPLETED]: [],
  [PROJECT_STATUS.CANCELLED]: [],
};

// ==================== 工具函数 ====================

// 项目状态相关
export function getStatusConfig(status: string): StatusConfig {
  return PROJECT_STATUS_CONFIG[status as ProjectStatus] || PROJECT_STATUS_CONFIG[PROJECT_STATUS.IDEATION];
}

export function getStatusLabel(status: string): string {
  const config = getStatusConfig(status);
  return `${config.icon} ${config.label}`;
}

export function getStatusOptions(): Array<{ value: ProjectStatus; label: string }> {
  return Object.values(PROJECT_STATUS).map(status => ({
    value: status,
    label: getStatusLabel(status)
  }));
}

export function getAvailableTransitions(currentStatus: string): ProjectStatus[] {
  return STATUS_TRANSITIONS[currentStatus as ProjectStatus] || [];
}

export function isValidTransition(from: string, to: string): boolean {
  const availableTransitions = getAvailableTransitions(from);
  return availableTransitions.includes(to as ProjectStatus);
}

// 项目类型相关
export function getTypeConfig(type: string): TypeConfig {
  return PROJECT_TYPE_CONFIG[type as ProjectType] || PROJECT_TYPE_CONFIG[PROJECT_TYPE.OTHER];
}

export function getTypeLabel(type: string): string {
  const config = getTypeConfig(type);
  return `${config.icon} ${config.label}`;
}

export function getTypeOptions(): Array<{ value: ProjectType; label: string }> {
  return Object.values(PROJECT_TYPE).map(type => ({
    value: type,
    label: getTypeLabel(type)
  }));
}

// 优先级相关
export function getPriorityConfig(priority: string): PriorityConfig {
  return PROJECT_PRIORITY_CONFIG[priority as ProjectPriority] || PROJECT_PRIORITY_CONFIG[PROJECT_PRIORITY.MEDIUM];
}

export function getPriorityLabel(priority: string): string {
  const config = getPriorityConfig(priority);
  return `${config.icon} ${config.label}`;
}

export function getPriorityOptions(): Array<{ value: ProjectPriority; label: string }> {
  return Object.values(PROJECT_PRIORITY).map(priority => ({
    value: priority,
    label: getPriorityLabel(priority)
  }));
}

// 成员角色相关
export function getRoleConfig(role: string): RoleConfig {
  return MEMBER_ROLE_CONFIG[role as MemberRole] || MEMBER_ROLE_CONFIG[MEMBER_ROLE.OTHER];
}

export function getRoleLabel(role: string): string {
  const config = getRoleConfig(role);
  return `${config.icon} ${config.label}`;
}

export function getRoleOptions(): Array<{ value: MemberRole; label: string }> {
  return Object.values(MEMBER_ROLE).map(role => ({
    value: role,
    label: getRoleLabel(role)
  }));
}

// 成员状态相关
export function getMemberStatusConfig(status: string): MemberStatusConfig {
  return MEMBER_STATUS_CONFIG[status as MemberStatus] || MEMBER_STATUS_CONFIG[MEMBER_STATUS.PENDING];
}

export function getMemberStatusLabel(status: string): string {
  const config = getMemberStatusConfig(status);
  return `${config.icon} ${config.label}`;
}

// 里程碑状态相关
export function getMilestoneStatusConfig(status: string): MilestoneStatusConfig {
  return MILESTONE_STATUS_CONFIG[status as MilestoneStatus] || MILESTONE_STATUS_CONFIG[MILESTONE_STATUS.PENDING];
}

export function getMilestoneStatusLabel(status: string): string {
  const config = getMilestoneStatusConfig(status);
  return `${config.icon} ${config.label}`;
}

// 分成类型相关
export function getShareTypeConfig(type: string): ShareTypeConfig {
  return SHARE_TYPE_CONFIG[type as ShareType] || SHARE_TYPE_CONFIG[SHARE_TYPE.REVENUE];
}

export function getShareTypeLabel(type: string): string {
  const config = getShareTypeConfig(type);
  return `${config.icon} ${config.label}`;
}

// ==================== 用于API的枚举数组 ====================

export const PROJECT_STATUS_VALUES = Object.values(PROJECT_STATUS);
export const PROJECT_TYPE_VALUES = Object.values(PROJECT_TYPE);
export const PROJECT_PRIORITY_VALUES = Object.values(PROJECT_PRIORITY);
export const MEMBER_ROLE_VALUES = Object.values(MEMBER_ROLE);
export const MEMBER_STATUS_VALUES = Object.values(MEMBER_STATUS);
export const MILESTONE_STATUS_VALUES = Object.values(MILESTONE_STATUS);
export const SHARE_TYPE_VALUES = Object.values(SHARE_TYPE);
