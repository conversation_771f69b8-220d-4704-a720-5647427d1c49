import { useProjectManagement } from "~/hooks/useProjectManagement";
import { getRoleConfig } from "~/config/project-management";

interface BadgeProps {
  children: React.ReactNode;
  variant?: "default" | "success" | "warning" | "error" | "info" | "purple" | "pink" | "secondary" | "outline" | "destructive";
  size?: "sm" | "md" | "lg";
  className?: string;
}

export function Badge({ 
  children, 
  variant = "default", 
  size = "md", 
  className = "" 
}: BadgeProps) {
  const variantStyles = {
    default: "bg-gray-100 text-gray-800",
    success: "bg-green-100 text-green-800",
    warning: "bg-yellow-100 text-yellow-800",
    error: "bg-red-100 text-red-800",
    info: "bg-blue-100 text-blue-800",
    purple: "bg-purple-100 text-purple-800",
    pink: "bg-pink-100 text-pink-800",
    secondary: "bg-gray-100 text-gray-800",
    outline: "border border-gray-300 bg-transparent text-gray-800",
    destructive: "bg-red-100 text-red-800",
  };

  const sizeStyles = {
    sm: "px-2 py-0.5 text-xs",
    md: "px-2.5 py-0.5 text-xs",
    lg: "px-3 py-1 text-sm",
  };

  return (
    <span className={`inline-flex items-center font-medium rounded-full ${variantStyles[variant]} ${sizeStyles[size]} ${className}`}>
      {children}
    </span>
  );
}

// 用户状态标签
export function UserStatusBadge({ status }: { status: string }) {
  const statusConfig = {
    pending: { label: "待审核", variant: "warning" as const },
    active: { label: "活跃", variant: "success" as const },
    inactive: { label: "非活跃", variant: "default" as const },
    rejected: { label: "已拒绝", variant: "error" as const },
  };

  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
  
  return (
    <Badge variant={config.variant}>
      {config.label}
    </Badge>
  );
}

// 用户角色标签
export function UserRoleBadge({ role }: { role: string }) {
  const roleConfig = {
    admin: { label: "超管", variant: "purple" as const },
    manager: { label: "经理", variant: "info" as const },
    member: { label: "成员", variant: "default" as const },
  };

  const config = roleConfig[role as keyof typeof roleConfig] || roleConfig.member;
  
  return (
    <Badge variant={config.variant}>
      {config.label}
    </Badge>
  );
}

// 项目状态标签
export function ProjectStatusBadge({ status }: { status: string }) {
  const { getStatusConfig } = useProjectManagement();
  const config = getStatusConfig(status);

  return (
    <Badge variant={config.variant}>
      {config.icon} {config.label}
    </Badge>
  );
}



// 项目成员角色标签 - 已废弃，请使用 MemberRoleBadge 组件
// 这个组件保留是为了向后兼容，建议使用 ~/components/project/MemberRoleSelect 中的 MemberRoleBadge
export function ProjectMemberRoleBadge({ role }: { role: string }) {
  const config = getRoleConfig(role);

  return (
    <Badge variant="default">
      {config.icon} {config.label}
    </Badge>
  );
}

// 分成类型标签
export function ShareTypeBadge({ shareType }: { shareType: string }) {
  const typeConfig = {
    equity: { label: "股权", variant: "purple" as const },
    revenue: { label: "收入", variant: "success" as const },
    bonus: { label: "奖金", variant: "warning" as const },
    commission: { label: "佣金", variant: "info" as const },
  };

  const config = typeConfig[shareType as keyof typeof typeConfig] || typeConfig.equity;
  
  return (
    <Badge variant={config.variant}>
      {config.label}
    </Badge>
  );
}

// 分成状态标签
export function ShareStatusBadge({ status }: { status: string }) {
  const statusConfig = {
    pending: { label: "待生效", variant: "warning" as const },
    active: { label: "生效中", variant: "success" as const },
    completed: { label: "已完成", variant: "info" as const },
    cancelled: { label: "已取消", variant: "error" as const },
  };

  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
  
  return (
    <Badge variant={config.variant}>
      {config.label}
    </Badge>
  );
}

// 优先级标签
export function PriorityBadge({ priority }: { priority: string }) {
  const priorityConfig = {
    low: { label: "低", variant: "default" as const },
    medium: { label: "中", variant: "info" as const },
    high: { label: "高", variant: "warning" as const },
    urgent: { label: "紧急", variant: "error" as const },
  };

  const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;
  
  return (
    <Badge variant={config.variant}>
      {config.label}
    </Badge>
  );
}

// 带图标的标签
interface IconBadgeProps extends BadgeProps {
  icon: React.ReactNode;
}

export function IconBadge({ icon, children, ...props }: IconBadgeProps) {
  return (
    <Badge {...props}>
      <span className="flex items-center space-x-1">
        <span className="w-3 h-3">{icon}</span>
        <span>{children}</span>
      </span>
    </Badge>
  );
}

// 可点击的标签
interface ClickableBadgeProps extends BadgeProps {
  onClick: () => void;
}

export function ClickableBadge({ onClick, children, className = "", ...props }: ClickableBadgeProps) {
  return (
    <button
      onClick={onClick}
      className={`transition-colors hover:opacity-80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${className}`}
    >
      <Badge {...props}>
        {children}
      </Badge>
    </button>
  );
}

// 可关闭的标签
interface DismissibleBadgeProps extends BadgeProps {
  onDismiss: () => void;
}

export function DismissibleBadge({ onDismiss, children, className = "", ...props }: DismissibleBadgeProps) {
  return (
    <Badge {...props} className={`pr-1 ${className}`}>
      <span className="flex items-center space-x-1">
        <span>{children}</span>
        <button
          onClick={onDismiss}
          className="flex-shrink-0 ml-1 h-3 w-3 rounded-full inline-flex items-center justify-center hover:bg-black hover:bg-opacity-10 focus:outline-none focus:bg-black focus:bg-opacity-10"
        >
          <svg className="h-2 w-2" stroke="currentColor" fill="none" viewBox="0 0 8 8">
            <path strokeLinecap="round" strokeWidth="1.5" d="m1 1 6 6m0-6L1 7" />
          </svg>
        </button>
      </span>
    </Badge>
  );
}
