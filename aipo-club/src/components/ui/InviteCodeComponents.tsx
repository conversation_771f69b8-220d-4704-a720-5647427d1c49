import React, { useState } from 'react';
import Image from 'next/image';
import { api } from '~/trpc/react';

interface InviteCodeUsage {
  id: string;
  usedAt: Date;
  user: {
    id: string;
    name: string;
  };
}

interface InviteCode {
  id: string;
  code: string;
  maxUses: number;
  usedCount: number;
  expiresAt: Date | null;
  isActive: boolean;
  description: string | null;
  createdAt: Date;
  usages?: InviteCodeUsage[];
}

interface InviteCodeGeneratorProps {
  onGenerated?: (inviteCode: InviteCode) => void;
}

export function InviteCodeGenerator({ onGenerated }: InviteCodeGeneratorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [formData, setFormData] = useState({
    maxUses: 1,
    expiresAt: '',
    description: '',
  });

  const generateMutation = api.user.generateInviteCode.useMutation({
    onSuccess: (data) => {
      setIsOpen(false);
      setFormData({ maxUses: 1, expiresAt: '', description: '' });
      onGenerated?.(data);
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    generateMutation.mutate({
      maxUses: formData.maxUses,
      expiresAt: formData.expiresAt ? new Date(formData.expiresAt) : undefined,
      description: formData.description || undefined,
    });
  };

  return (
    <>
      <button
        onClick={() => setIsOpen(true)}
        className="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700"
      >
        生成邀请码
      </button>

      {isOpen && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">生成邀请码</h3>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">最大使用次数</label>
                  <input
                    type="number"
                    min="1"
                    max="100"
                    value={formData.maxUses}
                    onChange={(e) => setFormData(prev => ({ ...prev, maxUses: parseInt(e.target.value) }))}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700">过期时间（可选）</label>
                  <input
                    type="datetime-local"
                    value={formData.expiresAt}
                    onChange={(e) => setFormData(prev => ({ ...prev, expiresAt: e.target.value }))}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700">描述（可选）</label>
                  <textarea
                    rows={3}
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="邀请码用途说明"
                  />
                </div>
                
                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setIsOpen(false)}
                    className="bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-300"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    disabled={generateMutation.isPending}
                    className="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700 disabled:opacity-50"
                  >
                    {generateMutation.isPending ? '生成中...' : '生成'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

interface InviteCodeCardProps {
  inviteCode: InviteCode;
  onCopy?: () => void;
}

export function InviteCodeCard({ inviteCode, onCopy }: InviteCodeCardProps) {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(inviteCode.code);
      setCopied(true);
      onCopy?.();
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('复制失败:', err);
    }
  };

  const isExpired = inviteCode.expiresAt && new Date(inviteCode.expiresAt) < new Date();
  const isExhausted = inviteCode.usedCount >= inviteCode.maxUses;
  const isInactive = !inviteCode.isActive || isExpired || isExhausted;

  return (
    <div className={`border rounded-lg p-4 ${isInactive ? 'bg-gray-50 border-gray-200' : 'bg-white border-gray-300'}`}>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          <code className={`px-2 py-1 rounded text-sm font-mono ${isInactive ? 'bg-gray-200 text-gray-500' : 'bg-indigo-100 text-indigo-800'}`}>
            {inviteCode.code}
          </code>
          <button
            onClick={handleCopy}
            className={`text-sm ${isInactive ? 'text-gray-400' : 'text-indigo-600 hover:text-indigo-500'}`}
            disabled={isInactive}
          >
            {copied ? '已复制' : '复制'}
          </button>
        </div>
        <div className="flex items-center space-x-2">
          {isInactive && (
            <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-600 rounded">
              {isExpired ? '已过期' : isExhausted ? '已用完' : '已停用'}
            </span>
          )}
          {!isInactive && (
            <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-600 rounded">
              有效
            </span>
          )}
        </div>
      </div>
      
      <div className="space-y-2 text-sm text-gray-600">
        <div className="flex justify-between">
          <span>使用次数:</span>
          <span>{inviteCode.usedCount} / {inviteCode.maxUses}</span>
        </div>
        {inviteCode.expiresAt && (
          <div className="flex justify-between">
            <span>过期时间:</span>
            <span>{new Date(inviteCode.expiresAt).toLocaleString('zh-CN')}</span>
          </div>
        )}
        <div className="flex justify-between">
          <span>创建时间:</span>
          <span>{new Date(inviteCode.createdAt).toLocaleString('zh-CN')}</span>
        </div>
        {inviteCode.description && (
          <div className="mt-2 p-2 bg-gray-50 rounded text-xs">
            {inviteCode.description}
          </div>
        )}
      </div>
      
      {inviteCode.usages && inviteCode.usages.length > 0 && (
        <div className="mt-3 pt-3 border-t border-gray-200">
          <h4 className="text-sm font-medium text-gray-700 mb-2">使用记录</h4>
          <div className="space-y-1">
            {inviteCode.usages.map((usage: InviteCodeUsage) => (
              <div key={usage.id} className="flex items-center justify-between text-xs text-gray-500">
                <span>{usage.user.name}</span>
                <span>{new Date(usage.usedAt).toLocaleDateString('zh-CN')}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

interface InviteeCardProps {
  invitee: any;
}

export function InviteeCard({ invitee }: InviteeCardProps) {
  return (
    <div className="border border-gray-200 rounded-lg p-4 bg-white">
      <div className="flex items-center space-x-3">
        <div className="flex-shrink-0">
          {invitee.avatar ? (
            <Image className="h-10 w-10 rounded-full" src={invitee.avatar} alt={invitee.name} width={40} height={40} />
          ) : (
            <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
              <span className="text-sm font-medium text-gray-700">
                {invitee.name.charAt(0).toUpperCase()}
              </span>
            </div>
          )}
        </div>
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-gray-900 truncate">
            {invitee.name}
          </p>
          <p className="text-sm text-gray-500 truncate">
            {invitee.email}
          </p>
        </div>
        <div className="flex flex-col items-end space-y-1">
          <span className={`px-2 py-1 text-xs font-medium rounded ${
            invitee.status === 'active' 
              ? 'bg-green-100 text-green-600' 
              : invitee.status === 'pending'
              ? 'bg-yellow-100 text-yellow-600'
              : 'bg-gray-100 text-gray-600'
          }`}>
            {invitee.status === 'active' ? '已激活' : 
             invitee.status === 'pending' ? '待审核' : '其他'}
          </span>
          <span className="text-xs text-gray-500">
            {new Date(invitee.createdAt).toLocaleDateString('zh-CN')}
          </span>
        </div>
      </div>
    </div>
  );
}
