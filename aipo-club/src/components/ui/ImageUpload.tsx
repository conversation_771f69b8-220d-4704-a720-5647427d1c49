"use client";

import React, { useState, useRef } from 'react';
import Image from 'next/image';
import { 
  uploadToOSS, 
  validateImageFile, 
  buildImageUrl, 
  buildThumbnailUrl,
  getOSSConfig,
  type UploadResult 
} from '~/lib/oss';

interface ImageUploadProps {
  value?: string; // 当前图片URL
  onChange: (url: string) => void; // 上传成功回调
  onError?: (error: string) => void; // 错误回调
  placeholder?: string; // 占位符文本
  className?: string;
  // 上传配置
  prefix?: string; // 文件路径前缀
  compress?: boolean; // 是否压缩
  maxWidth?: number; // 最大宽度
  quality?: number; // 压缩质量
  // 显示配置
  previewSize?: number; // 预览图尺寸
  showFileName?: boolean; // 是否显示文件名
  disabled?: boolean; // 是否禁用
}

export function ImageUpload({
  value,
  onChange,
  onError,
  placeholder = "点击上传图片",
  className = "",
  prefix = "images",
  compress = true,
  maxWidth = 800,
  quality = 0.8,
  previewSize = 80,
  showFileName = false,
  disabled = false,
}: ImageUploadProps) {
  const [uploading, setUploading] = useState(false);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (file: File) => {
    if (disabled) return;

    // 验证文件
    const validation = validateImageFile(file);
    if (!validation.valid) {
      onError?.(validation.error || '文件验证失败');
      return;
    }

    setUploading(true);

    void (async () => {
      try {
        const config = getOSSConfig();
        const result: UploadResult = await uploadToOSS(file, config, {
          prefix,
          compress,
          maxWidth,
          quality,
        });

        if (result.success && result.url) {
          onChange(result.url);
        } else {
          onError?.(result.error || '上传失败');
        }
      } catch (error) {
        onError?.(error instanceof Error ? error.message : '上传失败');
      } finally {
        setUploading(false);
      }
    })();
  };

  const handleClick = () => {
    if (disabled || uploading) return;
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
    // 清空input值，允许重复选择同一文件
    event.target.value = '';
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    if (!disabled && !uploading) {
      setDragOver(true);
    }
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(false);

    if (disabled || uploading) return;

    const files = Array.from(event.dataTransfer.files);
    const imageFile = files.find(file => file.type.startsWith('image/'));

    if (imageFile) {
      handleFileSelect(imageFile);
    } else {
      onError?.('请选择图片文件');
    }
  };

  const handleRemove = () => {
    if (disabled) return;
    onChange('');
  };

  return (
    <div className={`relative ${className}`}>
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileChange}
        className="hidden"
        disabled={disabled || uploading}
      />

      {value ? (
        // 已有图片的预览
        <div className="relative group">
          <div 
            className={`relative overflow-hidden rounded-lg border-2 border-gray-200 bg-gray-50`}
            style={{ width: previewSize, height: previewSize }}
          >
            <Image
              src={buildThumbnailUrl(value, previewSize, previewSize)}
              alt="预览图"
              fill
              className="object-cover"
              onError={() => {
                // 如果缩略图加载失败，尝试加载原图
                const img = document.querySelector(`img[src*="${value}"]`) as HTMLImageElement;
                if (img) {
                  img.src = buildImageUrl(value);
                }
              }}
            />
          </div>

          {/* 操作按钮 */}
          <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg flex items-center justify-center space-x-2">
            <button
              type="button"
              onClick={handleClick}
              disabled={disabled || uploading}
              className="p-2 bg-white rounded-full text-gray-600 hover:text-gray-800 transition-colors"
              title="更换图片"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </button>
            <button
              type="button"
              onClick={handleRemove}
              disabled={disabled}
              className="p-2 bg-white rounded-full text-red-600 hover:text-red-800 transition-colors"
              title="删除图片"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
          </div>

          {showFileName && value && (
            <p className="mt-2 text-xs text-gray-500 truncate">
              {value.split('/').pop()}
            </p>
          )}
        </div>
      ) : (
        // 上传区域
        <div
          onClick={handleClick}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          className={`
            relative cursor-pointer border-2 border-dashed rounded-lg transition-colors duration-200
            ${dragOver 
              ? 'border-blue-400 bg-blue-50' 
              : 'border-gray-300 hover:border-gray-400'
            }
            ${disabled ? 'cursor-not-allowed opacity-50' : ''}
            ${uploading ? 'cursor-wait' : ''}
          `}
          style={{ width: previewSize, height: previewSize }}
        >
          <div className="flex flex-col items-center justify-center h-full p-2">
            {uploading ? (
              <>
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <p className="mt-2 text-xs text-gray-500 text-center">上传中...</p>
              </>
            ) : (
              <>
                <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <p className="mt-2 text-xs text-gray-500 text-center">{placeholder}</p>
              </>
            )}
          </div>
        </div>
      )}

      {/* 上传提示 */}
      <p className="mt-1 text-xs text-gray-500">
        支持 JPG、PNG、GIF、WebP 格式，最大 5MB
      </p>
    </div>
  );
}

// 头像上传组件（圆形预览）
export function AvatarUpload(props: Omit<ImageUploadProps, 'previewSize'>) {
  const { value, onChange, onError, disabled } = props;

  return (
    <div className="flex flex-col items-center">
      <div className="relative">
        {value ? (
          // 已有头像的预览
          <div className="relative group">
            <div className="relative w-20 h-20 rounded-full overflow-hidden border-2 border-gray-200 bg-gray-50">
              <Image
                src={buildThumbnailUrl(value, 80, 80)}
                alt="用户头像"
                fill
                className="object-cover"
                onError={() => {
                  // 如果缩略图加载失败，尝试加载原图
                  const img = document.querySelector(`img[src*="${value}"]`) as HTMLImageElement;
                  if (img) {
                    img.src = buildImageUrl(value);
                  }
                }}
              />
            </div>

            {/* 操作按钮 */}
            <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-full flex items-center justify-center space-x-1">
              <button
                type="button"
                onClick={() => {
                  const input = document.createElement('input');
                  input.type = 'file';
                  input.accept = 'image/*';
                  input.onchange = (e) => {
                    const file = (e.target as HTMLInputElement).files?.[0];
                    if (file) {
                      void handleFileUpload(file, onChange, onError);
                    }
                  };
                  input.click();
                }}
                disabled={disabled}
                className="p-1.5 bg-white rounded-full text-gray-600 hover:text-gray-800 transition-colors"
                title="更换头像"
              >
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </button>
              <button
                type="button"
                onClick={() => onChange('')}
                disabled={disabled}
                className="p-1.5 bg-white rounded-full text-red-600 hover:text-red-800 transition-colors"
                title="删除头像"
              >
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>
            </div>
          </div>
        ) : (
          // 上传区域
          <div
            onClick={() => {
              const input = document.createElement('input');
              input.type = 'file';
              input.accept = 'image/*';
              input.onchange = (e) => {
                const file = (e.target as HTMLInputElement).files?.[0];
                if (file) {
                  void handleFileUpload(file, onChange, onError);
                }
              };
              input.click();
            }}
            className={`
              relative cursor-pointer border-2 border-dashed rounded-full transition-colors duration-200 w-20 h-20
              ${disabled ? 'cursor-not-allowed opacity-50' : 'border-gray-300 hover:border-gray-400'}
            `}
          >
            <div className="flex flex-col items-center justify-center h-full p-2">
              <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <p className="mt-1 text-xs text-gray-500 text-center">上传头像</p>
            </div>
          </div>
        )}
      </div>

      {/* 上传提示 */}
      <p className="mt-2 text-xs text-gray-500 text-center">
        支持 JPG、PNG、GIF、WebP<br/>最大 5MB
      </p>
    </div>
  );
}

// 头像上传处理函数
async function handleFileUpload(
  file: File,
  onChange: (url: string) => void,
  onError?: (error: string) => void
) {
  // 验证文件
  const validation = validateImageFile(file);
  if (!validation.valid) {
    onError?.(validation.error || '文件验证失败');
    return;
  }

  try {
    // 直接调用上传API，不依赖OSS配置
    const result = await uploadToOSS(file, getOSSConfig(), {
      prefix: 'avatars',
      compress: true,
      maxWidth: 400,
      quality: 0.8,
    });

    if (result.success && result.url) {
      onChange(result.url);
    } else {
      onError?.(result.error || '上传失败');
    }
  } catch (error) {
    onError?.(error instanceof Error ? error.message : '上传失败');
  }
}

// 项目Logo上传组件
export function ProjectLogoUpload(props: Omit<ImageUploadProps, 'previewSize' | 'prefix'>) {
  return (
    <ImageUpload
      {...props}
      previewSize={100}
      prefix="project-logos"
      placeholder="上传项目Logo"
    />
  );
}
