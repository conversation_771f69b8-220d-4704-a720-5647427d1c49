import React from 'react';
import Image from 'next/image';
import { 
  getUserLevelConfig, 
  getSubscriptionStatusConfig,
  getSubscriptionDaysRemaining,
  isSubscriptionExpiringSoon,
  type UserLevel,
  type SubscriptionStatus,
  type User
} from '~/types/user';

interface UserLevelBadgeProps {
  level: UserLevel;
  className?: string;
}

export function UserLevelBadge({ level, className = "" }: UserLevelBadgeProps) {
  const config = getUserLevelConfig(level);
  
  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color} ${className}`}>
      <span className="mr-1">{config.icon}</span>
      {config.label}
    </span>
  );
}

interface SubscriptionStatusBadgeProps {
  status: SubscriptionStatus;
  className?: string;
}

export function SubscriptionStatusBadge({ status, className = "" }: SubscriptionStatusBadgeProps) {
  const config = getSubscriptionStatusConfig(status);
  
  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color} ${className}`}>
      {config.label}
    </span>
  );
}

interface SubscriptionCountdownProps {
  user: User;
  className?: string;
}

export function SubscriptionCountdown({ user, className = "" }: SubscriptionCountdownProps) {
  if (!user.subscriptionExpiry) {
    return null;
  }

  const daysRemaining = getSubscriptionDaysRemaining(user);
  const isExpiringSoon = isSubscriptionExpiringSoon(user);
  
  if (daysRemaining === null || daysRemaining <= 0) {
    return null;
  }

  return (
    <div className={`text-sm ${isExpiringSoon ? 'text-orange-600' : 'text-gray-600'} ${className}`}>
      {isExpiringSoon && (
        <span className="inline-flex items-center">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        </span>
      )}
      订阅剩余 {daysRemaining} 天
    </div>
  );
}

interface UserInfoCardProps {
  user: User;
  showSubscription?: boolean;
  className?: string;
}

export function UserInfoCard({ user, showSubscription = true, className = "" }: UserInfoCardProps) {
  return (
    <div className={`bg-white rounded-lg border p-4 ${className}`}>
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            {user.avatar ? (
              <Image className="h-10 w-10 rounded-full" src={user.avatar} alt={user.name} width={40} height={40} />
            ) : (
              <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                <span className="text-sm font-medium text-gray-700">
                  {user.name.charAt(0).toUpperCase()}
                </span>
              </div>
            )}
          </div>
          <div className="min-w-0 flex-1">
            <p className="text-sm font-medium text-gray-900 truncate">
              {user.name}
            </p>
            <p className="text-sm text-gray-500 truncate">
              {user.email}
            </p>
          </div>
        </div>
        <div className="flex flex-col items-end space-y-2">
          <UserLevelBadge level={user.level} />
          {showSubscription && (
            <SubscriptionStatusBadge status={user.subscriptionStatus} />
          )}
        </div>
      </div>
      {showSubscription && (
        <div className="mt-3">
          <SubscriptionCountdown user={user} />
        </div>
      )}
    </div>
  );
}
