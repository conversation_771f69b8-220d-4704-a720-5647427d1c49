/**
 * 成员角色选择组件
 * 统一的成员角色下拉选择器，使用配置中心的数据
 */

import React from 'react';
import { useProjectManagement } from '~/hooks/useProjectManagement';
import type { MemberRole } from '~/config/project-management';

interface MemberRoleSelectProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  allowedRoles?: MemberRole[];
  name?: string;
  id?: string;
  showIcons?: boolean;
}

export function MemberRoleSelect({
  value,
  onChange,
  placeholder = "选择角色",
  className = "",
  disabled = false,
  allowedRoles,
  name,
  id,
  showIcons = true
}: MemberRoleSelectProps) {
  const { roleOptions } = useProjectManagement();

  const selectOptions = allowedRoles
    ? roleOptions.filter(opt => allowedRoles.includes(opt.value))
    : roleOptions;

  return (
    <select
      id={id}
      name={name}
      value={value}
      onChange={(e) => onChange(e.target.value)}
      disabled={disabled}
      className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 ${className}`}
    >
      <option value="">{placeholder}</option>
      {selectOptions.map(option => (
        <option key={option.value} value={option.value}>
          {showIcons ? option.label : option.label.replace(/^[^\s]+ /, '')}
        </option>
      ))}
    </select>
  );
}

// 简化版本，直接返回选项数组，用于其他场景
export function useMemberRoleOptions() {
  const { roleOptions } = useProjectManagement();
  return roleOptions;
}

// 角色徽章组件
export function MemberRoleBadge({ role, className = "" }: { role: string; className?: string }) {
  const { getRoleConfig } = useProjectManagement();
  const config = getRoleConfig(role);
  
  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color} ${className}`}>
      {config.icon} {config.label}
    </span>
  );
}

export default MemberRoleSelect;
