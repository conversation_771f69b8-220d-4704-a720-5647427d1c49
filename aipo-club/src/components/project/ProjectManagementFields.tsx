"use client";

import React from 'react';
import Image from 'next/image';
import {
  getProjectHealthStatusConfig,
  isValidUrl,
  isValidVersion,
  formatTechStack,
  PROJECT_HEALTH_STATUS,
} from '~/types/project';
import { buildImageUrl, buildThumbnailUrl } from '~/lib/oss';

interface ProjectManagementFieldsProps {
  project: {
    logo?: string | null;
    visitUrl?: string | null;
    wikiUrl?: string | null;
    gitUrl?: string | null;
    healthStatus?: string;
    version?: string;
    techStack?: string | null;
    lastDeployAt?: Date | null;
  };
  showLabels?: boolean;
  className?: string;
}

export function ProjectManagementFields({ 
  project, 
  showLabels = true, 
  className = "" 
}: ProjectManagementFieldsProps) {
  const healthConfig = getProjectHealthStatusConfig(project.healthStatus || 'healthy');
  const formattedTechStack = formatTechStack(project.techStack ?? null);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 项目链接 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {project.logo && (
          <div>
            {showLabels && (
              <label className="block text-sm font-medium text-gray-700 mb-1">
                项目Logo
              </label>
            )}
            <div className="flex items-center space-x-3">
              <div className="relative w-12 h-12 rounded-lg overflow-hidden bg-gray-100 border border-gray-200">
                <Image
                  src={buildThumbnailUrl(project.logo, 48, 48)}
                  alt="项目Logo"
                  fill
                  className="object-cover"
                  onError={(e) => {
                    // 如果缩略图加载失败，尝试加载原图
                    const target = e.target as HTMLImageElement;
                    target.src = buildImageUrl(project.logo!);
                  }}
                />
              </div>
              <div className="flex-1 min-w-0">
                <a
                  href={buildImageUrl(project.logo)}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-blue-600 hover:text-blue-800 text-sm font-medium"
                >
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  查看Logo
                </a>
                <p className="text-xs text-gray-500 mt-1 truncate">
                  {project.logo.split('/').pop()}
                </p>
              </div>
            </div>
          </div>
        )}

        {project.visitUrl && isValidUrl(project.visitUrl) && (
          <div>
            {showLabels && (
              <label className="block text-sm font-medium text-gray-700 mb-1">
                访问地址
              </label>
            )}
            <a 
              href={project.visitUrl} 
              target="_blank" 
              rel="noopener noreferrer"
              className="inline-flex items-center text-blue-600 hover:text-blue-800 text-sm"
            >
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
              </svg>
              访问项目
            </a>
          </div>
        )}

        {project.wikiUrl && isValidUrl(project.wikiUrl) && (
          <div>
            {showLabels && (
              <label className="block text-sm font-medium text-gray-700 mb-1">
                知识库
              </label>
            )}
            <a 
              href={project.wikiUrl} 
              target="_blank" 
              rel="noopener noreferrer"
              className="inline-flex items-center text-blue-600 hover:text-blue-800 text-sm"
            >
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
              查看文档
            </a>
          </div>
        )}

        {project.gitUrl && isValidUrl(project.gitUrl) && (
          <div>
            {showLabels && (
              <label className="block text-sm font-medium text-gray-700 mb-1">
                代码仓库
              </label>
            )}
            <a 
              href={project.gitUrl} 
              target="_blank" 
              rel="noopener noreferrer"
              className="inline-flex items-center text-blue-600 hover:text-blue-800 text-sm"
            >
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
              </svg>
              查看代码
            </a>
          </div>
        )}
      </div>

      {/* 项目状态信息 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          {showLabels && (
            <label className="block text-sm font-medium text-gray-700 mb-1">
              健康状态
            </label>
          )}
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${healthConfig.color}`}>
            <span className="mr-1">{healthConfig.icon}</span>
            {healthConfig.label}
          </span>
        </div>

        {project.version && (
          <div>
            {showLabels && (
              <label className="block text-sm font-medium text-gray-700 mb-1">
                版本号
              </label>
            )}
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              v{project.version}
            </span>
          </div>
        )}

        {project.lastDeployAt && (
          <div>
            {showLabels && (
              <label className="block text-sm font-medium text-gray-700 mb-1">
                最后部署
              </label>
            )}
            <span className="text-sm text-gray-600">
              {new Date(project.lastDeployAt).toLocaleDateString('zh-CN')}
            </span>
          </div>
        )}
      </div>

      {/* 技术栈 */}
      {formattedTechStack && (
        <div>
          {showLabels && (
            <label className="block text-sm font-medium text-gray-700 mb-1">
              技术栈
            </label>
          )}
          <div className="flex flex-wrap gap-1">
            {formattedTechStack.split(',').map((tech, index) => (
              <span 
                key={index}
                className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800"
              >
                {tech.trim()}
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

// 健康状态选择器组件
interface HealthStatusSelectorProps {
  value: string;
  onChange: (value: string) => void;
  className?: string;
}

export function HealthStatusSelector({ value, onChange, className = "" }: HealthStatusSelectorProps) {
  return (
    <select
      value={value}
      onChange={(e) => onChange(e.target.value)}
      className={`appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm ${className}`}
    >
      {Object.entries(PROJECT_HEALTH_STATUS).map(([key, status]) => {
        const config = getProjectHealthStatusConfig(status);
        return (
          <option key={key} value={status}>
            {config.icon} {config.label}
          </option>
        );
      })}
    </select>
  );
}

// 版本号验证输入组件
interface VersionInputProps {
  value: string;
  onChange: (value: string) => void;
  className?: string;
  placeholder?: string;
}

export function VersionInput({ value, onChange, className = "", placeholder = "1.0.0" }: VersionInputProps) {
  const [error, setError] = React.useState<string>("");

  const handleChange = (newValue: string) => {
    onChange(newValue);
    if (newValue && !isValidVersion(newValue)) {
      setError("请输入有效的版本号（如：1.0.0）");
    } else {
      setError("");
    }
  };

  return (
    <div>
      <input
        type="text"
        value={value}
        onChange={(e) => handleChange(e.target.value)}
        placeholder={placeholder}
        className={`appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm ${className}`}
      />
      {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
    </div>
  );
}
