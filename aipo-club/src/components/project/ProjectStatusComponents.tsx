/**
 * 项目状态相关组件
 * 
 * 提供统一的状态显示和交互组件
 * 所有状态相关的UI都通过这些组件实现
 */

import React from 'react';
import { useProjectManagement, type ProjectStatus, type ProjectPriority } from '~/hooks/useProjectManagement';

// 状态徽章组件
interface ProjectStatusBadgeProps {
  status: string;
  className?: string;
  showIcon?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export function ProjectStatusBadge({ 
  status, 
  className = '', 
  showIcon = true,
  size = 'md' 
}: ProjectStatusBadgeProps) {
  const { getStatusConfig } = useProjectManagement();
  const config = getStatusConfig(status);
  
  const sizeClasses = {
    sm: 'px-2 py-0.5 text-xs',
    md: 'px-2.5 py-0.5 text-xs',
    lg: 'px-3 py-1 text-sm'
  };
  
  return (
    <span 
      className={`inline-flex items-center rounded-full font-medium ${config.color} ${sizeClasses[size]} ${className}`}
      title={config.description}
    >
      {showIcon && config.icon} {config.label}
    </span>
  );
}

// 状态选择器组件
interface ProjectStatusSelectProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  allowedStatuses?: ProjectStatus[];
  showTransitions?: boolean;
  currentStatus?: string;
}

export function ProjectStatusSelect({
  value,
  onChange,
  placeholder = "选择状态",
  className = "",
  disabled = false,
  allowedStatuses,
  showTransitions = false,
  currentStatus
}: ProjectStatusSelectProps) {
  const { statusOptions, getAvailableTransitions } = useProjectManagement();
  
  const selectOptions = allowedStatuses 
    ? statusOptions.filter(opt => allowedStatuses.includes(opt.value))
    : statusOptions;
  
  const transitions = showTransitions && currentStatus
    ? getAvailableTransitions(currentStatus)
    : [];
    
  return (
    <div>
      <select
        value={value}
        onChange={(e) => onChange(e.target.value)}
        disabled={disabled}
        className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 ${className}`}
      >
        <option value="">{placeholder}</option>
        {selectOptions.map(option => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
      
      {showTransitions && transitions.length > 0 && (
        <div className="mt-2">
          <p className="text-sm text-gray-600 mb-1">建议下一步：</p>
          <div className="flex flex-wrap gap-1">
            {transitions.map(status => (
              <button
                  key={status}
                  onClick={() => onChange(status)}
                  className="cursor-pointer hover:opacity-80"
                >
                  <ProjectStatusBadge
                    status={status}
                    size="sm"
                  />
                </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

// 状态筛选器组件
interface ProjectStatusFilterProps {
  value: string;
  onChange: (value: string) => void;
  className?: string;
  showAll?: boolean;
  allowedStatuses?: ProjectStatus[];
}

export function ProjectStatusFilter({
  value,
  onChange,
  className = "",
  showAll = true,
  allowedStatuses
}: ProjectStatusFilterProps) {
  const { statusOptions } = useProjectManagement();
  
  const filterOptions = allowedStatuses 
    ? statusOptions.filter(opt => allowedStatuses.includes(opt.value))
    : statusOptions;
    
  return (
    <select
      value={value}
      onChange={(e) => onChange(e.target.value)}
      className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 ${className}`}
    >
      {showAll && <option value="">全部状态</option>}
      {filterOptions.map(option => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  );
}

// 状态进度指示器组件
interface ProjectStatusProgressProps {
  currentStatus: string;
  className?: string;
}

export function ProjectStatusProgress({ currentStatus, className = "" }: ProjectStatusProgressProps) {
  
  // 定义状态顺序（用于进度计算）
  const statusOrder = [
    'ideation', 'planning', 'developing', 'testing', 
    'launching', 'promoting', 'profiting', 'maintaining'
  ];
  
  const currentIndex = statusOrder.indexOf(currentStatus);
  const progress = currentIndex >= 0 ? ((currentIndex + 1) / statusOrder.length) * 100 : 0;
  
  return (
    <div className={`w-full ${className}`}>
      <div className="flex justify-between text-xs text-gray-600 mb-1">
        <span>项目进度</span>
        <span>{Math.round(progress)}%</span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
          style={{ width: `${progress}%` }}
        />
      </div>
      <div className="mt-1">
        <ProjectStatusBadge status={currentStatus} size="sm" />
      </div>
    </div>
  );
}

// 状态历史组件
interface ProjectStatusHistoryProps {
  history: Array<{
    status: string;
    timestamp: Date;
    user?: string;
  }>;
  className?: string;
}

export function ProjectStatusHistory({ history, className = "" }: ProjectStatusHistoryProps) {
  const { getStatusConfig } = useProjectManagement();
  
  return (
    <div className={`space-y-2 ${className}`}>
      <h4 className="text-sm font-medium text-gray-900">状态历史</h4>
      <div className="space-y-1">
        {history.map((item, index) => {
          const config = getStatusConfig(item.status);
          return (
            <div key={index} className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-2">
                <span>{config.icon}</span>
                <span>{config.label}</span>
                {item.user && <span className="text-gray-500">by {item.user}</span>}
              </div>
              <span className="text-gray-400">
                {item.timestamp.toLocaleDateString()}
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
}

// 优先级徽章组件
interface ProjectPriorityBadgeProps {
  priority: string;
  className?: string;
  showIcon?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export function ProjectPriorityBadge({
  priority,
  className = '',
  showIcon = true,
  size = 'md'
}: ProjectPriorityBadgeProps) {
  const { getPriorityConfig } = useProjectManagement();
  const config = getPriorityConfig(priority);

  const sizeClasses = {
    sm: 'px-2 py-0.5 text-xs',
    md: 'px-2.5 py-0.5 text-xs',
    lg: 'px-3 py-1 text-sm'
  };

  return (
    <span
      className={`inline-flex items-center rounded-full font-medium ${config.color} ${sizeClasses[size]} ${className}`}
      title={config.description}
    >
      {showIcon && config.icon} {config.label}
    </span>
  );
}

// 优先级选择器组件
interface ProjectPrioritySelectProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  allowedPriorities?: ProjectPriority[];
}

export function ProjectPrioritySelect({
  value,
  onChange,
  placeholder = "选择优先级",
  className = "",
  disabled = false,
  allowedPriorities
}: ProjectPrioritySelectProps) {
  const { priorityOptions } = useProjectManagement();

  const selectOptions = allowedPriorities
    ? priorityOptions.filter(opt => allowedPriorities.includes(opt.value))
    : priorityOptions;

  return (
    <select
      value={value}
      onChange={(e) => onChange(e.target.value)}
      disabled={disabled}
      className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 ${className}`}
    >
      <option value="">{placeholder}</option>
      {selectOptions.map(option => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  );
}
