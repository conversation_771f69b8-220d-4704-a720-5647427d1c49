"use client";

import React, { useState } from 'react';
import { api } from '~/trpc/react';

interface Milestone {
  id: string;
  name: string;
  description?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  dueDate?: Date;
  completedAt?: Date;
  createdBy: {
    id: string;
    name: string;
    avatar?: string;
  };
  assignedTo?: {
    id: string;
    name: string;
    avatar?: string;
  };
  createdAt: Date;
}

interface MilestoneManagerProps {
  projectId: string;
}

const PriorityBadge = ({ priority }: { priority: string }) => {
  const priorityConfig = {
    low: { label: "低", color: "bg-gray-100 text-gray-800" },
    medium: { label: "中", color: "bg-blue-100 text-blue-800" },
    high: { label: "高", color: "bg-orange-100 text-orange-800" },
    urgent: { label: "紧急", color: "bg-red-100 text-red-800" }
  };

  const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;
  
  return (
    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
      {config.label}
    </span>
  );
};

const StatusBadge = ({ status }: { status: string }) => {
  const statusConfig = {
    pending: { label: "待开始", color: "bg-gray-100 text-gray-800" },
    in_progress: { label: "进行中", color: "bg-blue-100 text-blue-800" },
    completed: { label: "已完成", color: "bg-green-100 text-green-800" },
    cancelled: { label: "已取消", color: "bg-red-100 text-red-800" }
  };

  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
  
  return (
    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
      {config.label}
    </span>
  );
};

const CreateMilestoneForm = ({ 
  projectId, 
  onSuccess, 
  onCancel 
}: { 
  projectId: string; 
  onSuccess: () => void; 
  onCancel: () => void; 
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    priority: 'medium' as const,
    dueDate: '',
    assignedToId: ''
  });

  const createMilestone = api.project.createMilestone.useMutation({
    onSuccess: () => {
      onSuccess();
      setFormData({
        name: '',
        description: '',
        priority: 'medium',
        dueDate: '',
        assignedToId: ''
      });
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    createMilestone.mutate({
      projectId,
      name: formData.name,
      description: formData.description || undefined,
      priority: formData.priority,
      dueDate: formData.dueDate ? new Date(formData.dueDate) : undefined,
      assignedToId: formData.assignedToId || undefined,
    });
  };

  return (
    <form onSubmit={handleSubmit} className="bg-white border border-gray-200 rounded-lg p-4 mb-4">
      <h3 className="text-lg font-medium text-gray-900 mb-4">创建里程碑</h3>
      
      <div className="grid grid-cols-1 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            里程碑名称 *
          </label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            描述
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              优先级
            </label>
            <select
              value={formData.priority}
              onChange={(e) => setFormData({ ...formData, priority: e.target.value as any })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            >
              <option value="low">低</option>
              <option value="medium">中</option>
              <option value="high">高</option>
              <option value="urgent">紧急</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              截止时间
            </label>
            <input
              type="date"
              value={formData.dueDate}
              onChange={(e) => setFormData({ ...formData, dueDate: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
        </div>
      </div>

      <div className="flex justify-end space-x-3 mt-4">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
        >
          取消
        </button>
        <button
          type="submit"
          disabled={createMilestone.isPending}
          className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 disabled:opacity-50"
        >
          {createMilestone.isPending ? '创建中...' : '创建'}
        </button>
      </div>
    </form>
  );
};

const MilestoneCard = ({ 
  milestone, 
  onStatusUpdate 
}: { 
  milestone: Milestone; 
  onStatusUpdate: (id: string, status: string) => void; 
}) => {
  const isOverdue = milestone.dueDate && new Date(milestone.dueDate) < new Date() && milestone.status !== 'completed';

  return (
    <div className={`bg-white border rounded-lg p-4 ${isOverdue ? 'border-red-200 bg-red-50' : 'border-gray-200'}`}>
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1">
          <h4 className="text-lg font-medium text-gray-900">{milestone.name}</h4>
          {milestone.description && (
            <p className="text-sm text-gray-600 mt-1">{milestone.description}</p>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <PriorityBadge priority={milestone.priority} />
          <StatusBadge status={milestone.status} />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 mb-3">
        <div>
          <span className="font-medium">创建者:</span> {milestone.createdBy.name}
        </div>
        {milestone.assignedTo && (
          <div>
            <span className="font-medium">负责人:</span> {milestone.assignedTo.name}
          </div>
        )}
        {milestone.dueDate && (
          <div className={isOverdue ? 'text-red-600' : ''}>
            <span className="font-medium">截止时间:</span> {new Date(milestone.dueDate).toLocaleDateString()}
            {isOverdue && <span className="ml-1 text-red-500">(已逾期)</span>}
          </div>
        )}
        {milestone.completedAt && (
          <div>
            <span className="font-medium">完成时间:</span> {new Date(milestone.completedAt).toLocaleDateString()}
          </div>
        )}
      </div>

      {milestone.status !== 'completed' && milestone.status !== 'cancelled' && (
        <div className="flex space-x-2">
          {milestone.status === 'pending' && (
            <button
              onClick={() => onStatusUpdate(milestone.id, 'in_progress')}
              className="px-3 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded hover:bg-blue-200"
            >
              开始
            </button>
          )}
          {milestone.status === 'in_progress' && (
            <button
              onClick={() => onStatusUpdate(milestone.id, 'completed')}
              className="px-3 py-1 text-xs font-medium text-green-700 bg-green-100 rounded hover:bg-green-200"
            >
              完成
            </button>
          )}
          <button
            onClick={() => onStatusUpdate(milestone.id, 'cancelled')}
            className="px-3 py-1 text-xs font-medium text-red-700 bg-red-100 rounded hover:bg-red-200"
          >
            取消
          </button>
        </div>
      )}
    </div>
  );
};

export const MilestoneManager: React.FC<MilestoneManagerProps> = ({ projectId }) => {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string>('');

  const { data: milestones, refetch } = api.project.getMilestones.useQuery({
    projectId,
    status: (statusFilter as "pending" | "cancelled" | "completed" | "in_progress" | undefined) || undefined,
  });

  const updateMilestoneStatus = api.project.updateMilestoneStatus.useMutation({
    onSuccess: () => {
      void refetch();
    },
  });

  const handleStatusUpdate = (id: string, status: string) => {
    updateMilestoneStatus.mutate({ id, status: status as any });
  };

  const handleCreateSuccess = () => {
    setShowCreateForm(false);
    void refetch();
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">项目里程碑</h2>
        <div className="flex items-center space-x-3">
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
          >
            <option value="">全部状态</option>
            <option value="pending">待开始</option>
            <option value="in_progress">进行中</option>
            <option value="completed">已完成</option>
            <option value="cancelled">已取消</option>
          </select>
          <button
            onClick={() => setShowCreateForm(true)}
            className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700"
          >
            创建里程碑
          </button>
        </div>
      </div>

      {showCreateForm && (
        <CreateMilestoneForm
          projectId={projectId}
          onSuccess={handleCreateSuccess}
          onCancel={() => setShowCreateForm(false)}
        />
      )}

      <div className="space-y-3">
        {milestones && milestones.length > 0 ? (
          milestones.map((milestone: any) => (
            <MilestoneCard
              key={milestone.id}
              milestone={milestone}
              onStatusUpdate={handleStatusUpdate}
            />
          ))
        ) : (
          <div className="text-center py-8 text-gray-500">
            {statusFilter ? '没有符合条件的里程碑' : '暂无里程碑'}
          </div>
        )}
      </div>
    </div>
  );
};

export default MilestoneManager;
