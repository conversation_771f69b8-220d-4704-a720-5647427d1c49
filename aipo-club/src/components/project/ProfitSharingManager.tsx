"use client";

import React, { useState } from 'react';
import Image from 'next/image';
import { api } from '~/trpc/react';
import { useProjectManagement } from '~/hooks/useProjectManagement';

interface ProjectMember {
  id: string;
  role: string;
  status: string;
  profitRate: number | null;
  joinedAt: Date;
  user: {
    id: string;
    name: string;
    avatar: string | null;
    email: string;
  };
}

interface ProjectShare {
  id: string;
  shareType: string;
  percentage: number;
  amount: number | null;
  status: string;
  period: string;
  startDate: Date;
  endDate: Date | null;
  user: {
    id: string;
    name: string;
    avatar: string | null;
  };
}

interface ProfitSharingManagerProps {
  projectId: string;
  members: ProjectMember[];
  shares: ProjectShare[];
  currentMonthRevenue: number;
  totalRevenue: number;
}

const RoleBadge = ({ role }: { role: string }) => {
  const { getRoleConfig } = useProjectManagement();
  const config = getRoleConfig(role);

  return (
    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
      {config.icon} {config.label}
    </span>
  );
};

const ShareTypeBadge = ({ shareType }: { shareType: string }) => {
  const typeConfig = {
    equity: { label: "股权分成", color: "bg-blue-100 text-blue-800" },
    revenue: { label: "收益分成", color: "bg-green-100 text-green-800" },
    bonus: { label: "奖金分成", color: "bg-yellow-100 text-yellow-800" },
    commission: { label: "佣金分成", color: "bg-purple-100 text-purple-800" }
  };

  const config = typeConfig[shareType as keyof typeof typeConfig] || typeConfig.revenue;
  
  return (
    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
      {config.label}
    </span>
  );
};

// MemberProfitRateCard 组件暂时未使用，保留以备将来使用
// const MemberProfitRateCard = ({ ... }) => { ... };

const ShareCard = ({ share }: { share: ProjectShare }) => {
  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          {share.user.avatar ? (
            <Image
              src={share.user.avatar}
              alt={share.user.name || "用户头像"}
              className="w-8 h-8 rounded-full"
              width={32}
              height={32}
            />
          ) : (
            <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
              <span className="text-sm font-medium text-gray-600">
                {share.user.name.charAt(0)}
              </span>
            </div>
          )}
          <div>
            <h4 className="font-medium text-gray-900">
              {share.user.name}
            </h4>
          </div>
        </div>
        <ShareTypeBadge shareType={share.shareType} />
      </div>

      <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
        <div>
          <span className="font-medium">分成比例:</span> {share.percentage}%
        </div>
        {share.amount && (
          <div>
            <span className="font-medium">固定金额:</span> ¥{share.amount.toLocaleString()}
          </div>
        )}
        <div>
          <span className="font-medium">分成周期:</span> {share.period}
        </div>
        <div>
          <span className="font-medium">状态:</span> {share.status}
        </div>
        <div>
          <span className="font-medium">开始时间:</span> {new Date(share.startDate).toLocaleDateString()}
        </div>
        {share.endDate && (
          <div>
            <span className="font-medium">结束时间:</span> {new Date(share.endDate).toLocaleDateString()}
          </div>
        )}
      </div>
    </div>
  );
};

const ProfitCalculator = ({ 
  members, 
  currentMonthRevenue, 
  totalRevenue 
}: { 
  members: ProjectMember[]; 
  currentMonthRevenue: number; 
  totalRevenue: number; 
}) => {
  const totalProfitRate = members.reduce((sum, member) => sum + (member.profitRate || 0), 0);
  const remainingRate = Math.max(0, 100 - totalProfitRate);

  const memberProfits = members.map(member => ({
    ...member,
    currentMonthProfit: currentMonthRevenue * (member.profitRate || 0) / 100,
    totalProfit: totalRevenue * (member.profitRate || 0) / 100,
  }));

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">分成计算</h3>
      
      <div className="grid grid-cols-3 gap-4 mb-6">
        <div className="text-center">
          <div className="text-2xl font-bold text-gray-900">¥{currentMonthRevenue.toLocaleString()}</div>
          <div className="text-sm text-gray-500">当月收益</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-gray-900">¥{totalRevenue.toLocaleString()}</div>
          <div className="text-sm text-gray-500">累计收益</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-gray-900">{totalProfitRate}%</div>
          <div className="text-sm text-gray-500">已分配比例</div>
        </div>
      </div>

      {remainingRate > 0 && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
          <div className="text-sm text-yellow-800">
            还有 {remainingRate}% 的收益未分配
          </div>
        </div>
      )}

      <div className="space-y-3">
        <h4 className="font-medium text-gray-900">成员分成明细</h4>
        {memberProfits.map(member => (
          <div key={member.id} className="flex items-center justify-between py-2 border-b border-gray-100">
            <div className="flex items-center space-x-3">
              <span className="font-medium text-gray-900">
                {member.user.name}
              </span>
              <span className="text-sm text-gray-500">({member.profitRate || 0}%)</span>
            </div>
            <div className="text-right">
              <div className="font-medium text-gray-900">
                ¥{member.currentMonthProfit.toLocaleString()}
              </div>
              <div className="text-sm text-gray-500">
                累计: ¥{member.totalProfit.toLocaleString()}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const CreateShareForm = ({
  projectId,
  members,
  onSuccess
}: {
  projectId: string;
  members: ProjectMember[];
  onSuccess: () => void;
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [formData, setFormData] = useState({
    userId: '',
    shareType: 'revenue' as const,
    percentage: 0,
    amount: undefined as number | undefined,
    period: 'monthly' as const,
    startDate: new Date().toISOString().split('T')[0],
    endDate: '',
    conditions: '',
    rules: '',
  });

  const createProfitShare = api.project.createProfitShare.useMutation({
    onSuccess: () => {
      onSuccess();
      setIsOpen(false);
      setFormData({
        userId: '',
        shareType: 'revenue',
        percentage: 0,
        amount: undefined,
        period: 'monthly',
        startDate: new Date().toISOString().split('T')[0],
        endDate: '',
        conditions: '',
        rules: '',
      });
    },
    onError: (error) => {
      alert(`创建失败: ${error.message}`);
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.userId) {
      alert('请选择成员');
      return;
    }

    createProfitShare.mutate({
      projectId,
      userId: formData.userId,
      shareType: formData.shareType,
      percentage: formData.percentage,
      amount: formData.amount,
      period: formData.period,
      startDate: formData.startDate ? new Date(formData.startDate) : new Date(),
      endDate: formData.endDate ? new Date(formData.endDate) : undefined,
      conditions: formData.conditions,
      rules: formData.rules,
    });
  };

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
      >
        创建分成记录
      </button>
    );
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">创建分成记录</h3>
        <button
          onClick={() => setIsOpen(false)}
          className="text-gray-400 hover:text-gray-600"
        >
          ✕
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">成员</label>
          <select
            value={formData.userId}
            onChange={(e) => setFormData({ ...formData, userId: e.target.value })}
            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            required
          >
            <option value="">请选择成员</option>
            {members.map(member => (
              <option key={member.user.id} value={member.user.id}>
                {member.user.name} ({member.user.email})
              </option>
            ))}
          </select>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">分成类型</label>
            <select
              value={formData.shareType}
              onChange={(e) => setFormData({ ...formData, shareType: e.target.value as any })}
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            >
              <option value="equity">股权分成</option>
              <option value="revenue">收益分成</option>
              <option value="bonus">奖金分成</option>
              <option value="commission">佣金分成</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">分成周期</label>
            <select
              value={formData.period}
              onChange={(e) => setFormData({ ...formData, period: e.target.value as any })}
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            >
              <option value="monthly">月度</option>
              <option value="quarterly">季度</option>
              <option value="yearly">年度</option>
              <option value="one_time">一次性</option>
            </select>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">分成比例 (%)</label>
            <input
              type="number"
              value={formData.percentage}
              onChange={(e) => setFormData({ ...formData, percentage: Number(e.target.value) })}
              min="0"
              max="100"
              step="0.1"
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">固定金额 (可选)</label>
            <input
              type="number"
              value={formData.amount || ''}
              onChange={(e) => setFormData({ ...formData, amount: e.target.value ? Number(e.target.value) : undefined })}
              min="0"
              step="0.01"
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">开始日期</label>
            <input
              type="date"
              value={formData.startDate}
              onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">结束日期 (可选)</label>
            <input
              type="date"
              value={formData.endDate}
              onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">分成条件 (可选)</label>
          <textarea
            value={formData.conditions}
            onChange={(e) => setFormData({ ...formData, conditions: e.target.value })}
            rows={3}
            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            placeholder="描述分成的触发条件..."
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">分成规则 (可选)</label>
          <textarea
            value={formData.rules}
            onChange={(e) => setFormData({ ...formData, rules: e.target.value })}
            rows={3}
            className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            placeholder="描述具体的分成规则..."
          />
        </div>

        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={() => setIsOpen(false)}
            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            取消
          </button>
          <button
            type="submit"
            disabled={createProfitShare.isPending}
            className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 disabled:opacity-50"
          >
            {createProfitShare.isPending ? '创建中...' : '创建'}
          </button>
        </div>
      </form>
    </div>
  );
};

export const ProfitSharingManager: React.FC<ProfitSharingManagerProps> = ({
  projectId,
  members,
  shares,
  currentMonthRevenue,
  totalRevenue
}) => {
  const [activeTab, setActiveTab] = useState<'shares' | 'calculator'>('calculator');

  // Note: This mutation is defined but currently unused
  // const updateMemberProfitRate = api.project.updateMemberProfitRate.useMutation({
  //   onSuccess: () => {
  //     // 可以添加成功提示
  //     window.location.reload(); // 简单的刷新页面来更新数据
  //   },
  //   onError: (error) => {
  //     alert(`更新失败: ${error.message}`);
  //   },
  // });

  // handleUpdateProfitRate 函数暂时未使用，保留以备将来使用
  // const handleUpdateProfitRate = async (userId: string, profitRate: number) => { ... };

  return (
    <div className="space-y-6">
      {/* 标签页导航 */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('calculator')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'calculator'
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            分成计算
          </button>
          <button
            onClick={() => setActiveTab('shares')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'shares'
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            分成记录
          </button>
        </nav>
      </div>

      {/* 标签页内容 */}
      {activeTab === 'calculator' && (
        <ProfitCalculator
          members={members}
          currentMonthRevenue={currentMonthRevenue}
          totalRevenue={totalRevenue}
        />
      )}

      {activeTab === 'shares' && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">分成记录</h3>
            <CreateShareForm
              projectId={projectId}
              members={members}
              onSuccess={() => window.location.reload()}
            />
          </div>
          {shares.length > 0 ? (
            shares.map(share => (
              <ShareCard key={share.id} share={share} />
            ))
          ) : (
            <div className="text-center py-8 text-gray-500">
              暂无分成记录
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ProfitSharingManager;
