/**
 * 项目类型选择组件
 * 统一的项目类型下拉选择器，使用配置中心的数据
 */

import React from 'react';
import { useProjectManagement } from '~/hooks/useProjectManagement';
import type { ProjectType } from '~/config/project-management';

interface ProjectTypeSelectProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  allowedTypes?: ProjectType[];
  name?: string;
  id?: string;
}

export function ProjectTypeSelect({
  value,
  onChange,
  placeholder = "选择项目类型",
  className = "",
  disabled = false,
  allowedTypes,
  name,
  id
}: ProjectTypeSelectProps) {
  const { typeOptions } = useProjectManagement();

  const selectOptions = allowedTypes
    ? typeOptions.filter(opt => allowedTypes.includes(opt.value))
    : typeOptions;

  return (
    <select
      id={id}
      name={name}
      value={value}
      onChange={(e) => onChange(e.target.value)}
      disabled={disabled}
      className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 ${className}`}
    >
      <option value="">{placeholder}</option>
      {selectOptions.map(option => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  );
}

// 简化版本，直接返回选项数组，用于其他场景
export function useProjectTypeOptions() {
  const { typeOptions } = useProjectManagement();
  return typeOptions;
}

export default ProjectTypeSelect;
