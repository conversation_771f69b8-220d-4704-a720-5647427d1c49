"use client";

import React, { useState } from 'react';

// 定义项目类型
interface ProjectWithChildren {
  id: string;
  name: string;
  code: string;
  type: string;
  status: string;
  parentId?: string | null;
  createdBy: { name: string };
  poUser?: { name: string } | null;
  currentMonthRevenue: number;
  totalRevenue: number;
  memberCount?: number;
  children?: ProjectWithChildren[];
}

interface ProjectHierarchyProps {
  projects: ProjectWithChildren[];
  currentProjectId?: string;
  onProjectSelect?: (project: ProjectWithChildren) => void;
}

const ProjectTypeIcon = ({ type }: { type: string }) => {
  const { getTypeConfig } = useProjectManagement();
  const config = getTypeConfig(type);

  return <span className="text-lg mr-2">{config.icon}</span>;
};

import { useProjectManagement } from "~/hooks/useProjectManagement";

const StatusBadge = ({ status }: { status: string }) => {
  const { getStatusConfig } = useProjectManagement();
  const config = getStatusConfig(status);
  
  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
      {config.icon} {config.label}
    </span>
  );
};

const ProjectCard = ({
  project,
  level = 0,
  onSelect,
  isCurrentProject = false,
  currentProjectId
}: {
  project: ProjectWithChildren;
  level?: number;
  onSelect?: (project: ProjectWithChildren) => void;
  isCurrentProject?: boolean;
  currentProjectId?: string;
}) => {
  const [isExpanded, setIsExpanded] = useState(level < 2);
  const hasChildren = project.children && project.children.length > 0;

  return (
    <div className={`ml-${level * 4}`}>
      <div
        className={`bg-white border rounded-lg p-4 mb-3 hover:shadow-md transition-shadow cursor-pointer ${
          isCurrentProject
            ? 'border-indigo-500 bg-indigo-50 shadow-md'
            : 'border-gray-200'
        }`}
        onClick={() => onSelect?.(project)}
      >
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center mb-2">
              {hasChildren && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsExpanded(!isExpanded);
                  }}
                  className="mr-2 p-1 hover:bg-gray-100 rounded"
                >
                  {isExpanded ? "▼" : "▶"}
                </button>
              )}
              <ProjectTypeIcon type={project.type} />
              <h3 className={`text-lg font-semibold ${isCurrentProject ? 'text-indigo-900' : 'text-gray-900'}`}>
                {project.name}
                {isCurrentProject && (
                  <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-indigo-100 text-indigo-800">
                    当前项目
                  </span>
                )}
              </h3>
              <span className="ml-2 text-sm text-gray-500">({project.code})</span>
            </div>
            
            <div className="flex items-center space-x-3 mb-2">
              <StatusBadge status={project.status} />
            </div>

            <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
              <div>
                <span className="font-medium">创建者:</span> {project.createdBy.name}
              </div>
              {project.poUser && (
                <div>
                  <span className="font-medium">负责人:</span> {project.poUser.name}
                </div>
              )}
              <div>
                <span className="font-medium">当月收益:</span> ¥{project.currentMonthRevenue.toLocaleString()}
              </div>
              <div>
                <span className="font-medium">累计收益:</span> ¥{project.totalRevenue.toLocaleString()}
              </div>
            </div>
          </div>

          <div className="flex flex-col items-end space-y-2">
            {project.memberCount !== undefined && (
              <span className="text-sm text-gray-500">
                {project.memberCount} 成员
              </span>
            )}
          </div>
        </div>
      </div>

      {hasChildren && isExpanded && (
        <div className="ml-4">
          {project.children!.map((child: ProjectWithChildren) => (
            <ProjectCard
              key={child.id}
              project={child}
              level={level + 1}
              onSelect={onSelect}
              isCurrentProject={child.id === currentProjectId}
              currentProjectId={currentProjectId}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export const ProjectHierarchy: React.FC<ProjectHierarchyProps> = ({
  projects,
  currentProjectId,
  onProjectSelect
}) => {
  // 构建项目层级结构
  const buildHierarchy = (projects: ProjectWithChildren[]): ProjectWithChildren[] => {
    const projectMap = new Map<string, ProjectWithChildren>();
    const rootProjects: ProjectWithChildren[] = [];

    // 创建项目映射
    projects.forEach(project => {
      projectMap.set(project.id, { ...project, children: [] });
    });

    // 构建层级关系
    projects.forEach(project => {
      const projectWithChildren = projectMap.get(project.id)!;
      
      if (project.parentId) {
        const parent = projectMap.get(project.parentId);
        if (parent) {
          parent.children!.push(projectWithChildren);
        } else {
          // 父项目不存在，作为根项目处理
          rootProjects.push(projectWithChildren);
        }
      } else {
        rootProjects.push(projectWithChildren);
      }
    });

    return rootProjects;
  };

  const hierarchicalProjects = buildHierarchy(projects);

  if (projects.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="text-gray-500 mb-4">暂无项目</div>
        <button className="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700">
          创建项目
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">项目层级结构</h2>
        <div className="text-sm text-gray-500">
          共 {projects.length} 个相关项目
        </div>
      </div>

      <div className="space-y-3">
        {hierarchicalProjects.map((project) => (
          <ProjectCard
            key={project.id}
            project={project}
            onSelect={onProjectSelect}
            isCurrentProject={project.id === currentProjectId}
            currentProjectId={currentProjectId}
          />
        ))}
      </div>
    </div>
  );
};

export default ProjectHierarchy;
