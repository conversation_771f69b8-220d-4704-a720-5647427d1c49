"use client";

import { useState } from "react";
import Image from "next/image";
import { api } from "~/trpc/react";
import type { ProjectMember, User } from "~/types/project";
import { useProjectManagement } from "~/hooks/useProjectManagement";

// 使用导入的类型定义

interface MemberManagerProps {
  projectId: string;
  members: ProjectMember[];
  canEdit: boolean;
  onMemberUpdate: () => void;
}

// 角色徽章组件
const RoleBadge = ({ role }: { role: string }) => {
  const { getRoleConfig } = useProjectManagement();
  const config = getRoleConfig(role);

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
      {config.icon} {config.label}
    </span>
  );
};

const AddMemberModal = ({
  projectId,
  isOpen,
  onClose,
  onSuccess
}: {
  projectId: string;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [selectedRole, setSelectedRole] = useState("other");
  const [profitRate, setProfitRate] = useState(0);

  const { roleOptions } = useProjectManagement();

  // 获取用户列表
  const { data: usersData } = api.user.getUsers.useQuery({
    page: 1,
    limit: 50,
    search: searchTerm || undefined,
  }, {
    enabled: isOpen && searchTerm.length > 0,
  });

  const addMemberMutation = api.project.addMember.useMutation({
    onSuccess: () => {
      onSuccess();
      onClose();
      setSelectedUser(null);
      setSelectedRole("other");
      setProfitRate(0);
      setSearchTerm("");
    },
    onError: (error) => {
      alert(`添加成员失败: ${error.message}`);
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedUser) {
      alert("请选择用户");
      return;
    }

    addMemberMutation.mutate({
      projectId,
      userId: selectedUser.id,
      role: selectedRole,
      profitRate: profitRate > 0 ? profitRate : undefined,
      permissions: [],
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div className="mt-3">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">添加项目成员</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">搜索用户</label>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="输入用户名或邮箱搜索..."
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              />
            </div>

            {usersData?.users && usersData.users.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700">选择用户</label>
                <div className="mt-1 max-h-40 overflow-y-auto border border-gray-300 rounded-md">
                  {usersData.users.map((user) => (
                    <div
                      key={user.id}
                      onClick={() => setSelectedUser(user)}
                      className={`p-3 cursor-pointer hover:bg-gray-50 ${
                        selectedUser?.id === user.id ? 'bg-indigo-50 border-indigo-200' : ''
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        {user.avatar ? (
                          <Image
                            className="h-8 w-8 rounded-full"
                            src={user.avatar}
                            alt={user.name || "用户头像"}
                            width={32}
                            height={32}
                          />
                        ) : (
                          <div className="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                            <span className="text-xs font-medium text-gray-700">
                              {user.name.charAt(0)}
                            </span>
                          </div>
                        )}
                        <div>
                          <p className="text-sm font-medium text-gray-900">{user.name}</p>
                          <p className="text-xs text-gray-500">{user.email}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {selectedUser && (
              <>
                <div>
                  <label className="block text-sm font-medium text-gray-700">角色</label>
                  <select
                    value={selectedRole}
                    onChange={(e) => setSelectedRole(e.target.value)}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  >
                    {roleOptions.map((role) => (
                      <option key={role.value} value={role.value}>
                        {role.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">分成比例 (%)</label>
                  <input
                    type="number"
                    value={profitRate}
                    onChange={(e) => setProfitRate(Number(e.target.value))}
                    min="0"
                    max="100"
                    step="0.1"
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  />
                </div>
              </>
            )}

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                取消
              </button>
              <button
                type="submit"
                disabled={!selectedUser || addMemberMutation.isPending}
                className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 disabled:opacity-50"
              >
                {addMemberMutation.isPending ? '添加中...' : '添加成员'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

const MemberCard = ({
  member,
  projectId,
  canEdit,
  onUpdate
}: {
  member: ProjectMember;
  projectId: string;
  canEdit: boolean;
  onUpdate: () => void;
}) => {
  const [isEditingRole, setIsEditingRole] = useState(false);
  const [selectedRole, setSelectedRole] = useState(member.role);

  const { roleOptions } = useProjectManagement();

  const updateRoleMutation = api.project.updateMemberRole.useMutation({
    onSuccess: () => {
      onUpdate();
      setIsEditingRole(false);
    },
    onError: (error) => {
      alert(`更新角色失败: ${error.message}`);
    },
  });

  const removeMemberMutation = api.project.removeMember.useMutation({
    onSuccess: () => {
      onUpdate();
    },
    onError: (error) => {
      alert(`移除成员失败: ${error.message}`);
    },
  });

  const handleRoleUpdate = () => {
    updateRoleMutation.mutate({
      projectId,
      userId: member.userId,
      role: selectedRole,
    });
  };

  const handleRemoveMember = () => {
    if (confirm(`确定要移除成员 ${member.user.name} 吗？`)) {
      removeMemberMutation.mutate({
        projectId,
        userId: member.userId,
      });
    }
  };

  return (
    <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
      <div className="flex items-center space-x-3">
        {member.user.avatar ? (
          <Image
            className="h-10 w-10 rounded-full"
            src={member.user.avatar}
            alt={member.user.name || "用户头像"}
            width={40}
            height={40}
          />
        ) : (
          <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
            <span className="text-sm font-medium text-gray-700">
              {member.user.name.charAt(0)}
            </span>
          </div>
        )}
        <div>
          <h4 className="text-sm font-medium text-gray-900">{member.user.name}</h4>
          <p className="text-sm text-gray-500">{member.user.email}</p>
          {member.profitRate && (
            <p className="text-xs text-green-600">分成比例: {member.profitRate}%</p>
          )}
        </div>
      </div>
      <div className="flex items-center space-x-3">
        {isEditingRole ? (
          <div className="flex items-center space-x-2">
            <select
              value={selectedRole}
              onChange={(e) => setSelectedRole(e.target.value)}
              className="text-xs border border-gray-300 rounded px-2 py-1"
            >
              {roleOptions.map((role) => (
                <option key={role.value} value={role.value}>
                  {role.label}
                </option>
              ))}
            </select>
            <button
              onClick={handleRoleUpdate}
              disabled={updateRoleMutation.isPending}
              className="text-xs bg-green-600 text-white px-2 py-1 rounded hover:bg-green-700"
            >
              保存
            </button>
            <button
              onClick={() => setIsEditingRole(false)}
              className="text-xs bg-gray-600 text-white px-2 py-1 rounded hover:bg-gray-700"
            >
              取消
            </button>
          </div>
        ) : (
          <>
            <RoleBadge role={member.role} />
            {canEdit && (
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setIsEditingRole(true)}
                  className="text-xs text-indigo-600 hover:text-indigo-800"
                >
                  编辑
                </button>
                <button
                  onClick={handleRemoveMember}
                  disabled={removeMemberMutation.isPending}
                  className="text-xs text-red-600 hover:text-red-800"
                >
                  移除
                </button>
              </div>
            )}
          </>
        )}
        <span className="text-sm text-gray-500">
          {new Date(member.joinedAt).toLocaleDateString('zh-CN')}
        </span>
      </div>
    </div>
  );
};

export const MemberManager: React.FC<MemberManagerProps> = ({
  projectId,
  members,
  canEdit,
  onMemberUpdate,
}) => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg leading-6 font-medium text-gray-900">项目成员</h3>
          {canEdit && (
            <button
              onClick={() => setIsAddModalOpen(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              添加成员
            </button>
          )}
        </div>
      </div>
      <div className="p-6">
        <div className="space-y-4">
          {members.map((member) => (
            <MemberCard
              key={member.id}
              member={member}
              projectId={projectId}
              canEdit={canEdit}
              onUpdate={onMemberUpdate}
            />
          ))}
          {members.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              暂无项目成员
            </div>
          )}
        </div>
      </div>

      <AddMemberModal
        projectId={projectId}
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSuccess={onMemberUpdate}
      />
    </div>
  );
};
