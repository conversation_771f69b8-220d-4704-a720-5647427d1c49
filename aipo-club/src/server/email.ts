import nodemailer from "nodemailer";
import { env } from "~/env";

// 创建邮件传输器
const transporter = nodemailer.createTransport({
  host: env.SMTP_HOST,
  port: env.SMTP_PORT,
  secure: env.SMTP_SECURE, // true for 465, false for other ports
  auth: {
    user: env.SMTP_USER,
    pass: env.SMTP_PASS,
  },
});

// 邮件模板类型
export interface EmailTemplate {
  to: string;
  subject: string;
  html: string;
  text?: string;
}

// 发送邮件的通用函数
export async function sendEmail(template: EmailTemplate): Promise<boolean> {
  try {
    const mailOptions = {
      from: `"${env.SMTP_FROM_NAME}" <${env.SMTP_FROM_EMAIL}>`,
      to: template.to,
      subject: template.subject,
      html: template.html,
      text: template.text,
    };

    const info = await transporter.sendMail(mailOptions);
    console.log("邮件发送成功:", info.messageId);
    return true;
  } catch (error) {
    console.error("邮件发送失败:", error);
    return false;
  }
}

// 用户注册欢迎邮件
export async function sendWelcomeEmail(
  email: string,
  name: string
): Promise<boolean> {
  const template: EmailTemplate = {
    to: email,
    subject: "欢迎加入AIPO俱乐部！",
    html: `
      <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px 20px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px;">欢迎加入AIPO俱乐部！</h1>
        </div>
        
        <div style="padding: 40px 20px; background: #f8f9fa;">
          <h2 style="color: #333; margin-bottom: 20px;">亲爱的 ${name}，</h2>
          
          <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
            感谢您注册AIPO俱乐部！我们很高兴您能加入我们的数字游民社区。
          </p>
          
          <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
            您的账户正在审核中，我们会在1-2个工作日内完成审核。审核通过后，您将收到账户激活通知邮件。
          </p>
          
          <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #1976d2; margin-top: 0;">在等待审核期间，您可以：</h3>
            <ul style="color: #666; line-height: 1.6;">
              <li>了解我们的平台功能和服务</li>
              <li>关注我们的社交媒体获取最新动态</li>
              <li>准备您的项目想法和计划</li>
            </ul>
          </div>
          
          <p style="color: #666; line-height: 1.6;">
            如有任何问题，请随时联系我们的客服团队。
          </p>
          
          <div style="text-align: center; margin-top: 40px;">
            <a href="${process.env.NEXTAUTH_URL || 'http://localhost:3001'}" 
               style="background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; display: inline-block;">
              访问AIPO俱乐部
            </a>
          </div>
        </div>
        
        <div style="background: #333; color: #999; padding: 20px; text-align: center; font-size: 14px;">
          <p style="margin: 0;">© 2024 AIPO俱乐部. 保留所有权利.</p>
          <p style="margin: 10px 0 0 0;">这是一封自动发送的邮件，请勿回复。</p>
        </div>
      </div>
    `,
    text: `
      欢迎加入AIPO俱乐部！
      
      亲爱的 ${name}，
      
      感谢您注册AIPO俱乐部！我们很高兴您能加入我们的数字游民社区。
      
      您的账户正在审核中，我们会在1-2个工作日内完成审核。审核通过后，您将收到账户激活通知邮件。
      
      如有任何问题，请随时联系我们的客服团队。
      
      访问AIPO俱乐部: ${process.env.NEXTAUTH_URL || 'http://localhost:3001'}
      
      © 2024 AIPO俱乐部. 保留所有权利.
      这是一封自动发送的邮件，请勿回复。
    `,
  };

  return await sendEmail(template);
}

// 账户激活通知邮件
export async function sendAccountActivatedEmail(
  email: string,
  name: string
): Promise<boolean> {
  const template: EmailTemplate = {
    to: email,
    subject: "账户已激活 - 开始您的AIPO俱乐部之旅！",
    html: `
      <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
        <div style="background: linear-gradient(135deg, #4caf50 0%, #45a049 100%); padding: 40px 20px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px;">🎉 账户已激活！</h1>
        </div>
        
        <div style="padding: 40px 20px; background: #f8f9fa;">
          <h2 style="color: #333; margin-bottom: 20px;">恭喜 ${name}！</h2>
          
          <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
            您的AIPO俱乐部账户已成功激活！现在您可以开始使用我们平台的所有功能了。
          </p>
          
          <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #2e7d32; margin-top: 0;">您现在可以：</h3>
            <ul style="color: #666; line-height: 1.6;">
              <li>创建和管理项目</li>
              <li>加入其他项目团队</li>
              <li>参与分成计划</li>
              <li>使用AI资源和中台支持</li>
              <li>与其他数字游民交流合作</li>
            </ul>
          </div>
          
          <p style="color: #666; line-height: 1.6;">
            立即登录开始您的数字游民之旅吧！
          </p>
          
          <div style="text-align: center; margin-top: 40px;">
            <a href="${process.env.NEXTAUTH_URL || 'http://localhost:3001'}/login" 
               style="background: #4caf50; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; display: inline-block; margin-right: 10px;">
              立即登录
            </a>
            <a href="${process.env.NEXTAUTH_URL || 'http://localhost:3001'}/dashboard" 
               style="background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; display: inline-block;">
              进入控制台
            </a>
          </div>
        </div>
        
        <div style="background: #333; color: #999; padding: 20px; text-align: center; font-size: 14px;">
          <p style="margin: 0;">© 2024 AIPO俱乐部. 保留所有权利.</p>
          <p style="margin: 10px 0 0 0;">这是一封自动发送的邮件，请勿回复。</p>
        </div>
      </div>
    `,
    text: `
      🎉 账户已激活！
      
      恭喜 ${name}！
      
      您的AIPO俱乐部账户已成功激活！现在您可以开始使用我们平台的所有功能了。
      
      您现在可以：
      - 创建和管理项目
      - 加入其他项目团队
      - 参与分成计划
      - 使用AI资源和中台支持
      - 与其他数字游民交流合作
      
      立即登录开始您的数字游民之旅吧！
      
      登录地址: ${process.env.NEXTAUTH_URL || 'http://localhost:3001'}/login
      控制台: ${process.env.NEXTAUTH_URL || 'http://localhost:3001'}/dashboard
      
      © 2024 AIPO俱乐部. 保留所有权利.
      这是一封自动发送的邮件，请勿回复。
    `,
  };

  return await sendEmail(template);
}

// 密码重置邮件
export async function sendPasswordResetEmail(
  email: string,
  name: string,
  resetToken: string
): Promise<boolean> {
  const resetUrl = `${process.env.NEXTAUTH_URL || 'http://localhost:3001'}/reset-password?token=${resetToken}`;
  
  const template: EmailTemplate = {
    to: email,
    subject: "重置您的AIPO俱乐部密码",
    html: `
      <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
        <div style="background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); padding: 40px 20px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px;">🔐 密码重置</h1>
        </div>
        
        <div style="padding: 40px 20px; background: #f8f9fa;">
          <h2 style="color: #333; margin-bottom: 20px;">您好 ${name}，</h2>
          
          <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
            我们收到了您重置AIPO俱乐部账户密码的请求。
          </p>
          
          <div style="background: #fff3e0; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ff9800;">
            <p style="color: #e65100; margin: 0; font-weight: bold;">
              ⚠️ 重要提醒：此链接将在24小时后失效
            </p>
          </div>
          
          <p style="color: #666; line-height: 1.6; margin-bottom: 30px;">
            请点击下面的按钮重置您的密码：
          </p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}" 
               style="background: #ff9800; color: white; padding: 15px 40px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold;">
              重置密码
            </a>
          </div>
          
          <p style="color: #999; font-size: 14px; line-height: 1.6;">
            如果按钮无法点击，请复制以下链接到浏览器地址栏：<br>
            <span style="word-break: break-all;">${resetUrl}</span>
          </p>
          
          <div style="background: #ffebee; padding: 20px; border-radius: 8px; margin: 30px 0;">
            <p style="color: #c62828; margin: 0; font-size: 14px;">
              <strong>安全提醒：</strong>如果您没有请求重置密码，请忽略此邮件。您的账户仍然安全。
            </p>
          </div>
        </div>
        
        <div style="background: #333; color: #999; padding: 20px; text-align: center; font-size: 14px;">
          <p style="margin: 0;">© 2024 AIPO俱乐部. 保留所有权利.</p>
          <p style="margin: 10px 0 0 0;">这是一封自动发送的邮件，请勿回复。</p>
        </div>
      </div>
    `,
    text: `
      🔐 密码重置
      
      您好 ${name}，
      
      我们收到了您重置AIPO俱乐部账户密码的请求。
      
      ⚠️ 重要提醒：此链接将在24小时后失效
      
      请访问以下链接重置您的密码：
      ${resetUrl}
      
      安全提醒：如果您没有请求重置密码，请忽略此邮件。您的账户仍然安全。
      
      © 2024 AIPO俱乐部. 保留所有权利.
      这是一封自动发送的邮件，请勿回复。
    `,
  };

  return await sendEmail(template);
}

// 测试邮件连接
export async function testEmailConnection(): Promise<boolean> {
  try {
    await transporter.verify();
    console.log("SMTP连接测试成功");
    return true;
  } catch (error) {
    console.error("SMTP连接测试失败:", error);
    return false;
  }
}
