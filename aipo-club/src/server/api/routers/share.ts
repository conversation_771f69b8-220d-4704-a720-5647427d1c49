import { z } from "zod";
import type { Prisma } from "@prisma/client";

import {
  createTRPCRouter,
  protectedProcedure,
} from "~/server/api/trpc";

// 分成创建输入验证
const createShareSchema = z.object({
  projectId: z.string(),
  userId: z.string(),
  shareType: z.enum(["equity", "revenue", "bonus", "commission"]),
  percentage: z.number().min(0).max(100),
  amount: z.number().min(0).optional(),
  period: z.enum(["one_time", "monthly", "quarterly", "yearly"]).default("one_time"),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  conditions: z.string().optional(),
  rules: z.string().optional(),
});

// 分成更新输入验证
const updateShareSchema = z.object({
  id: z.string(),
  shareType: z.enum(["equity", "revenue", "bonus", "commission"]).optional(),
  percentage: z.number().min(0).max(100).optional(),
  amount: z.number().min(0).optional(),
  period: z.enum(["one_time", "monthly", "quarterly", "yearly"]).optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  conditions: z.string().optional(),
  rules: z.string().optional(),
  status: z.enum(["pending", "active", "completed", "cancelled"]).optional(),
});

export const shareRouter = createTRPCRouter({
  // 创建分成规则
  create: protectedProcedure
    .input(createShareSchema)
    .mutation(async ({ ctx, input }) => {
      // 检查项目权限
      const project = await ctx.db.project.findUnique({
        where: { id: input.projectId },
        include: {
          projectMember: {
            where: {
              userId: ctx.session.user.id,
              status: "active",
            },
          },
        },
      });

      if (!project) {
        throw new Error("项目不存在");
      }

      const isCreator = project.createdById === ctx.session.user.id;
      const isManager = project.projectMember.some(
        member => ["po", "product"].includes(member.role)
      );

      if (!isCreator && !isManager) {
        throw new Error("权限不足");
      }

      // 检查用户是否是项目成员
      const targetMember = await ctx.db.projectMember.findUnique({
        where: {
          projectId_userId: {
            projectId: input.projectId,
            userId: input.userId,
          },
        },
      });

      if (!targetMember) {
        throw new Error("用户不是项目成员");
      }

      // 检查分成总比例是否超过100%
      const existingShares = await ctx.db.projectShare.findMany({
        where: {
          projectId: input.projectId,
          shareType: input.shareType,
          status: "active",
        },
      });

      const totalPercentage = existingShares.reduce((sum, share) => sum + share.percentage, 0);
      if (totalPercentage + input.percentage > 100) {
        throw new Error(`${input.shareType} 分成总比例不能超过100%`);
      }

      // 获取当前用户信息
      const currentUser = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { name: true },
      });

      const share = await ctx.db.projectShare.create({
        data: {
          projectId: input.projectId,
          userId: input.userId,
          shareType: input.shareType,
          percentage: input.percentage,
          amount: input.amount,
          period: input.period,
          startDate: input.startDate || new Date(),
          endDate: input.endDate,
          conditions: input.conditions || "{}",
          rules: input.rules || "{}",
          status: "active",
          createdBy: currentUser?.name || "",
          updatedBy: currentUser?.name || "",
          createdById: ctx.session.user.id,
          updatedById: ctx.session.user.id,
          updatedAt: new Date(),
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              avatar: true,
            },
          },
          project: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      return {
        ...share,
        conditions: share.conditions ? JSON.parse(share.conditions) : {},
        rules: share.rules ? JSON.parse(share.rules) : {},
      };
    }),

  // 获取项目分成列表
  getProjectShares: protectedProcedure
    .input(z.object({ projectId: z.string() }))
    .query(async ({ ctx, input }) => {
      // 检查项目权限
      const project = await ctx.db.project.findUnique({
        where: { id: input.projectId },
        include: {
          projectMember: {
            where: {
              userId: ctx.session.user.id,
              status: "active",
            },
          },
        },
      });

      if (!project) {
        throw new Error("项目不存在");
      }

      const isMember = project.projectMember.length > 0;
      const isCreator = project.createdById === ctx.session.user.id;

      if (!isMember && !isCreator) {
        throw new Error("权限不足");
      }

      const shares = await ctx.db.projectShare.findMany({
        where: { projectId: input.projectId },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              avatar: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
      });

      return shares.map(share => ({
        ...share,
        conditions: share.conditions ? JSON.parse(share.conditions) : {},
        rules: share.rules ? JSON.parse(share.rules) : {},
      }));
    }),

  // 获取用户分成列表
  getUserShares: protectedProcedure
    .input(
      z.object({
        page: z.number().min(1).default(1),
        limit: z.number().min(1).max(100).default(10),
        status: z.enum(["pending", "active", "completed", "cancelled"]).optional(),
        shareType: z.enum(["equity", "revenue", "bonus", "commission"]).optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      const { page, limit, status, shareType } = input;
      const skip = (page - 1) * limit;

      const where: Prisma.projectShareWhereInput = {
        userId: ctx.session.user.id,
      };
      if (status) where.status = status;
      if (shareType) where.shareType = shareType;

      const [shares, total] = await Promise.all([
        ctx.db.projectShare.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: "desc" },
          include: {
            project: {
              select: {
                id: true,
                name: true,
                status: true,
              },
            },
          },
        }),
        ctx.db.projectShare.count({ where }),
      ]);

      return {
        shares: shares.map(share => ({
          ...share,
          conditions: share.conditions ? JSON.parse(share.conditions) : {},
          rules: share.rules ? JSON.parse(share.rules) : {},
        })),
        total,
        pages: Math.ceil(total / limit),
        currentPage: page,
      };
    }),

  // 更新分成规则
  update: protectedProcedure
    .input(updateShareSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, ...updateData } = input;

      // 检查分成规则是否存在
      const share = await ctx.db.projectShare.findUnique({
        where: { id },
        include: {
          project: {
            include: {
              projectMember: {
                where: {
                  userId: ctx.session.user.id,
                  status: "active",
                },
              },
            },
          },
        },
      });

      if (!share) {
        throw new Error("分成规则不存在");
      }

      const isCreator = share.project.createdById === ctx.session.user.id;
      const isManager = share.project.projectMember.some(
        member => ["po", "platform"].includes(member.role)
      );

      if (!isCreator && !isManager) {
        throw new Error("权限不足");
      }

      // 如果更新百分比，检查总比例
      if (updateData.percentage !== undefined) {
        const existingShares = await ctx.db.projectShare.findMany({
          where: {
            projectId: share.projectId,
            shareType: share.shareType,
            status: "active",
            id: { not: id },
          },
        });

        const totalPercentage = existingShares.reduce((sum, s) => sum + s.percentage, 0);
        if (totalPercentage + updateData.percentage > 100) {
          throw new Error(`${share.shareType} 分成总比例不能超过100%`);
        }
      }

      // 获取当前用户信息
      const currentUser = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { name: true },
      });

      const updatedShare = await ctx.db.projectShare.update({
        where: { id },
        data: {
          ...updateData,
          updatedBy: currentUser?.name || "",
          updatedById: ctx.session.user.id,
          updatedAt: new Date(),
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              avatar: true,
            },
          },
          project: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      return {
        ...updatedShare,
        conditions: updatedShare.conditions ? JSON.parse(updatedShare.conditions) : {},
        rules: updatedShare.rules ? JSON.parse(updatedShare.rules) : {},
      };
    }),

  // 删除分成规则
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // 检查分成规则是否存在
      const share = await ctx.db.projectShare.findUnique({
        where: { id: input.id },
        include: {
          project: {
            include: {
              projectMember: {
                where: {
                  userId: ctx.session.user.id,
                  status: "active",
                },
              },
            },
          },
        },
      });

      if (!share) {
        throw new Error("分成规则不存在");
      }

      const isCreator = share.project.createdById === ctx.session.user.id;
      const isManager = share.project.projectMember.some(
        member => ["po", "platform"].includes(member.role)
      );

      if (!isCreator && !isManager) {
        throw new Error("权限不足");
      }

      await ctx.db.projectShare.delete({
        where: { id: input.id },
      });

      return { success: true };
    }),

  // 获取分成统计
  getShareStats: protectedProcedure
    .input(z.object({ projectId: z.string() }))
    .query(async ({ ctx, input }) => {
      // 检查项目权限
      const project = await ctx.db.project.findUnique({
        where: { id: input.projectId },
        include: {
          projectMember: {
            where: {
              userId: ctx.session.user.id,
              status: "active",
            },
          },
        },
      });

      if (!project) {
        throw new Error("项目不存在");
      }

      const isMember = project.projectMember.length > 0;
      const isCreator = project.createdById === ctx.session.user.id;

      if (!isMember && !isCreator) {
        throw new Error("权限不足");
      }

      const shares = await ctx.db.projectShare.findMany({
        where: {
          projectId: input.projectId,
          status: "active",
        },
      });

      const stats = {
        equity: { total: 0, count: 0 },
        revenue: { total: 0, count: 0 },
        bonus: { total: 0, count: 0 },
        commission: { total: 0, count: 0 },
      };

      shares.forEach(share => {
        if (stats[share.shareType as keyof typeof stats]) {
          stats[share.shareType as keyof typeof stats].total += share.percentage;
          stats[share.shareType as keyof typeof stats].count += 1;
        }
      });

      return stats;
    }),
});
