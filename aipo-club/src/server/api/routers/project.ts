import { z } from "zod";
import type { Prisma } from "@prisma/client";

import {
  createTRPCRouter,
  protectedProcedure,
} from "~/server/api/trpc";

import {
  PROJECT_STATUS_VALUES,
  PROJECT_TYPE_VALUES,
  PROJECT_PRIORITY_VALUES,
  MEMBER_ROLE_VALUES,
} from "~/config/project-management";

// 使用配置中心的枚举
const projectTypeEnum = z.enum(PROJECT_TYPE_VALUES as [string, ...string[]]);
const projectStatusEnum = z.enum(PROJECT_STATUS_VALUES as [string, ...string[]]);
const projectPriorityEnum = z.enum(PROJECT_PRIORITY_VALUES as [string, ...string[]]);
const memberRoleEnum = z.enum(MEMBER_ROLE_VALUES as [string, ...string[]]);

// 角色权限映射 - 用于权限检查的角色分组
const ROLE_PERMISSIONS = {
  // 管理员级别权限 - 只有 PO 和平台角色
  ADMIN_ROLES: ['po', 'platform'],
  // 管理级别权限 - PO 和产品角色
  MANAGER_ROLES: ['po', 'product'],
  // 开发级别权限
  DEVELOPER_ROLES: ['po', 'product', 'frontend', 'backend', 'fullstack'],
  // 成员级别权限
  MEMBER_ROLES: ['po', 'product', 'frontend', 'backend', 'fullstack', 'tester', 'operation'],
  // 成员管理权限 - 只有 PO 和平台角色可以管理成员
  MEMBER_MANAGEMENT_ROLES: ['po', 'platform'],
} as const;

// 检查用户是否有指定级别的权限
function hasRolePermission(userRole: string, requiredLevel: keyof typeof ROLE_PERMISSIONS): boolean {
  return (ROLE_PERMISSIONS[requiredLevel] as readonly string[]).includes(userRole);
}

// 项目健康状态枚举
const projectHealthStatusEnum = z.enum(['healthy', 'warning', 'critical']);

// URL验证函数
const urlSchema = z.string().url("请输入有效的URL").optional();



// 项目创建输入验证
const createProjectSchema = z.object({
  name: z.string().min(1, "项目名称不能为空").max(100, "项目名称不能超过100个字符"),
  code: z.string().min(1, "项目编码不能为空").max(50, "项目编码不能超过50个字符"),
  description: z.string().max(1000, "项目描述不能超过1000个字符").optional(),
  type: projectTypeEnum.default("other"),
  status: projectStatusEnum.default("ideation"),
  category: z.string().max(50, "项目分类不能超过50个字符").optional(),
  priority: projectPriorityEnum.default("medium"),
  parentId: z.string().optional(), // 父项目ID
  poUserId: z.string().optional(), // 项目负责人ID
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  deadline: z.date().optional(),
  budget: z.number().min(0).optional(),
  // 新增项目管理字段
  logo: urlSchema,
  visitUrl: urlSchema,
  wikiUrl: urlSchema,
  gitUrl: urlSchema,
  healthStatus: projectHealthStatusEnum.default("healthy"),
  version: z.string().max(50, "版本号不能超过50个字符").default("1.0.0"),
  techStack: z.string().max(500, "技术栈描述不能超过500个字符").optional(),
});

// 项目更新输入验证
const updateProjectSchema = z.object({
  id: z.string(),
  name: z.string().min(1).max(100).optional(),
  code: z.string().min(1).max(50).optional(),
  description: z.string().max(1000).optional(),
  type: projectTypeEnum.optional(),
  status: projectStatusEnum.optional(),
  category: z.string().max(50).optional(),
  priority: projectPriorityEnum.optional(),
  parentId: z.string().optional(),
  poUserId: z.string().optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  deadline: z.date().optional(),
  budget: z.number().min(0).optional(),
  investment: z.number().min(0).optional(),
  revenue: z.number().min(0).optional(),
  currentMonthRevenue: z.number().min(0).optional(),
  totalRevenue: z.number().min(0).optional(),
  // 新增项目管理字段
  logo: urlSchema,
  visitUrl: urlSchema,
  wikiUrl: urlSchema,
  gitUrl: urlSchema,
  healthStatus: projectHealthStatusEnum.optional(),
  version: z.string().max(50, "版本号不能超过50个字符").optional(),
  techStack: z.string().max(500, "技术栈描述不能超过500个字符").optional(),
});

// 项目成员管理输入验证
const addMemberSchema = z.object({
  projectId: z.string(),
  userId: z.string(),
  role: memberRoleEnum.default("other"),
  permissions: z.array(z.string()).default([]),
  profitRate: z.number().min(0).max(100).optional(), // 分成比例
});

// 项目里程碑创建输入验证
const createMilestoneSchema = z.object({
  projectId: z.string(),
  name: z.string().min(1, "里程碑名称不能为空").max(100, "里程碑名称不能超过100个字符"),
  description: z.string().max(1000, "里程碑描述不能超过1000个字符").optional(),
  priority: projectPriorityEnum.default("medium"),
  dueDate: z.date().optional(),
  assignedToId: z.string().optional(),
});

export const projectRouter = createTRPCRouter({
  // 创建项目
  create: protectedProcedure
    .input(createProjectSchema)
    .mutation(async ({ ctx, input }) => {
      // 检查用户状态
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { status: true },
      });

      if (!user || user.status !== "active") {
        throw new Error("用户状态不允许创建项目");
      }

      // 检查项目编码是否已存在
      const existingProject = await ctx.db.project.findUnique({
        where: { code: input.code },
      });

      if (existingProject) {
        throw new Error("项目编码已存在");
      }

      // 如果指定了父项目，检查父项目是否存在
      if (input.parentId) {
        const parentProject = await ctx.db.project.findUnique({
          where: { id: input.parentId },
        });
        if (!parentProject) {
          throw new Error("父项目不存在");
        }
      }

      // 如果指定了项目负责人，检查用户是否存在
      if (input.poUserId) {
        const poUser = await ctx.db.user.findUnique({
          where: { id: input.poUserId },
        });
        if (!poUser) {
          throw new Error("指定的项目负责人不存在");
        }
      }

      // 获取当前用户信息
      const currentUser = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { name: true },
      });

      const projectData = {
        id: `proj_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        name: input.name,
        code: input.code,
        description: input.description,
        type: input.type,
        status: input.status,
        category: input.category,
        priority: input.priority,
        startDate: input.startDate,
        endDate: input.endDate,
        deadline: input.deadline,
        budget: input.budget,
        parentId: input.parentId,
        tags: "[]", // 初始化为空数组
        // 新增项目管理字段
        logo: input.logo,
        visitUrl: input.visitUrl,
        wikiUrl: input.wikiUrl,
        gitUrl: input.gitUrl,
        healthStatus: input.healthStatus,
        isDeleted: false, // 默认未删除
        version: input.version,
        techStack: input.techStack,
        lastDeployAt: null, // 初始化为null
        createdBy: currentUser?.name || "",
        updatedBy: currentUser?.name || "",
        createdById: ctx.session.user.id,
        updatedById: ctx.session.user.id,
        // 如果没有指定负责人，默认为创建者
        poUserId: input.poUserId || ctx.session.user.id,
        updatedAt: new Date(),
      };

      const project = await ctx.db.project.create({
        data: projectData,
      });

      // 自动添加创建者为PO
      const memberData = {
        projectId: project.id,
        userId: ctx.session.user.id,
        role: "po",
        status: "active",
        permissions: "[]", // 初始化为空数组
        createdBy: currentUser?.name || "",
        updatedBy: currentUser?.name || "",
        createdById: ctx.session.user.id,
        updatedById: ctx.session.user.id,
        updatedAt: new Date(),
      };

      await ctx.db.projectMember.create({
        data: memberData,
      });

      return {
        ...project,
        tags: project.tags ? JSON.parse(project.tags) : [],
      };
    }),

  // 获取指定用户的项目列表（公开访问，权限控制显示内容）
  getUserProjects: protectedProcedure
    .input(
      z.object({
        userId: z.string(),
        page: z.number().min(1).default(1),
        limit: z.number().min(1).max(100).default(10),
      })
    )
    .query(async ({ ctx, input }) => {
      // 获取当前用户信息以判断权限
      const currentUser = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { id: true, role: true },
      });

      // 权限检查变量已定义但未使用，暂时保留以备将来使用
      // const isAdmin = currentUser && ["admin", "manager"].includes(currentUser.role);
      // const isOwnProfile = currentUser?.id === input.userId;

      const { userId, page, limit } = input;
      const skip = (page - 1) * limit;

      // 构建查询条件：根据权限显示不同项目
      const baseConditions = [
        { createdById: userId }, // 用户创建的项目
        {
          projectMember: {
            some: {
              userId: userId,
              status: "active",
            },
          },
        }, // 用户参与的项目
      ];

      const where: Prisma.projectWhereInput = {
        AND: [
          { OR: baseConditions },
          // 如果不是管理员且不是本人，只显示公开项目（这里假设所有项目都是公开的，可以根据需要添加isPublic字段）
          // 暂时所有项目都显示，后续可以根据需要添加隐私控制
        ],
      };

      const [projects, total] = await Promise.all([
        ctx.db.project.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: "desc" },
          include: {
            user_project_createdByIdTouser: {
              select: {
                id: true,
                name: true,
                avatar: true,
              },
            },
            projectMember: {
              where: { status: "active" },
              include: {
                user: {
                  select: {
                    id: true,
                    name: true,
                    avatar: true,
                  },
                },
              },
            },
            _count: {
              select: {
                projectMember: {
                  where: { status: "active" },
                },
              },
            },
          },
        }),
        ctx.db.project.count({ where }),
      ]);

      return {
        projects: projects.map(project => ({
          ...project,
          tags: project.tags ? JSON.parse(project.tags) : [],
          memberCount: project._count.projectMember,
          createdBy: project.user_project_createdByIdTouser?.name || "未知",
        })),
        total,
        pages: Math.ceil(total / limit),
        currentPage: page,
      };
    }),

  // 获取项目列表
  getProjects: protectedProcedure
    .input(
      z.object({
        page: z.number().min(1).default(1),
        limit: z.number().min(1).max(100).default(10),
        status: projectStatusEnum.optional(),

        search: z.string().optional(),
        myProjects: z.boolean().default(false), // 是否只显示我的项目
      })
    )
    .query(async ({ ctx, input }) => {
      const { page, limit, status, search } = input;
      const skip = (page - 1) * limit;

      const where: Prisma.projectWhereInput = {};
      if (status) where.status = status;

      // 搜索条件
      const searchConditions = search ? [
        { name: { contains: search } },
        { description: { contains: search } },
      ] : [];

      // 权限控制：只显示用户有权限访问的项目
      const accessConditions = [
        { createdById: ctx.session.user.id }, // 用户创建的项目
        {
          projectMember: {
            some: {
              userId: ctx.session.user.id,
              status: "active",
            },
          },
        }, // 用户是成员的项目
      ];

      // 如果有搜索条件，需要同时满足搜索和权限条件
      if (search) {
        where.AND = [
          { OR: searchConditions },
          { OR: accessConditions },
        ];
      } else {
        where.OR = accessConditions;
      }

      // myProjects 参数现在是多余的，因为我们已经只显示用户有权限的项目
      // 保留这个参数是为了向后兼容，但实际上不会改变查询结果

      const [projects, total] = await Promise.all([
        ctx.db.project.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: "desc" },
          include: {
            projectMember: {
              where: { status: "active" },
              include: {
                user: {
                  select: {
                    id: true,
                    name: true,
                    avatar: true,
                  },
                },
              },
            },
            _count: {
              select: {
                projectMember: {
                  where: { status: "active" },
                },
              },
            },
          },
        }),
        ctx.db.project.count({ where }),
      ]);

      return {
        projects: projects.map(project => ({
          ...project,
          tags: project.tags ? JSON.parse(project.tags) : [],
          memberCount: project._count.projectMember,
        })),
        total,
        pages: Math.ceil(total / limit),
        currentPage: page,
      };
    }),

  // 获取项目详情
  getProject: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const project = await ctx.db.project.findUnique({
        where: { id: input.id },
        include: {
          user_project_poUserIdTouser: {
            select: {
              id: true,
              name: true,
              avatar: true,
              email: true,
            },
          },
          projectMember: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  avatar: true,
                  email: true,
                },
              },
            },
            orderBy: { joinedAt: "asc" },
          },
          projectShare: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  avatar: true,
                },
              },
            },
          },
        },
      });

      if (!project) {
        throw new Error("项目不存在");
      }

      // 检查用户是否有权限查看项目
      const isMember = project.projectMember.some(
        member => member.userId === ctx.session.user.id && member.status === "active"
      );
      const isCreator = project.createdById === ctx.session.user.id;

      if (!isMember && !isCreator) {
        throw new Error("权限不足");
      }

      return {
        ...project,
        tags: project.tags ? JSON.parse(project.tags) : [],
        members: project.projectMember.map(member => ({
          ...member,
          permissions: member.permissions ? JSON.parse(member.permissions) : [],
        })),
        shares: project.projectShare.map(share => ({
          ...share,
          conditions: share.conditions ? JSON.parse(share.conditions) : {},
          rules: share.rules ? JSON.parse(share.rules) : {},
        })),
      };
    }),

  // 更新项目
  update: protectedProcedure
    .input(updateProjectSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, ...updateData } = input;

      // 检查权限
      const project = await ctx.db.project.findUnique({
        where: { id },
        include: {
          projectMember: {
            where: {
              userId: ctx.session.user.id,
              status: "active",
            },
          },
        },
      });

      if (!project) {
        throw new Error("项目不存在");
      }

      const isCreator = project.createdById === ctx.session.user.id;
      const isManager = project.projectMember.some(
        member => hasRolePermission(member.role, 'MANAGER_ROLES')
      );

      if (!isCreator && !isManager) {
        throw new Error("权限不足");
      }

      // 获取当前用户信息
      const currentUser = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { name: true },
      });

      const updateProjectData = {
        ...updateData,
        updatedBy: currentUser?.name || "",
        updatedById: ctx.session.user.id,
        updatedAt: new Date(),
      };

      const updatedProject = await ctx.db.project.update({
        where: { id },
        data: updateProjectData,
        include: {
          user_project_poUserIdTouser: {
            select: {
              id: true,
              name: true,
              avatar: true,
            },
          },
        },
      });

      return {
        ...updatedProject,
        tags: updatedProject.tags ? JSON.parse(updatedProject.tags) : [],
      };
    }),

  // 添加项目成员
  addMember: protectedProcedure
    .input(addMemberSchema)
    .mutation(async ({ ctx, input }) => {
      // 检查权限
      const project = await ctx.db.project.findUnique({
        where: { id: input.projectId },
        include: {
          projectMember: {
            where: {
              userId: ctx.session.user.id,
              status: "active",
            },
          },
        },
      });

      if (!project) {
        throw new Error("项目不存在");
      }

      const isCreator = project.createdById === ctx.session.user.id;
      const canManageMembers = project.projectMember.some(
        member => hasRolePermission(member.role, 'MEMBER_MANAGEMENT_ROLES')
      );

      if (!isCreator && !canManageMembers) {
        throw new Error("只有项目创建者、PO或平台角色才能添加成员");
      }

      // 检查用户是否已经是成员
      const existingMember = await ctx.db.projectMember.findUnique({
        where: {
          projectId_userId: {
            projectId: input.projectId,
            userId: input.userId,
          },
        },
      });

      if (existingMember) {
        throw new Error("用户已经是项目成员");
      }

      // 检查角色唯一性约束
      if (input.role === 'po' || input.role === 'platform') {
        const existingMemberWithRole = await ctx.db.projectMember.findFirst({
          where: {
            projectId: input.projectId,
            role: input.role,
            status: "active",
          },
        });

        if (existingMemberWithRole) {
          const roleLabel = input.role === 'po' ? '产品负责人' : '平台角色';
          throw new Error(`项目中已存在${roleLabel}，一个项目只能有一个${roleLabel}`);
        }
      }

      // 获取当前用户信息
      const currentUser = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { name: true },
      });

      const memberCreateData = {
        projectId: input.projectId,
        userId: input.userId,
        role: input.role,
        status: "active",
        permissions: JSON.stringify(input.permissions),
        profitRate: input.profitRate,
        createdBy: currentUser?.name || "",
        updatedBy: currentUser?.name || "",
        createdById: ctx.session.user.id,
        updatedById: ctx.session.user.id,
        updatedAt: new Date(),
      };

      // 如果添加的是PO角色，同时更新项目的poUserId字段
      if (input.role === 'po') {
        await ctx.db.project.update({
          where: { id: input.projectId },
          data: { poUserId: input.userId },
        });
      }

      const member = await ctx.db.projectMember.create({
        data: memberCreateData,
      });

      return {
        ...member,
        permissions: member.permissions ? JSON.parse(member.permissions) : [],
      };
    }),

  // 移除项目成员
  removeMember: protectedProcedure
    .input(
      z.object({
        projectId: z.string(),
        userId: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // 检查权限
      const project = await ctx.db.project.findUnique({
        where: { id: input.projectId },
        include: {
          projectMember: {
            where: {
              userId: ctx.session.user.id,
              status: "active",
            },
          },
        },
      });

      if (!project) {
        throw new Error("项目不存在");
      }

      const isCreator = project.createdById === ctx.session.user.id;
      const canManageMembers = project.projectMember.some(
        member => hasRolePermission(member.role, 'MEMBER_MANAGEMENT_ROLES')
      );

      if (!isCreator && !canManageMembers) {
        throw new Error("只有项目创建者、PO或平台角色才能移除成员");
      }

      // 不能移除项目创建者
      if (input.userId === project.createdById) {
        throw new Error("不能移除项目创建者");
      }

      // 检查要移除的成员是否是PO，如果是则需要清除项目的poUserId
      const memberToRemove = await ctx.db.projectMember.findUnique({
        where: {
          projectId_userId: {
            projectId: input.projectId,
            userId: input.userId,
          },
        },
      });

      if (memberToRemove?.role === 'po') {
        // 如果移除的是PO，将项目的poUserId设置为创建者
        await ctx.db.project.update({
          where: { id: input.projectId },
          data: { poUserId: project.createdById },
        });
      }

      await ctx.db.projectMember.delete({
        where: {
          projectId_userId: {
            projectId: input.projectId,
            userId: input.userId,
          },
        },
      });

      return { success: true };
    }),

  // 更新成员角色
  updateMemberRole: protectedProcedure
    .input(
      z.object({
        projectId: z.string(),
        userId: z.string(),
        role: memberRoleEnum,
      })
    )
    .mutation(async ({ ctx, input }) => {
      // 检查权限
      const project = await ctx.db.project.findUnique({
        where: { id: input.projectId },
        include: {
          projectMember: {
            where: {
              userId: ctx.session.user.id,
              status: "active",
            },
          },
        },
      });

      if (!project) {
        throw new Error("项目不存在");
      }

      const isCreator = project.createdById === ctx.session.user.id;
      const canManageMembers = project.projectMember.some(
        member => hasRolePermission(member.role, 'MEMBER_MANAGEMENT_ROLES')
      );

      if (!isCreator && !canManageMembers) {
        throw new Error("只有项目创建者、PO或平台角色才能修改成员角色");
      }

      // 检查角色唯一性约束
      if (input.role === 'po' || input.role === 'platform') {
        const existingMemberWithRole = await ctx.db.projectMember.findFirst({
          where: {
            projectId: input.projectId,
            role: input.role,
            status: "active",
            userId: { not: input.userId }, // 排除当前要更新的用户
          },
        });

        if (existingMemberWithRole) {
          const roleLabel = input.role === 'po' ? '产品负责人' : '平台角色';
          throw new Error(`项目中已存在${roleLabel}，一个项目只能有一个${roleLabel}`);
        }
      }

      // 如果设置为PO，同时更新项目的poUserId字段
      if (input.role === 'po') {
        await ctx.db.project.update({
          where: { id: input.projectId },
          data: { poUserId: input.userId },
        });
      }

      // 获取当前用户信息
      const currentUser = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { name: true },
      });

      const memberUpdateData = {
        role: input.role,
        updatedBy: currentUser?.name || "",
        updatedById: ctx.session.user.id,
        updatedAt: new Date(),
      };

      const member = await ctx.db.projectMember.update({
        where: {
          projectId_userId: {
            projectId: input.projectId,
            userId: input.userId,
          },
        },
        data: memberUpdateData,
      });

      return {
        ...member,
        permissions: member.permissions ? JSON.parse(member.permissions) : [],
      };
    }),

  // 更新成员分成比例
  updateMemberProfitRate: protectedProcedure
    .input(
      z.object({
        projectId: z.string(),
        userId: z.string(),
        profitRate: z.number().min(0).max(100),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // 检查权限
      const project = await ctx.db.project.findUnique({
        where: { id: input.projectId },
        include: {
          projectMember: {
            where: {
              userId: ctx.session.user.id,
              status: "active",
            },
          },
        },
      });

      if (!project) {
        throw new Error("项目不存在");
      }

      const isCreator = project.createdById === ctx.session.user.id;
      const canManageMembers = project.projectMember.some(
        member => hasRolePermission(member.role, 'MEMBER_MANAGEMENT_ROLES')
      );

      if (!isCreator && !canManageMembers) {
        throw new Error("只有项目创建者、PO或平台角色才能修改分成比例");
      }

      // 获取当前用户信息
      const currentUser = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { name: true },
      });

      const profitRateUpdateData = {
        profitRate: input.profitRate,
        updatedBy: currentUser?.name || "",
        updatedById: ctx.session.user.id,
        updatedAt: new Date(),
      };

      const member = await ctx.db.projectMember.update({
        where: {
          projectId_userId: {
            projectId: input.projectId,
            userId: input.userId,
          },
        },
        data: profitRateUpdateData,
      });

      return {
        ...member,
        permissions: member.permissions ? JSON.parse(member.permissions) : [],
      };
    }),

  // 创建分成记录
  createProfitShare: protectedProcedure
    .input(
      z.object({
        projectId: z.string(),
        userId: z.string(),
        shareType: z.enum(["equity", "revenue", "bonus", "commission"]),
        percentage: z.number().min(0).max(100),
        amount: z.number().optional(),
        period: z.enum(["monthly", "quarterly", "yearly", "one_time"]),
        startDate: z.date(),
        endDate: z.date().optional(),
        conditions: z.string().optional(),
        rules: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // 检查权限
      const project = await ctx.db.project.findUnique({
        where: { id: input.projectId },
        include: {
          projectMember: {
            where: {
              userId: ctx.session.user.id,
              status: "active",
            },
          },
        },
      });

      if (!project) {
        throw new Error("项目不存在");
      }

      const isCreator = project.createdById === ctx.session.user.id;
      const isManager = project.projectMember.some(
        member => hasRolePermission(member.role, 'MANAGER_ROLES')
      );

      if (!isCreator && !isManager) {
        throw new Error("权限不足");
      }

      // 检查用户是否是项目成员
      const targetMember = await ctx.db.projectMember.findUnique({
        where: {
          projectId_userId: {
            projectId: input.projectId,
            userId: input.userId,
          },
        },
      });

      if (!targetMember || targetMember.status !== "active") {
        throw new Error("目标用户不是项目成员");
      }

      // 获取当前用户信息
      const currentUser = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { name: true },
      });

      const shareCreateData = {
        projectId: input.projectId,
        userId: input.userId,
        shareType: input.shareType,
        percentage: input.percentage,
        amount: input.amount,
        period: input.period,
        startDate: input.startDate,
        endDate: input.endDate,
        conditions: input.conditions ? JSON.stringify(input.conditions) : null,
        rules: input.rules ? JSON.stringify(input.rules) : null,
        status: "active",
        createdBy: currentUser?.name || "",
        updatedBy: currentUser?.name || "",
        createdById: ctx.session.user.id,
        updatedById: ctx.session.user.id,
        updatedAt: new Date(),
      };

      const share = await ctx.db.projectShare.create({
        data: shareCreateData,
      });

      return {
        ...share,
        conditions: share.conditions ? JSON.parse(share.conditions) : {},
        rules: share.rules ? JSON.parse(share.rules) : {},
      };
    }),

  // 删除项目
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // 检查权限
      const project = await ctx.db.project.findUnique({
        where: { id: input.id },
      });

      if (!project) {
        throw new Error("项目不存在");
      }

      if (project.createdById !== ctx.session.user.id) {
        throw new Error("只有项目创建者可以删除项目");
      }

      // 删除相关数据
      await ctx.db.$transaction([
        ctx.db.projectShare.deleteMany({
          where: { projectId: input.id },
        }),
        ctx.db.projectMember.deleteMany({
          where: { projectId: input.id },
        }),
        ctx.db.project.delete({
          where: { id: input.id },
        }),
      ]);

      return { success: true };
    }),

  // 创建项目里程碑
  createMilestone: protectedProcedure
    .input(createMilestoneSchema)
    .mutation(async ({ ctx, input }) => {
      // 检查项目权限
      const project = await ctx.db.project.findUnique({
        where: { id: input.projectId },
        include: {
          projectMember: {
            where: {
              userId: ctx.session.user.id,
              status: "active",
            },
          },
        },
      });

      if (!project) {
        throw new Error("项目不存在");
      }

      const isCreator = project.createdById === ctx.session.user.id;
      const isManager = project.projectMember.some(
        member => hasRolePermission(member.role, 'MANAGER_ROLES')
      );

      if (!isCreator && !isManager) {
        throw new Error("权限不足");
      }

      // 如果指定了负责人，检查用户是否存在且是项目成员
      if (input.assignedToId) {
        const assignedUser = await ctx.db.projectMember.findUnique({
          where: {
            projectId_userId: {
              projectId: input.projectId,
              userId: input.assignedToId,
            },
          },
        });
        if (!assignedUser || assignedUser.status !== "active") {
          throw new Error("指定的负责人不是项目成员");
        }
      }

      // 获取当前用户信息
      const currentUser = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { name: true },
      });

      const milestoneCreateData = {
        projectId: input.projectId,
        name: input.name,
        description: input.description,
        priority: input.priority,
        dueDate: input.dueDate,
        assignedToId: input.assignedToId,
        createdBy: currentUser?.name || "",
        updatedBy: currentUser?.name || "",
        createdById: ctx.session.user.id,
        updatedById: ctx.session.user.id,
        updatedAt: new Date(),
      };

      const milestone = await ctx.db.projectMilestone.create({
        data: milestoneCreateData,
      });

      return milestone;
    }),

  // 获取项目里程碑列表
  getMilestones: protectedProcedure
    .input(z.object({
      projectId: z.string(),
      status: z.enum(["pending", "in_progress", "completed", "cancelled"]).optional(),
    }))
    .query(async ({ ctx, input }) => {
      // 检查项目权限
      const project = await ctx.db.project.findUnique({
        where: { id: input.projectId },
        include: {
          projectMember: {
            where: {
              userId: ctx.session.user.id,
              status: "active",
            },
          },
        },
      });

      if (!project) {
        throw new Error("项目不存在");
      }

      const isCreator = project.createdById === ctx.session.user.id;
      const isMember = project.projectMember.length > 0;

      if (!isCreator && !isMember) {
        throw new Error("权限不足");
      }

      const where: Prisma.projectMilestoneWhereInput = { projectId: input.projectId };
      if (input.status) {
        where.status = input.status;
      }

      const milestones = await ctx.db.projectMilestone.findMany({
        where,
        orderBy: [
          { status: "asc" },
          { dueDate: "asc" },
          { createdAt: "desc" },
        ],
        // 暂时不包含关联数据，避免类型问题
      });

      return milestones;
    }),

  // 获取项目层级关系
  getProjectHierarchy: protectedProcedure
    .input(z.object({ projectId: z.string() }))
    .query(async ({ ctx, input }) => {
      // 检查项目权限
      const project = await ctx.db.project.findUnique({
        where: { id: input.projectId },
        include: {
          projectMember: {
            where: {
              userId: ctx.session.user.id,
              status: "active",
            },
          },
        },
      });

      if (!project) {
        throw new Error("项目不存在");
      }

      const isCreator = project.createdById === ctx.session.user.id;
      const isMember = project.projectMember.length > 0;

      if (!isCreator && !isMember) {
        throw new Error("权限不足");
      }

      // 获取项目的完整层级关系
      const relatedProjectIds = new Set<string>();

      // 1. 添加当前项目
      relatedProjectIds.add(input.projectId);

      // 2. 获取所有父项目链
      let currentProject = project;
      while (currentProject.parentId) {
        relatedProjectIds.add(currentProject.parentId);
        const parentProject = await ctx.db.project.findUnique({
          where: { id: currentProject.parentId },
          include: {
            projectMember: {
              where: { status: "active" },
            },
          },
        });
        if (!parentProject) break;
        currentProject = parentProject;
      }

      // 3. 获取所有子项目（递归）
      const getChildProjects = async (parentId: string): Promise<void> => {
        const children = await ctx.db.project.findMany({
          where: { parentId },
          select: { id: true, parentId: true },
        });

        for (const child of children) {
          if (!relatedProjectIds.has(child.id)) {
            relatedProjectIds.add(child.id);
            await getChildProjects(child.id);
          }
        }
      };

      await getChildProjects(input.projectId);

      // 4. 获取所有相关项目的详细信息
      const relatedProjects = await ctx.db.project.findMany({
        where: {
          id: { in: Array.from(relatedProjectIds) },
        },
        include: {
          projectMember: {
            where: { status: "active" },
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  avatar: true,
                },
              },
            },
          },
          user_project_poUserIdTouser: {
            select: {
              id: true,
              name: true,
              avatar: true,
            },
          },
          user_project_createdByIdTouser: {
            select: {
              id: true,
              name: true,
              avatar: true,
            },
          },
          _count: {
            select: {
              projectMember: {
                where: { status: "active" },
              },
            },
          },
        },
        orderBy: { createdAt: "desc" },
      });

      return {
        projects: relatedProjects.map(proj => ({
          ...proj,
          tags: proj.tags ? JSON.parse(proj.tags) : [],
          memberCount: proj._count.projectMember,
          // 添加一些计算字段用于显示
          currentMonthRevenue: proj.currentMonthRevenue || 0,
          totalRevenue: proj.totalRevenue || 0,
          createdBy: proj.user_project_createdByIdTouser,
          poUser: proj.user_project_poUserIdTouser,
        })),
        currentProjectId: input.projectId,
      };
    }),

  // 更新里程碑状态
  updateMilestoneStatus: protectedProcedure
    .input(z.object({
      id: z.string(),
      status: z.enum(["pending", "in_progress", "completed", "cancelled"]),
    }))
    .mutation(async ({ ctx, input }) => {
      const milestone = await ctx.db.projectMilestone.findUnique({
        where: { id: input.id },
        include: {
          project: {
            include: {
              projectMember: {
                where: {
                  userId: ctx.session.user.id,
                  status: "active",
                },
              },
            },
          },
        },
      });

      if (!milestone) {
        throw new Error("里程碑不存在");
      }

      const isCreator = milestone.project.createdById === ctx.session.user.id;
      const isAssigned = milestone.assignedToId === ctx.session.user.id;
      const isManager = milestone.project.projectMember.some(
        member => hasRolePermission(member.role, 'MANAGER_ROLES')
      );

      if (!isCreator && !isAssigned && !isManager) {
        throw new Error("权限不足");
      }

      // 获取当前用户信息
      const currentUser = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { name: true },
      });

      const milestoneUpdateData = {
        status: input.status,
        completedAt: input.status === "completed" ? new Date() : null,
        updatedBy: currentUser?.name || "",
        updatedById: ctx.session.user.id,
        updatedAt: new Date(),
      };

      const updatedMilestone = await ctx.db.projectMilestone.update({
        where: { id: input.id },
        data: milestoneUpdateData,
      });

      return updatedMilestone;
    }),
});
