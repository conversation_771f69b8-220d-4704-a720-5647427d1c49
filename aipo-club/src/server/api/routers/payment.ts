import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { createExpressAccount, createAccountLink, createTransfer } from "~/lib/stripe";
import { TRPCError } from "@trpc/server";

export const paymentRouter = createTRPCRouter({
  // 获取用户支付账户
  getPaymentAccounts: protectedProcedure.query(async ({ ctx }) => {
    return await ctx.db.paymentAccount.findMany({
      where: { userId: ctx.session.user.id },
      orderBy: { createdAt: "desc" },
    });
  }),

  // 添加支付账户
  addPaymentAccount: protectedProcedure
    .input(
      z.object({
        provider: z.enum(["stripe", "paypal", "alipay", "wechat", "bank"]),
        accountEmail: z.string().email().optional(),
        accountName: z.string().optional(),
        isDefault: z.boolean().default(false),
      })
    )
    .mutation(async ({ ctx, input }) => {
      let accountId: string | undefined;
      let isVerified = false;

      // 如果是 Stripe，创建快速账户
      if (input.provider === "stripe") {
        try {
          const account = await createExpressAccount(
            input.accountEmail ?? ctx.session.user.email!,
            { userId: ctx.session.user.id }
          );
          accountId = account.id;
          isVerified = false; // 需要完成 onboarding
        } catch {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to create Stripe account",
          });
        }
      }

      // 如果设置为默认，先取消其他默认账户
      if (input.isDefault) {
        await ctx.db.paymentAccount.updateMany({
          where: { userId: ctx.session.user.id },
          data: { isDefault: false },
        });
      }

      return await ctx.db.paymentAccount.create({
        data: {
          userId: ctx.session.user.id,
          provider: input.provider,
          accountId,
          accountEmail: input.accountEmail,
          accountName: input.accountName,
          isDefault: input.isDefault,
          isVerified,
          updatedAt: new Date(),
        },
      });
    }),

  // 创建 Stripe 账户链接
  createStripeAccountLink: protectedProcedure
    .input(
      z.object({
        paymentAccountId: z.string(),
        refreshUrl: z.string().url(),
        returnUrl: z.string().url(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const paymentAccount = await ctx.db.paymentAccount.findFirst({
        where: {
          id: input.paymentAccountId,
          userId: ctx.session.user.id,
          provider: "stripe",
        },
      });

      if (!paymentAccount?.accountId) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Stripe account not found",
        });
      }

      try {
        const accountLink = await createAccountLink(
          paymentAccount.accountId,
          input.refreshUrl,
          input.returnUrl
        );
        return { url: accountLink.url };
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create account link",
        });
      }
    }),

  // 记录项目收入
  recordProjectRevenue: protectedProcedure
    .input(
      z.object({
        projectId: z.string(),
        amount: z.number().positive(),
        currency: z.string().default("CNY"),
        source: z.string().default("manual"),
        sourceId: z.string().optional(),
        description: z.string().optional(),
        period: z.string().default("monthly"),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // 验证用户是否有权限记录该项目的收入
      const projectMember = await ctx.db.projectMember.findFirst({
        where: {
          projectId: input.projectId,
          userId: ctx.session.user.id,
          role: { in: ["owner", "admin"] },
          status: "active",
        },
      });

      if (!projectMember) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You don't have permission to record revenue for this project",
        });
      }

      // 记录收入
      const revenue = await ctx.db.projectRevenue.create({
        data: {
          projectId: input.projectId,
          amount: input.amount,
          currency: input.currency,
          source: input.source,
          sourceId: input.sourceId,
          description: input.description,
          period: input.period,
          createdBy: ctx.session.user.id,
          updatedAt: new Date(),
        },
      });

      // 更新项目总收入
      await ctx.db.project.update({
        where: { id: input.projectId },
        data: {
          totalRevenue: {
            increment: input.amount,
          },
          currentMonthRevenue: {
            increment: input.amount,
          },
        },
      });

      return revenue;
    }),

  // 获取项目收入记录
  getProjectRevenues: protectedProcedure
    .input(
      z.object({
        projectId: z.string(),
        limit: z.number().default(10),
        offset: z.number().default(0),
      })
    )
    .query(async ({ ctx, input }) => {
      // 验证用户是否有权限查看该项目
      const projectMember = await ctx.db.projectMember.findFirst({
        where: {
          projectId: input.projectId,
          userId: ctx.session.user.id,
          status: "active",
        },
      });

      if (!projectMember) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You don't have permission to view this project's revenues",
        });
      }

      return await ctx.db.projectRevenue.findMany({
        where: { projectId: input.projectId },
        orderBy: { recordedAt: "desc" },
        take: input.limit,
        skip: input.offset,
        include: {
          user: {
            select: {
              name: true,
              avatar: true,
            },
          },
        },
      });
    }),

  // 触发利润分配
  triggerProfitDistribution: protectedProcedure
    .input(
      z.object({
        projectId: z.string(),
        periodStart: z.date(),
        periodEnd: z.date(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // 验证权限
      const projectMember = await ctx.db.projectMember.findFirst({
        where: {
          projectId: input.projectId,
          userId: ctx.session.user.id,
          role: { in: ["owner", "admin"] },
          status: "active",
        },
      });

      if (!projectMember) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You don't have permission to distribute profits for this project",
        });
      }

      // 获取项目分成规则
      const shares = await ctx.db.projectShare.findMany({
        where: {
          projectId: input.projectId,
          status: "active",
          startDate: { lte: input.periodEnd },
          OR: [
            { endDate: null },
            { endDate: { gte: input.periodStart } },
          ],
        },
        include: {
          user: true,
        },
      });

      if (shares.length === 0) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "No active profit sharing rules found for this project",
        });
      }

      // 计算期间内的总收入
      const totalRevenue = await ctx.db.projectRevenue.aggregate({
        where: {
          projectId: input.projectId,
          recordedAt: {
            gte: input.periodStart,
            lte: input.periodEnd,
          },
        },
        _sum: {
          amount: true,
        },
      });

      const revenue = totalRevenue._sum.amount ?? 0;
      if (revenue <= 0) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "No revenue found for the specified period",
        });
      }

      // 创建分成支付记录
      const payouts = [];
      for (const share of shares) {
        const amount = revenue * (share.percentage / 100);
        if (amount > 0) {
          const payout = await ctx.db.sharePayout.create({
            data: {
              shareId: share.id,
              amount,
              currency: "CNY",
              periodStart: input.periodStart,
              periodEnd: input.periodEnd,
              status: "pending",
              createdBy: ctx.session.user.name ?? "System",
              updatedBy: ctx.session.user.name ?? "System",
              createdById: ctx.session.user.id,
              updatedById: ctx.session.user.id,
              updatedAt: new Date(),
            },
          });
          payouts.push(payout);
        }
      }

      return {
        totalRevenue: revenue,
        payoutsCreated: payouts.length,
        payouts,
      };
    }),

  // 基于真实财务数据的智能利润分配
  smartProfitDistribution: protectedProcedure
    .input(
      z.object({
        projectId: z.string(),
        periodStart: z.date(),
        periodEnd: z.date(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // 验证权限
      const projectMember = await ctx.db.projectMember.findFirst({
        where: {
          projectId: input.projectId,
          userId: ctx.session.user.id,
          status: "active",
          role: { in: ["owner", "admin"] },
        },
      });

      if (!projectMember) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "您没有权限执行此项目的利润分配",
        });
      }

      // 获取项目分成规则
      const shares = await ctx.db.projectShare.findMany({
        where: {
          projectId: input.projectId,
          status: "active",
          startDate: { lte: input.periodEnd },
          OR: [
            { endDate: null },
            { endDate: { gte: input.periodStart } },
          ],
        },
        include: {
          user: true,
        },
      });

      if (shares.length === 0) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "未找到有效的分成规则",
        });
      }

      // 获取期间内的收入交易
      const incomeTransactions = await ctx.db.financialTransaction.findMany({
        where: {
          projectId: input.projectId,
          type: "income",
          status: "confirmed",
          transactionDate: {
            gte: input.periodStart,
            lte: input.periodEnd,
          },
        },
      });

      // 获取期间内的支出交易
      const expenseTransactions = await ctx.db.financialTransaction.findMany({
        where: {
          projectId: input.projectId,
          type: "expense",
          status: "confirmed",
          transactionDate: {
            gte: input.periodStart,
            lte: input.periodEnd,
          },
        },
        include: {
          account: true,
        },
      });

      // 计算财务摘要
      const totalIncome = incomeTransactions.reduce((sum, t) => sum + t.amount, 0);
      const totalExpense = expenseTransactions.reduce((sum, t) => sum + t.amount, 0);
      const netProfit = totalIncome - totalExpense;

      if (totalIncome <= 0) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "指定期间内没有收入记录",
        });
      }

      if (netProfit <= 0) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "指定期间内没有利润，无法进行分成",
        });
      }

      // 计算平台手续费 (5%)
      const platformFeeRate = 0.05;
      const platformFee = netProfit * platformFeeRate;
      const distributableProfit = netProfit - platformFee;

      // 创建分成支付记录
      const payouts = [];
      let totalDistributed = 0;

      for (const share of shares) {
        let amount = 0;
        let calculation = "";

        // 根据分成类型计算金额
        switch (share.shareType) {
          case "equity":
          case "profit":
            // 基于净利润分成
            amount = distributableProfit * (share.percentage / 100);
            calculation = `净利润分成: ${distributableProfit} × ${share.percentage}% = ${amount}`;
            break;
          case "revenue":
            // 基于收入分成
            amount = totalIncome * (share.percentage / 100);
            calculation = `收入分成: ${totalIncome} × ${share.percentage}% = ${amount}`;
            break;
          case "bonus":
            // 奖金类型，利润率达到10%才发放
            const profitMargin = (netProfit / totalIncome) * 100;
            if (profitMargin >= 10) {
              amount = distributableProfit * (share.percentage / 100);
              calculation = `奖金分成(利润率${profitMargin.toFixed(1)}%≥10%): ${distributableProfit} × ${share.percentage}% = ${amount}`;
            } else {
              calculation = `奖金分成未达标(利润率${profitMargin.toFixed(1)}%<10%): 0`;
            }
            break;
          case "commission":
            // 佣金基于收入
            amount = totalIncome * (share.percentage / 100);
            calculation = `佣金分成: ${totalIncome} × ${share.percentage}% = ${amount}`;
            break;
          default:
            amount = distributableProfit * (share.percentage / 100);
            calculation = `默认利润分成: ${distributableProfit} × ${share.percentage}% = ${amount}`;
        }

        if (amount > 0) {
          const payout = await ctx.db.sharePayout.create({
            data: {
              shareId: share.id,
              amount,
              currency: "CNY",
              periodStart: input.periodStart,
              periodEnd: input.periodEnd,
              status: "pending",
              createdBy: ctx.session.user.name ?? "System",
              updatedBy: ctx.session.user.name ?? "System",
              createdById: ctx.session.user.id,
              updatedById: ctx.session.user.id,
              notes: `智能分成计算 - ${calculation}`,
              updatedAt: new Date(),
            },
          });
          payouts.push(payout);
          totalDistributed += amount;
        }
      }

      // 记录平台手续费
      if (platformFee > 0) {
        await ctx.db.platformTransaction.create({
          data: {
            type: "commission",
            amount: platformFee,
            currency: "CNY",
            description: `项目利润分配平台手续费 - ${input.periodStart.toISOString().split('T')[0]} 至 ${input.periodEnd.toISOString().split('T')[0]}`,
            relatedId: input.projectId,
            relatedType: "project",
            metadata: JSON.stringify({
              totalIncome,
              totalExpense,
              netProfit,
              platformFeeRate,
              payoutsCount: payouts.length,
              totalDistributed,
            }),
          },
        });
      }

      return {
        summary: {
          totalIncome,
          totalExpense,
          netProfit,
          profitMargin: (netProfit / totalIncome) * 100,
          platformFee,
          distributableProfit,
          totalDistributed,
          remainingProfit: distributableProfit - totalDistributed,
        },
        payouts: payouts.map(p => ({
          ...p,
          userName: shares.find(s => s.id === p.shareId)?.user.name,
          shareType: shares.find(s => s.id === p.shareId)?.shareType,
          percentage: shares.find(s => s.id === p.shareId)?.percentage,
        })),
        payoutsCreated: payouts.length,
      };
    }),

  // 执行实际支付
  processPayout: protectedProcedure
    .input(
      z.object({
        payoutId: z.string(),
        paymentAccountId: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // 获取分成支付记录
      const sharePayout = await ctx.db.sharePayout.findUnique({
        where: { id: input.payoutId },
        include: {
          projectShare: {
            include: {
              project: true,
              user: true,
            },
          },
        },
      });

      if (!sharePayout) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Payout not found",
        });
      }

      if (sharePayout.status !== "pending") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Payout has already been processed",
        });
      }

      // 获取支付账户
      const paymentAccount = await ctx.db.paymentAccount.findFirst({
        where: {
          id: input.paymentAccountId,
          userId: sharePayout.projectShare.userId,
        },
      });

      if (!paymentAccount) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Payment account not found",
        });
      }

      let stripePaymentId: string | undefined;
      let status: "processing" | "completed" | "failed" = "processing";
      let failureReason: string | undefined;

      // 如果是 Stripe 账户，执行转账
      if (paymentAccount.provider === "stripe" && paymentAccount.accountId) {
        try {
          const transfer = await createTransfer(
            sharePayout.amount,
            paymentAccount.accountId,
            {
              projectId: sharePayout.projectShare.projectId,
              sharePayoutId: sharePayout.id,
              userId: sharePayout.projectShare.userId,
            }
          );
          stripePaymentId = transfer.id;
          status = "completed";
        } catch (error) {
          status = "failed";
          failureReason = error instanceof Error ? error.message : "Unknown error";
        }
      }

      // 创建支付记录
      const payout = await ctx.db.payout.create({
        data: {
          sharePayoutId: input.payoutId,
          paymentAccountId: input.paymentAccountId,
          amount: sharePayout.amount,
          currency: sharePayout.currency,
          status,
          stripePaymentId,
          failureReason,
          processedAt: status === "completed" ? new Date() : null,
          updatedAt: new Date(),
        },
      });

      // 更新分成支付状态
      if (status === "completed") {
        await ctx.db.sharePayout.update({
          where: { id: input.payoutId },
          data: {
            status: "paid",
            paidAt: new Date(),
            paymentMethod: paymentAccount.provider,
            paymentInfo: JSON.stringify({
              provider: paymentAccount.provider,
              accountId: paymentAccount.accountId,
              stripePaymentId,
            }),
          },
        });
      }

      return payout;
    }),

  // 获取待支付记录
  getPendingPayouts: protectedProcedure.query(async ({ ctx }) => {
    return await ctx.db.sharePayout.findMany({
      where: {
        status: "pending",
        projectShare: {
          userId: ctx.session.user.id,
        },
      },
      include: {
        projectShare: {
          include: {
            project: {
              select: {
                name: true,
                logo: true,
              },
            },
          },
        },
      },
      orderBy: { createdAt: "desc" },
    });
  }),

  // 获取支付历史
  getPayoutHistory: protectedProcedure
    .input(
      z.object({
        limit: z.number().default(10),
        offset: z.number().default(0),
      })
    )
    .query(async ({ ctx, input }) => {
      return await ctx.db.payout.findMany({
        where: {
          sharePayout: {
            projectShare: {
              userId: ctx.session.user.id,
            },
          },
        },
        include: {
          sharePayout: {
            include: {
              projectShare: {
                include: {
                  project: {
                    select: {
                      name: true,
                      logo: true,
                    },
                  },
                },
              },
            },
          },
          paymentAccount: {
            select: {
              provider: true,
              accountEmail: true,
              accountName: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
        take: input.limit,
        skip: input.offset,
      });
    }),
});