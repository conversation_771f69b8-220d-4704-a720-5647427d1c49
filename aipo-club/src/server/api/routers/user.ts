import { z } from "zod";
import bcrypt from "bcryptjs";
import crypto from "crypto";
import type { Prisma } from "@prisma/client";

import {
  createTRPCRouter,
  protectedProcedure,
  publicProcedure,
} from "~/server/api/trpc";
import {
  sendWelcomeEmail,
  sendAccountActivatedEmail,
  sendPasswordResetEmail
} from "~/server/email";

// 用户注册输入验证
const registerSchema = z.object({
  name: z.string().min(1, "昵称不能为空").max(50, "昵称不能超过50个字符"),
  realname: z.string().min(1, "姓名不能为空").max(50, "姓名不能超过50个字符"),
  email: z.string().email("邮箱格式不正确"),
  phone: z.string().regex(/^1[3-9]\d{9}$/, "手机号格式不正确"),
  password: z.string().min(6, "密码至少6个字符"),
  bio: z.string().max(500, "个人简介不能超过500个字符").optional(),
  inviteCode: z.string().min(6, "邀请码至少6个字符").max(20, "邀请码不能超过20个字符").optional(),
});

// 用户资料更新输入验证
const updateProfileSchema = z.object({
  name: z.string().min(1).max(50).optional(),
  realname: z.string().min(1).max(50).optional(),
  avatar: z.string().url().optional().or(z.literal("")),
  birthday: z.date().optional(),
  gender: z.enum(["male", "female", "unknown"]).optional(),
  country: z.string().max(50).optional().or(z.literal("")),
  city: z.string().max(50).optional().or(z.literal("")),
  bio: z.string().max(500).optional().or(z.literal("")),
  occupation: z.string().max(100).optional().or(z.literal("")),
  education: z.string().max(500).optional().or(z.literal("")),
  github: z.string().url().optional().or(z.literal("")),
  linkedin: z.string().url().optional().or(z.literal("")),
  twitter: z.string().url().optional().or(z.literal("")),
  website: z.string().url().optional().or(z.literal("")),
  skills: z.string().max(500).optional().or(z.literal("")),
  interests: z.string().max(500).optional().or(z.literal("")),
});

export const userRouter = createTRPCRouter({
  // 用户注册
  register: publicProcedure
    .input(registerSchema)
    .mutation(async ({ ctx, input }) => {
      // 检查邮箱是否已存在
      const existingUserByEmail = await ctx.db.user.findUnique({
        where: { email: input.email },
      });
      if (existingUserByEmail) {
        throw new Error("邮箱已被注册");
      }

      // 检查手机号是否已存在
      const existingUserByPhone = await ctx.db.user.findUnique({
        where: { phone: input.phone },
      });
      if (existingUserByPhone) {
        throw new Error("手机号已被注册");
      }

      // 验证邀请码（如果提供）
      let inviterUserId: string | null = null;
      let inviteCodeRecord = null;

      if (input.inviteCode) {
        // 查找邀请码
        inviteCodeRecord = await ctx.db.inviteCode.findUnique({
          where: { code: input.inviteCode },
          include: { creator: true },
        });

        if (!inviteCodeRecord) {
          throw new Error("邀请码不存在");
        }

        if (!inviteCodeRecord.isActive) {
          throw new Error("邀请码已失效");
        }

        if (inviteCodeRecord.expiresAt && inviteCodeRecord.expiresAt < new Date()) {
          throw new Error("邀请码已过期");
        }

        if (inviteCodeRecord.usedCount >= inviteCodeRecord.maxUses) {
          throw new Error("邀请码使用次数已达上限");
        }

        inviterUserId = inviteCodeRecord.createdBy;
      }

      // 加密密码
      const hashedPassword = await bcrypt.hash(input.password, 12);

      // 创建用户
      const user = await ctx.db.user.create({
        data: {
          name: input.name.trim(),
          realname: input.realname.trim(),
          email: input.email.toLowerCase(),
          phone: input.phone,
          password: hashedPassword,
          bio: input.bio?.trim() || null,
          inviteCode: input.inviteCode || null,
          invitedBy: inviterUserId,
          status: "pending", // 新用户需要审核
          tags: "[]", // 初始化为空数组
          updatedAt: new Date(),
        },
        select: {
          id: true,
          name: true,
          realname: true,
          email: true,
          status: true,
          createdAt: true,
        },
      });

      // 如果使用了邀请码，记录使用情况
      if (inviteCodeRecord) {
        await ctx.db.$transaction([
          // 更新邀请码使用次数
          ctx.db.inviteCode.update({
            where: { id: inviteCodeRecord.id },
            data: { usedCount: { increment: 1 } },
          }),
          // 记录邀请码使用记录
          ctx.db.inviteCodeUsage.create({
            data: {
              inviteCodeId: inviteCodeRecord.id,
              userId: user.id,
            },
          }),
        ]);
      }

      // 发送欢迎邮件
      try {
        await sendWelcomeEmail(user.email, user.name);
      } catch (error) {
        console.error("发送欢迎邮件失败:", error);
        // 不影响注册流程，只记录错误
      }

      return user;
    }),

  // 获取当前用户信息
  getProfile: protectedProcedure.query(async ({ ctx }) => {
    const user = await ctx.db.user.findUnique({
      where: { id: ctx.session.user.id },
      select: {
        id: true,
        name: true,
        realname: true,
        email: true,
        phone: true,
        avatar: true,
        birthday: true,
        gender: true,
        country: true,
        city: true,
        bio: true,
        occupation: true,
        education: true,
        level: true,
        subscriptionStatus: true,
        subscriptionExpiry: true,
        inviteCode: true,
        invitedBy: true,
        github: true,
        linkedin: true,
        twitter: true,
        website: true,
        skills: true,
        interests: true,
        role: true,
        status: true,
        tags: true,
        joinDate: true,
        lastLoginAt: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!user) {
      throw new Error("用户不存在");
    }

    return {
      ...user,
      tags: user.tags ? JSON.parse(user.tags) : [],
    };
  }),

  // 更新用户资料
  updateProfile: protectedProcedure
    .input(updateProfileSchema)
    .mutation(async ({ ctx, input }) => {
      const user = await ctx.db.user.update({
        where: { id: ctx.session.user.id },
        data: {
          ...input,
          updatedAt: new Date(),
        },
        select: {
          id: true,
          name: true,
          realname: true,
          email: true,
          avatar: true,
          updatedAt: true,
        },
      });

      return user;
    }),

  // 获取公开用户列表（所有用户可访问，权限控制显示内容）
  getPublicUsers: protectedProcedure
    .input(
      z.object({
        page: z.number().min(1).default(1),
        limit: z.number().min(1).max(50).default(12),
        search: z.string().optional(),
        skills: z.string().optional(),
        location: z.string().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      const { page, limit, search, skills } = input;
      const skip = (page - 1) * limit;

      // 获取当前用户信息以判断权限
      const currentUser = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      const isAdmin = currentUser && ["admin", "manager"].includes(currentUser.role);

      const where: Prisma.userWhereInput = {
        status: "active", // 只显示活跃用户
      };

      if (search) {
        where.OR = [
          { name: { contains: search } },
          { bio: { contains: search } },
        ];
      }

      if (skills) {
        where.skills = { contains: skills };
      }

      // 基础字段选择（所有用户可见）
      const baseSelect = {
        id: true,
        name: true,
        avatar: true,
        bio: true,
        skills: true,
        role: true,
        createdAt: true,
      };

      // 管理员可见额外字段
      const adminSelect = isAdmin ? {
        ...baseSelect,
        email: true,
        realname: true,
        status: true,
        lastLoginAt: true,
      } : baseSelect;

      const [users, total] = await Promise.all([
        ctx.db.user.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: "desc" },
          select: adminSelect,
        }),
        ctx.db.user.count({ where }),
      ]);

      return {
        users: users.map(user => ({
          ...user,
          skills: user.skills ? user.skills.split(',').map(s => s.trim()).filter(Boolean) : [],
        })),
        total,
        pages: Math.ceil(total / limit),
        currentPage: page,
      };
    }),

  // 获取用户列表（管理员功能）
  getUsers: protectedProcedure
    .input(
      z.object({
        page: z.number().min(1).default(1),
        limit: z.number().min(1).max(100).default(10),
        status: z.enum(["pending", "active", "inactive", "rejected"]).optional(),
        role: z.enum(["admin", "manager", "member"]).optional(),
        search: z.string().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      // 检查权限
      const currentUser = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (!currentUser || !["admin", "manager"].includes(currentUser.role)) {
        throw new Error("权限不足");
      }

      const { page, limit, status, role, search } = input;
      const skip = (page - 1) * limit;

      const where: Prisma.userWhereInput = {};
      if (status) where.status = status;
      if (role) where.role = role;
      if (search) {
        where.OR = [
          { name: { contains: search } },
          { email: { contains: search } },
        ];
      }

      const [users, total] = await Promise.all([
        ctx.db.user.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: "desc" },
          select: {
            id: true,
            name: true,
            realname: true,
            email: true,
            avatar: true,
            level: true,
            subscriptionStatus: true,
            subscriptionExpiry: true,
            role: true,
            status: true,
            tags: true,
            joinDate: true,
            lastLoginAt: true,
            createdAt: true,
          },
        }),
        ctx.db.user.count({ where }),
      ]);

      return {
        users: users.map(user => ({
          ...user,
          tags: user.tags ? JSON.parse(user.tags) : [],
        })),
        total,
        pages: Math.ceil(total / limit),
        currentPage: page,
      };
    }),

  // 更新用户状态（管理员功能）
  updateUserStatus: protectedProcedure
    .input(
      z.object({
        userId: z.string(),
        status: z.enum(["pending", "active", "inactive", "rejected"]),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // 检查权限
      const currentUser = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (!currentUser || !["admin", "manager"].includes(currentUser.role)) {
        throw new Error("权限不足");
      }

      const user = await ctx.db.user.update({
        where: { id: input.userId },
        data: { status: input.status },
        select: {
          id: true,
          name: true,
          status: true,
        },
      });

      return user;
    }),

  // 获取公开用户详情（所有用户可访问，权限控制显示内容）
  getPublicUserById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      // 获取当前用户信息以判断权限
      const currentUser = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { id: true, role: true },
      });

      const isAdmin = currentUser && ["admin", "manager"].includes(currentUser.role);
      const isOwnProfile = currentUser?.id === input.id;

      // 查询目标用户
      const user = await ctx.db.user.findUnique({
        where: { id: input.id },
        select: {
          id: true,
          name: true,
          avatar: true,
          bio: true,
          skills: true,
          github: true,
          linkedin: true,
          twitter: true,
          website: true,
          role: true,
          status: true,
          createdAt: true,
          // 条件性包含敏感信息
          ...(isAdmin || isOwnProfile ? {
            realname: true,
            email: true,
            phone: true,
          } : {}),
        },
      });

      if (!user) {
        throw new Error("用户不存在");
      }

      // 如果不是活跃用户且不是管理员，则不允许查看
      if (user.status !== "active" && !isAdmin) {
        throw new Error("用户不存在");
      }

      return {
        ...user,
        skills: user.skills ? user.skills.split(',').map(s => s.trim()).filter(Boolean) : [],
        socialLinks: {
          github: user.github || '',
          linkedin: user.linkedin || '',
          twitter: user.twitter || '',
          website: user.website || '',
        },
      };
    }),

  // 获取单个用户信息（管理员功能）
  getUserById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      // 检查权限
      const currentUser = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (!currentUser || !["admin", "manager"].includes(currentUser.role)) {
        throw new Error("权限不足");
      }

      const user = await ctx.db.user.findUnique({
        where: { id: input.id },
        select: {
          id: true,
          name: true,
          realname: true,
          email: true,
          phone: true,
          avatar: true,
          bio: true,
          education: true,
          level: true,
          subscriptionStatus: true,
          subscriptionExpiry: true,
          skills: true,
          github: true,
          linkedin: true,
          twitter: true,
          website: true,
          status: true,
          role: true,
          createdAt: true,
          updatedAt: true,
          lastLoginAt: true,
        },
      });

      if (!user) {
        throw new Error("用户不存在");
      }

      return {
        ...user,
        skills: user.skills ? user.skills.split(',').map(s => s.trim()).filter(Boolean) : [],
        socialLinks: {
          github: user.github || '',
          linkedin: user.linkedin || '',
          twitter: user.twitter || '',
          website: user.website || '',
        },
      };
    }),

  // 更新用户信息（管理员功能）
  updateUser: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string().optional(),
        realname: z.string().optional(),
        email: z.string().email().optional(),
        phone: z.string().optional(),
        bio: z.string().optional(),
        education: z.string().optional(),
        level: z.enum(["bronze", "silver", "gold", "platinum", "diamond"]).optional(),
        subscriptionStatus: z.enum(["active", "expired", "cancelled", "trial"]).optional(),
        subscriptionExpiry: z.date().optional(),
        skills: z.array(z.string()).optional(),
        socialLinks: z.record(z.string()).optional(),
        status: z.enum(["pending", "active", "inactive", "rejected"]).optional(),
        role: z.enum(["admin", "manager", "member"]).optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { id, skills, socialLinks, ...updateData } = input;

      // 检查权限
      const currentUser = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (!currentUser || !["admin", "manager"].includes(currentUser.role)) {
        throw new Error("权限不足");
      }

      // 检查目标用户是否存在
      const targetUser = await ctx.db.user.findUnique({
        where: { id },
      });

      if (!targetUser) {
        throw new Error("用户不存在");
      }

      const updatedUser = await ctx.db.user.update({
        where: { id },
        data: {
          ...updateData,
          skills: skills ? skills.join(', ') : undefined,
          github: socialLinks?.github || undefined,
          linkedin: socialLinks?.linkedin || undefined,
          twitter: socialLinks?.twitter || undefined,
          website: socialLinks?.website || undefined,
        },
        select: {
          id: true,
          name: true,
          realname: true,
          email: true,
          phone: true,
          avatar: true,
          bio: true,
          education: true,
          level: true,
          subscriptionStatus: true,
          subscriptionExpiry: true,
          skills: true,
          github: true,
          linkedin: true,
          twitter: true,
          website: true,
          status: true,
          role: true,
          createdAt: true,
          lastLoginAt: true,
        },
      });

      return {
        ...updatedUser,
        skills: updatedUser.skills ? updatedUser.skills.split(',').map(s => s.trim()).filter(Boolean) : [],
        socialLinks: {
          github: updatedUser.github || '',
          linkedin: updatedUser.linkedin || '',
          twitter: updatedUser.twitter || '',
          website: updatedUser.website || '',
        },
      };
    }),

  // 请求密码重置
  requestPasswordReset: publicProcedure
    .input(z.object({ email: z.string().email() }))
    .mutation(async ({ ctx, input }) => {
      const user = await ctx.db.user.findUnique({
        where: { email: input.email.toLowerCase() },
        select: { id: true, name: true, email: true },
      });

      if (!user) {
        // 为了安全，即使用户不存在也返回成功
        return { success: true };
      }

      // 生成重置token
      const resetToken = crypto.randomBytes(32).toString('hex');
      const expires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24小时后过期

      // 删除该用户之前的重置token
      await ctx.db.passwordResetToken.deleteMany({
        where: { email: user.email },
      });

      // 创建新的重置token
      await ctx.db.passwordResetToken.create({
        data: {
          email: user.email,
          token: resetToken,
          expires,
        },
      });

      // 发送重置邮件
      try {
        await sendPasswordResetEmail(user.email, user.name, resetToken);
      } catch (error) {
        console.error("发送密码重置邮件失败:", error);
        throw new Error("发送重置邮件失败，请稍后重试");
      }

      return { success: true };
    }),

  // 验证重置token
  verifyResetToken: publicProcedure
    .input(z.object({ token: z.string() }))
    .query(async ({ ctx, input }) => {
      const resetToken = await ctx.db.passwordResetToken.findUnique({
        where: { token: input.token },
      });

      if (!resetToken || resetToken.used || resetToken.expires < new Date()) {
        throw new Error("重置链接无效或已过期");
      }

      return { valid: true, email: resetToken.email };
    }),

  // 重置密码
  resetPassword: publicProcedure
    .input(z.object({
      token: z.string(),
      password: z.string().min(6, "密码至少6个字符"),
    }))
    .mutation(async ({ ctx, input }) => {
      const resetToken = await ctx.db.passwordResetToken.findUnique({
        where: { token: input.token },
      });

      if (!resetToken || resetToken.used || resetToken.expires < new Date()) {
        throw new Error("重置链接无效或已过期");
      }

      // 加密新密码
      const hashedPassword = await bcrypt.hash(input.password, 12);

      // 更新用户密码
      await ctx.db.user.update({
        where: { email: resetToken.email },
        data: { password: hashedPassword },
      });

      // 标记token为已使用
      await ctx.db.passwordResetToken.update({
        where: { token: input.token },
        data: { used: true },
      });

      return { success: true };
    }),

  // 激活用户账户（管理员功能）
  activateUser: protectedProcedure
    .input(z.object({ userId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // 检查权限
      const currentUser = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (!currentUser || !["admin", "manager"].includes(currentUser.role)) {
        throw new Error("权限不足");
      }

      const user = await ctx.db.user.update({
        where: { id: input.userId },
        data: { status: "active" },
        select: {
          id: true,
          name: true,
          email: true,
          status: true,
        },
      });

      // 发送账户激活通知邮件
      try {
        await sendAccountActivatedEmail(user.email, user.name);
      } catch (error) {
        console.error("发送账户激活邮件失败:", error);
        // 不影响激活流程，只记录错误
      }

      return user;
    }),

  // 验证邀请码
  validateInviteCode: publicProcedure
    .input(z.object({ code: z.string() }))
    .query(async ({ ctx, input }) => {
      const inviteCode = await ctx.db.inviteCode.findUnique({
        where: { code: input.code },
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              avatar: true
            }
          }
        },
      });

      if (!inviteCode) {
        return { valid: false, message: "邀请码不存在" };
      }

      if (!inviteCode.isActive) {
        return { valid: false, message: "邀请码已失效" };
      }

      if (inviteCode.expiresAt && inviteCode.expiresAt < new Date()) {
        return { valid: false, message: "邀请码已过期" };
      }

      if (inviteCode.usedCount >= inviteCode.maxUses) {
        return { valid: false, message: "邀请码使用次数已达上限" };
      }

      return {
        valid: true,
        inviteCode: {
          id: inviteCode.id,
          code: inviteCode.code,
          description: inviteCode.description,
          creator: inviteCode.creator,
          usedCount: inviteCode.usedCount,
          maxUses: inviteCode.maxUses,
        },
      };
    }),

  // 生成邀请码（需要登录）
  generateInviteCode: protectedProcedure
    .input(z.object({
      maxUses: z.number().min(1).max(100).default(1),
      expiresAt: z.date().optional(),
      description: z.string().max(200).optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      // 生成唯一邀请码
      let code: string;
      let isUnique = false;

      while (!isUnique) {
        code = crypto.randomBytes(6).toString('hex').toUpperCase();
        const existing = await ctx.db.inviteCode.findUnique({
          where: { code },
        });
        if (!existing) {
          isUnique = true;
        }
      }

      const inviteCode = await ctx.db.inviteCode.create({
        data: {
          code: code!,
          createdBy: ctx.session.user.id,
          maxUses: input.maxUses,
          expiresAt: input.expiresAt,
          description: input.description,
          updatedAt: new Date(),
        },
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              avatar: true,
            },
          },
        },
      });

      return inviteCode;
    }),

  // 获取我的邀请码列表
  getMyInviteCodes: protectedProcedure
    .input(z.object({
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(50).default(10),
    }))
    .query(async ({ ctx, input }) => {
      const skip = (input.page - 1) * input.limit;

      const [inviteCodes, total] = await Promise.all([
        ctx.db.inviteCode.findMany({
          where: { createdBy: ctx.session.user.id },
          include: {
            usages: {
              include: {
                user: {
                  select: {
                    id: true,
                    name: true,
                    avatar: true,
                    createdAt: true,
                  },
                },
              },
            },
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: input.limit,
        }),
        ctx.db.inviteCode.count({
          where: { createdBy: ctx.session.user.id },
        }),
      ]);

      return {
        inviteCodes,
        total,
        page: input.page,
        limit: input.limit,
        totalPages: Math.ceil(total / input.limit),
      };
    }),

  // 获取我邀请的用户列表
  getMyInvitees: protectedProcedure
    .input(z.object({
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(50).default(10),
    }))
    .query(async ({ ctx, input }) => {
      const skip = (input.page - 1) * input.limit;

      const [invitees, total] = await Promise.all([
        ctx.db.user.findMany({
          where: { invitedBy: ctx.session.user.id },
          select: {
            id: true,
            name: true,
            realname: true,
            email: true,
            avatar: true,
            level: true,
            status: true,
            createdAt: true,
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: input.limit,
        }),
        ctx.db.user.count({
          where: { invitedBy: ctx.session.user.id },
        }),
      ]);

      return {
        invitees,
        total,
        page: input.page,
        limit: input.limit,
        totalPages: Math.ceil(total / input.limit),
      };
    }),

  // 获取平台统计数据（管理员权限）
  getPlatformStats: protectedProcedure
    .query(async ({ ctx }) => {
      // 获取用户完整信息以检查管理员权限
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { role: true },
      });

      if (!user || user.role !== "admin") {
        throw new Error("权限不足");
      }

      const [
        totalUsers,
        activeUsers,
        usersByRole,
        usersByStatus,
      ] = await Promise.all([
        ctx.db.user.count(),
        ctx.db.user.count({
          where: {
            status: "active",
            lastLoginAt: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30天内活跃
            },
          },
        }),
        ctx.db.user.groupBy({
          by: ["role"],
          _count: { role: true },
        }),
        ctx.db.user.groupBy({
          by: ["status"],
          _count: { status: true },
        }),
      ]);

      // 转换为对象格式
      const usersByRoleObj = usersByRole.reduce((acc, item) => {
        acc[item.role] = item._count.role;
        return acc;
      }, {} as Record<string, number>);

      const usersByStatusObj = usersByStatus.reduce((acc, item) => {
        acc[item.status] = item._count.status;
        return acc;
      }, {} as Record<string, number>);

      return {
        totalUsers,
        activeUsers,
        usersByRole: usersByRoleObj,
        usersByStatus: usersByStatusObj,
      };
    }),
});
