import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";

// 财务交易输入验证
const financialTransactionSchema = z.object({
  projectId: z.string(),
  accountId: z.string(),
  type: z.enum(["income", "expense"]),
  amount: z.number().positive(),
  currency: z.string().default("CNY"),
  description: z.string(),
  category: z.string(),
  subCategory: z.string().optional(),
  transactionDate: z.date(),
  invoiceNumber: z.string().optional(),
  receiptUrl: z.string().url().optional(),
  paymentMethod: z.string().optional(),
  vendor: z.string().optional(),
  tags: z.array(z.string()).optional(),
  isRecurring: z.boolean().default(false),
  recurringRule: z.object({
    frequency: z.enum(["daily", "weekly", "monthly", "quarterly", "yearly"]),
    interval: z.number().positive(),
    endDate: z.date().optional(),
  }).optional(),
});

// 预算输入验证
const budgetSchema = z.object({
  projectId: z.string(),
  year: z.number().int().min(2020).max(2100),
  quarter: z.number().int().min(1).max(4).optional(),
  month: z.number().int().min(1).max(12).optional(),
  accountId: z.string(),
  budgetType: z.enum(["revenue", "expense"]),
  amount: z.number(),
  description: z.string().optional(),
});

export const financeRouter = createTRPCRouter({
  // 获取财务科目列表
  getFinancialAccounts: protectedProcedure.query(async ({ ctx }) => {
    return await ctx.db.financialAccount.findMany({
      where: { isActive: true },
      orderBy: [{ type: "asc" }, { code: "asc" }],
      include: {
        children: true,
        parent: true,
      },
    });
  }),

  // 创建财务交易记录
  createTransaction: protectedProcedure
    .input(financialTransactionSchema)
    .mutation(async ({ ctx, input }) => {
      // 验证项目权限 - PO、产品经理、全栈开发者可以管理财务
      const projectMember = await ctx.db.projectMember.findFirst({
        where: {
          projectId: input.projectId,
          userId: ctx.session.user.id,
          status: "active",
          role: { in: ["po", "product", "fullstack", "platform"] },
        },
      });

      if (!projectMember) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "您没有权限管理此项目的财务记录",
        });
      }

      // 验证财务科目存在且类型匹配
      const account = await ctx.db.financialAccount.findUnique({
        where: { id: input.accountId },
      });

      if (!account) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "财务科目不存在",
        });
      }

      // 验证科目类型与交易类型匹配
      const validTypes = {
        income: ["收入", "revenue"],
        expense: ["费用", "成本", "expense", "cost"],
      };

      if (!validTypes[input.type].some(type => 
        account.type.toLowerCase().includes(type) || 
        account.category.toLowerCase().includes(type)
      )) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: `科目类型与交易类型不匹配`,
        });
      }

      const transaction = await ctx.db.financialTransaction.create({
        data: {
          projectId: input.projectId,
          accountId: input.accountId,
          type: input.type,
          amount: input.amount,
          currency: input.currency,
          description: input.description,
          category: input.category,
          subCategory: input.subCategory,
          transactionDate: input.transactionDate,
          invoiceNumber: input.invoiceNumber,
          receiptUrl: input.receiptUrl,
          paymentMethod: input.paymentMethod,
          vendor: input.vendor,
          tags: input.tags ? JSON.stringify(input.tags) : null,
          isRecurring: input.isRecurring,
          recurringRule: input.recurringRule ? JSON.stringify(input.recurringRule) : null,
          createdBy: ctx.session.user.id,
          updatedAt: new Date(),
        },
        include: {
          account: true,
          creator: {
            select: { name: true, avatar: true },
          },
        },
      });

      return transaction;
    }),

  // 获取项目财务交易记录
  getProjectTransactions: protectedProcedure
    .input(
      z.object({
        projectId: z.string(),
        type: z.enum(["income", "expense", "all"]).default("all"),
        startDate: z.date().optional(),
        endDate: z.date().optional(),
        category: z.string().optional(),
        limit: z.number().default(20),
        offset: z.number().default(0),
      })
    )
    .query(async ({ ctx, input }) => {
      // 验证项目权限
      const projectMember = await ctx.db.projectMember.findFirst({
        where: {
          projectId: input.projectId,
          userId: ctx.session.user.id,
          status: "active",
        },
      });

      if (!projectMember) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "您没有权限查看此项目的财务记录",
        });
      }

      const where: any = { projectId: input.projectId };

      if (input.type !== "all") {
        where.type = input.type;
      }

      if (input.startDate && input.endDate) {
        where.transactionDate = {
          gte: input.startDate,
          lte: input.endDate,
        };
      }

      if (input.category) {
        where.category = input.category;
      }

      const [transactions, total] = await Promise.all([
        ctx.db.financialTransaction.findMany({
          where,
          include: {
            account: true,
            creator: {
              select: { name: true, avatar: true },
            },
            approver: {
              select: { name: true, avatar: true },
            },
          },
          orderBy: { transactionDate: "desc" },
          take: input.limit,
          skip: input.offset,
        }),
        ctx.db.financialTransaction.count({ where }),
      ]);

      return {
        transactions,
        total,
        hasMore: total > input.offset + input.limit,
      };
    }),

  // 更新财务交易记录
  updateTransaction: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        data: financialTransactionSchema.partial(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const existingTransaction = await ctx.db.financialTransaction.findUnique({
        where: { id: input.id },
        include: { project: true },
      });

      if (!existingTransaction) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "财务记录不存在",
        });
      }

      // 验证权限 - PO、产品经理、全栈开发者可以管理财务
      const projectMember = await ctx.db.projectMember.findFirst({
        where: {
          projectId: existingTransaction.projectId,
          userId: ctx.session.user.id,
          status: "active",
          role: { in: ["po", "product", "fullstack", "platform"] },
        },
      });

      if (!projectMember) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "您没有权限修改此财务记录",
        });
      }

      const updateData: any = { ...input.data };
      if (updateData.tags) {
        updateData.tags = JSON.stringify(updateData.tags);
      }
      if (updateData.recurringRule) {
        updateData.recurringRule = JSON.stringify(updateData.recurringRule);
      }

      return await ctx.db.financialTransaction.update({
        where: { id: input.id },
        data: {
          ...updateData,
          updatedAt: new Date(),
        },
        include: {
          account: true,
          creator: {
            select: { name: true, avatar: true },
          },
        },
      });
    }),

  // 删除财务交易记录
  deleteTransaction: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const transaction = await ctx.db.financialTransaction.findUnique({
        where: { id: input.id },
      });

      if (!transaction) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "财务记录不存在",
        });
      }

      // 验证权限 - 只有PO和平台角色可以删除财务记录
      const projectMember = await ctx.db.projectMember.findFirst({
        where: {
          projectId: transaction.projectId,
          userId: ctx.session.user.id,
          status: "active",
          role: { in: ["po", "platform"] },
        },
      });

      if (!projectMember) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "您没有权限删除此财务记录",
        });
      }

      await ctx.db.financialTransaction.delete({
        where: { id: input.id },
      });

      return { success: true };
    }),

  // 获取项目财务统计
  getProjectFinancialStats: protectedProcedure
    .input(
      z.object({
        projectId: z.string(),
        year: z.number().optional(),
        quarter: z.number().optional(),
        month: z.number().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      // 验证权限
      const projectMember = await ctx.db.projectMember.findFirst({
        where: {
          projectId: input.projectId,
          userId: ctx.session.user.id,
          status: "active",
        },
      });

      if (!projectMember) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "您没有权限查看此项目的财务统计",
        });
      }

      // 构建日期过滤条件
      const dateFilter: any = {};
      if (input.year) {
        const startDate = new Date(input.year, 0, 1);
        let endDate = new Date(input.year + 1, 0, 1);

        if (input.quarter) {
          const quarterStart = (input.quarter - 1) * 3;
          startDate.setMonth(quarterStart);
          endDate = new Date(input.year, quarterStart + 3, 1);
        }

        if (input.month) {
          startDate.setMonth(input.month - 1);
          endDate = new Date(input.year, input.month, 1);
        }

        dateFilter.transactionDate = {
          gte: startDate,
          lt: endDate,
        };
      }

      // 获取收入统计
      const incomeStats = await ctx.db.financialTransaction.groupBy({
        by: ["category"],
        where: {
          projectId: input.projectId,
          type: "income",
          status: "confirmed",
          ...dateFilter,
        },
        _sum: { amount: true },
        _count: { id: true },
      });

      // 获取支出统计
      const expenseStats = await ctx.db.financialTransaction.groupBy({
        by: ["category"],
        where: {
          projectId: input.projectId,
          type: "expense",
          status: "confirmed",
          ...dateFilter,
        },
        _sum: { amount: true },
        _count: { id: true },
      });

      // 计算总计
      const totalIncome = incomeStats.reduce((sum, item) => sum + (item._sum.amount || 0), 0);
      const totalExpense = expenseStats.reduce((sum, item) => sum + (item._sum.amount || 0), 0);
      const netProfit = totalIncome - totalExpense;

      return {
        totalIncome,
        totalExpense,
        netProfit,
        incomeByCategory: incomeStats.map(item => ({
          category: item.category,
          amount: item._sum.amount || 0,
          count: item._count.id,
        })),
        expenseByCategory: expenseStats.map(item => ({
          category: item.category,
          amount: item._sum.amount || 0,
          count: item._count.id,
        })),
        profitMargin: totalIncome > 0 ? (netProfit / totalIncome) * 100 : 0,
      };
    }),

  // 创建/更新项目预算
  createOrUpdateBudget: protectedProcedure
    .input(budgetSchema)
    .mutation(async ({ ctx, input }) => {
      // 验证权限 - PO、产品经理、全栈开发者可以管理预算
      const projectMember = await ctx.db.projectMember.findFirst({
        where: {
          projectId: input.projectId,
          userId: ctx.session.user.id,
          status: "active",
          role: { in: ["po", "product", "fullstack", "platform"] },
        },
      });

      if (!projectMember) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "您没有权限管理此项目的预算",
        });
      }

      // 使用 upsert 创建或更新预算
      const budget = await ctx.db.projectBudget.upsert({
        where: {
          projectId_year_quarter_month_accountId: {
            projectId: input.projectId,
            year: input.year,
            quarter: input.quarter ?? null,
            month: input.month ?? null,
            accountId: input.accountId,
          } as any,
        },
        update: {
          amount: input.amount,
          description: input.description,
        },
        create: {
          projectId: input.projectId,
          year: input.year,
          quarter: input.quarter,
          month: input.month,
          accountId: input.accountId,
          budgetType: input.budgetType,
          amount: input.amount,
          description: input.description,
          createdBy: ctx.session.user.id,
          updatedAt: new Date(),
        },
        include: {
          account: true,
        },
      });

      return budget;
    }),

  // 获取项目预算
  getProjectBudgets: protectedProcedure
    .input(
      z.object({
        projectId: z.string(),
        year: z.number(),
        quarter: z.number().optional(),
        month: z.number().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      const where: any = {
        projectId: input.projectId,
        year: input.year,
      };

      if (input.quarter) {
        where.quarter = input.quarter;
      }

      if (input.month) {
        where.month = input.month;
      }

      return await ctx.db.projectBudget.findMany({
        where,
        include: {
          account: true,
          creator: {
            select: { name: true },
          },
        },
        orderBy: [{ budgetType: "asc" }, { account: { code: "asc" } }],
      });
    }),

  // 获取预算与实际对比
  getBudgetVsActual: protectedProcedure
    .input(
      z.object({
        projectId: z.string(),
        year: z.number(),
        quarter: z.number().optional(),
        month: z.number().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      // 获取预算数据
      const budgets = await ctx.db.projectBudget.findMany({
        where: {
          projectId: input.projectId,
          year: input.year,
          quarter: input.quarter,
          month: input.month,
        },
        include: { account: true },
      });

      // 构建实际数据查询的日期范围
      const startDate = new Date(input.year, 0, 1);
      let endDate = new Date(input.year + 1, 0, 1);

      if (input.quarter) {
        const quarterStart = (input.quarter - 1) * 3;
        startDate.setMonth(quarterStart);
        endDate = new Date(input.year, quarterStart + 3, 1);
      }

      if (input.month) {
        startDate.setMonth(input.month - 1);
        endDate = new Date(input.year, input.month, 1);
      }

      // 获取实际财务数据
      const actualData = await ctx.db.financialTransaction.groupBy({
        by: ["accountId", "type"],
        where: {
          projectId: input.projectId,
          transactionDate: {
            gte: startDate,
            lt: endDate,
          },
          status: "confirmed",
        },
        _sum: { amount: true },
      });

      // 合并预算和实际数据
      const comparison = budgets.map(budget => {
        const actual = actualData.find(
          a => a.accountId === budget.accountId && 
               ((budget.budgetType === "revenue" && a.type === "income") ||
                (budget.budgetType === "expense" && a.type === "expense"))
        );

        const actualAmount = actual?._sum.amount || 0;
        const budgetAmount = budget.amount;
        const variance = actualAmount - budgetAmount;
        const variancePercent = budgetAmount !== 0 ? (variance / budgetAmount) * 100 : 0;

        return {
          account: budget.account,
          budgetType: budget.budgetType,
          budgetAmount,
          actualAmount,
          variance,
          variancePercent,
          status: Math.abs(variancePercent) <= 10 ? "on_track" : 
                  variancePercent > 10 ? "over_budget" : "under_budget",
        };
      });

      return comparison;
    }),
});