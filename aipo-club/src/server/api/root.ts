import { postRouter } from "~/server/api/routers/post";
import { userRouter } from "~/server/api/routers/user";
import { projectRouter } from "~/server/api/routers/project";
import { shareRouter } from "~/server/api/routers/share";
import { paymentRouter } from "~/server/api/routers/payment";
import { financeRouter } from "~/server/api/routers/finance";
import { createCallerFactory, createTRPCRouter } from "~/server/api/trpc";

/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
  post: postRouter,
  user: userRouter,
  project: projectRouter,
  share: shareRouter,
  payment: paymentRouter,
  finance: financeRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;

/**
 * Create a server-side caller for the tRPC API.
 * @example
 * const trpc = createCaller(createContext);
 * const res = await trpc.post.all();
 *       ^? Post[]
 */
export const createCaller = createCallerFactory(appRouter);
