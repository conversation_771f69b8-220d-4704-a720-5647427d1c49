/**
 * 财务计算工具库
 * 包含成本、利润、分成等核心计算逻辑
 */

export interface FinancialPeriod {
  startDate: Date;
  endDate: Date;
}

export interface TransactionSummary {
  totalIncome: number;
  totalExpense: number;
  netProfit: number;
  grossProfit: number;
  operatingExpense: number;
  costOfRevenue: number;
  profitMargin: number;
  grossMargin: number;
}

export interface CategorySummary {
  category: string;
  amount: number;
  percentage: number;
}

export interface ProfitDistributionResult {
  totalDistributableProfit: number;
  platformFee: number;
  memberDistributions: {
    userId: string;
    userName: string;
    shareType: string;
    percentage: number;
    amount: number;
    basis: "revenue" | "profit";
  }[];
}

/**
 * 计算项目财务摘要
 */
export function calculateFinancialSummary(
  incomeTransactions: any[],
  expenseTransactions: any[]
): TransactionSummary {
  // 计算总收入
  const totalIncome = incomeTransactions.reduce((sum, t) => sum + t.amount, 0);
  
  // 按类别分类支出
  const operatingExpenseCategories = [
    "销售费用", "管理费用", "研发费用", "财务费用",
    "营销推广", "办公费用", "差旅费", "培训费"
  ];
  
  const costOfRevenueCategories = [
    "主营业务成本", "产品成本", "服务成本", "直接成本"
  ];

  let operatingExpense = 0;
  let costOfRevenue = 0;
  let otherExpenses = 0;

  expenseTransactions.forEach(transaction => {
    if (operatingExpenseCategories.some(cat => 
      transaction.category.includes(cat) || transaction.account?.category?.includes(cat)
    )) {
      operatingExpense += transaction.amount;
    } else if (costOfRevenueCategories.some(cat => 
      transaction.category.includes(cat) || transaction.account?.category?.includes(cat)
    )) {
      costOfRevenue += transaction.amount;
    } else {
      otherExpenses += transaction.amount;
    }
  });

  const totalExpense = operatingExpense + costOfRevenue + otherExpenses;
  const grossProfit = totalIncome - costOfRevenue;
  const netProfit = totalIncome - totalExpense;
  const profitMargin = totalIncome > 0 ? (netProfit / totalIncome) * 100 : 0;
  const grossMargin = totalIncome > 0 ? (grossProfit / totalIncome) * 100 : 0;

  return {
    totalIncome,
    totalExpense,
    netProfit,
    grossProfit,
    operatingExpense,
    costOfRevenue,
    profitMargin,
    grossMargin,
  };
}

/**
 * 计算分类统计
 */
export function calculateCategoryStats(
  transactions: any[],
  total: number
): CategorySummary[] {
  const categoryMap = new Map<string, number>();
  
  transactions.forEach(transaction => {
    const category = transaction.category || "未分类";
    categoryMap.set(category, (categoryMap.get(category) || 0) + transaction.amount);
  });

  return Array.from(categoryMap.entries()).map(([category, amount]) => ({
    category,
    amount,
    percentage: total > 0 ? (amount / total) * 100 : 0,
  })).sort((a, b) => b.amount - a.amount);
}

/**
 * 计算真实利润的分成分配
 * 基于实际财务数据而非简单的收入分成
 */
export function calculateProfitDistribution(
  financialSummary: TransactionSummary,
  projectShares: any[],
  platformFeeRate = 0.05
): ProfitDistributionResult {
  const { netProfit, totalIncome } = financialSummary;
  
  // 只有在有利润的情况下才进行分成
  if (netProfit <= 0) {
    return {
      totalDistributableProfit: 0,
      platformFee: 0,
      memberDistributions: [],
    };
  }

  // 先扣除平台手续费
  const platformFee = netProfit * platformFeeRate;
  const distributableProfit = netProfit - platformFee;

  const memberDistributions = projectShares
    .filter(share => share.status === "active")
    .map(share => {
      let amount = 0;
      let basis: "revenue" | "profit" = "profit";

      // 根据分成类型计算分成金额
      switch (share.shareType) {
        case "equity":
        case "profit":
          // 基于净利润的分成
          amount = distributableProfit * (share.percentage / 100);
          basis = "profit";
          break;
        
        case "revenue":
          // 基于收入的分成（较少使用，但某些情况下适用）
          amount = totalIncome * (share.percentage / 100);
          basis = "revenue";
          break;
        
        case "bonus":
          // 奖金类型，基于净利润，但可能有额外条件
          if (financialSummary.profitMargin >= 10) { // 利润率达到10%才发放奖金
            amount = distributableProfit * (share.percentage / 100);
          }
          basis = "profit";
          break;
        
        case "commission":
          // 佣金类型，通常基于收入
          amount = totalIncome * (share.percentage / 100);
          basis = "revenue";
          break;
        
        default:
          amount = distributableProfit * (share.percentage / 100);
          basis = "profit";
      }

      return {
        userId: share.userId,
        userName: share.user?.name || "未知用户",
        shareType: share.shareType,
        percentage: share.percentage,
        amount: Math.max(0, amount), // 确保金额不为负
        basis,
      };
    });

  return {
    totalDistributableProfit: distributableProfit,
    platformFee,
    memberDistributions,
  };
}

/**
 * 计算财务健康度指标
 */
export function calculateFinancialHealth(
  financialSummary: TransactionSummary,
  previousPeriodSummary?: TransactionSummary
): {
  healthScore: number;
  indicators: {
    profitability: "excellent" | "good" | "fair" | "poor";
    efficiency: "excellent" | "good" | "fair" | "poor";
    growth: "excellent" | "good" | "fair" | "poor" | "no_data";
  };
  recommendations: string[];
} {
  const { profitMargin, grossMargin, totalIncome, totalExpense } = financialSummary;
  
  // 盈利能力评估
  let profitability: "excellent" | "good" | "fair" | "poor";
  if (profitMargin >= 20) profitability = "excellent";
  else if (profitMargin >= 10) profitability = "good";
  else if (profitMargin >= 0) profitability = "fair";
  else profitability = "poor";

  // 效率评估
  let efficiency: "excellent" | "good" | "fair" | "poor";
  if (grossMargin >= 70) efficiency = "excellent";
  else if (grossMargin >= 50) efficiency = "good";
  else if (grossMargin >= 30) efficiency = "fair";
  else efficiency = "poor";

  // 增长评估
  let growth: "excellent" | "good" | "fair" | "poor" | "no_data" = "no_data";
  if (previousPeriodSummary && previousPeriodSummary.totalIncome > 0) {
    const growthRate = ((totalIncome - previousPeriodSummary.totalIncome) / previousPeriodSummary.totalIncome) * 100;
    if (growthRate >= 20) growth = "excellent";
    else if (growthRate >= 10) growth = "good";
    else if (growthRate >= 0) growth = "fair";
    else growth = "poor";
  }

  // 计算综合健康度评分 (0-100)
  const profitabilityScore = profitability === "excellent" ? 100 : 
                           profitability === "good" ? 75 : 
                           profitability === "fair" ? 50 : 25;
  
  const efficiencyScore = efficiency === "excellent" ? 100 : 
                         efficiency === "good" ? 75 : 
                         efficiency === "fair" ? 50 : 25;
  
  const growthScore = growth === "excellent" ? 100 : 
                     growth === "good" ? 75 : 
                     growth === "fair" ? 50 : 
                     growth === "poor" ? 25 : 60; // 无数据时给中等分

  const healthScore = Math.round((profitabilityScore + efficiencyScore + growthScore) / 3);

  // 生成建议
  const recommendations: string[] = [];
  
  if (profitability === "poor") {
    recommendations.push("当前项目处于亏损状态，建议分析成本结构并优化支出");
  } else if (profitability === "fair") {
    recommendations.push("利润率偏低，建议提高收入或降低成本");
  }

  if (efficiency === "poor") {
    recommendations.push("毛利率较低，建议优化产品成本或提高定价");
  }

  if (growth === "poor") {
    recommendations.push("收入出现下降，建议分析市场情况并调整策略");
  }

  if (totalExpense > totalIncome * 0.8) {
    recommendations.push("支出占收入比例过高，建议控制运营成本");
  }

  if (recommendations.length === 0) {
    recommendations.push("财务状况良好，继续保持当前策略");
  }

  return {
    healthScore,
    indicators: { profitability, efficiency, growth },
    recommendations,
  };
}

/**
 * 计算预算执行情况
 */
export function calculateBudgetVariance(
  actualAmount: number,
  budgetAmount: number
): {
  variance: number;
  variancePercent: number;
  status: "over_budget" | "on_track" | "under_budget";
  severity: "critical" | "warning" | "normal";
} {
  const variance = actualAmount - budgetAmount;
  const variancePercent = budgetAmount !== 0 ? (variance / budgetAmount) * 100 : 0;
  
  let status: "over_budget" | "on_track" | "under_budget";
  let severity: "critical" | "warning" | "normal";

  if (Math.abs(variancePercent) <= 5) {
    status = "on_track";
    severity = "normal";
  } else if (variancePercent > 5) {
    status = "over_budget";
    severity = variancePercent > 20 ? "critical" : "warning";
  } else {
    status = "under_budget";
    severity = variancePercent < -20 ? "warning" : "normal";
  }

  return {
    variance,
    variancePercent,
    status,
    severity,
  };
}