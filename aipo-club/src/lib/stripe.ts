import Stripe from 'stripe';
import { env } from '~/env';

export const stripe = new Stripe(env.STRIPE_SECRET_KEY, {
  apiVersion: '2025-05-28.basil',
  typescript: true,
});

// Stripe 配置常量
export const STRIPE_CONFIG = {
  CURRENCY: 'cny',
  MIN_AMOUNT: 100, // 最小支付金额（分）
  PLATFORM_FEE_RATE: 0.05, // 平台手续费率 5%
} as const;

// 支付方法映射
export const PAYMENT_METHODS = {
  stripe: 'Stripe',
  alipay: 'Alipay',
  wechat: 'WeChat Pay',
  bank: 'Bank Transfer',
} as const;

export type PaymentProvider = keyof typeof PAYMENT_METHODS;

// 创建支付意图
export async function createPaymentIntent(
  amount: number,
  currency = STRIPE_CONFIG.CURRENCY,
  metadata?: Record<string, string>
) {
  return await stripe.paymentIntents.create({
    amount: Math.round(amount * 100), // 转换为分
    currency,
    metadata,
    automatic_payment_methods: {
      enabled: true,
    },
  });
}

// 创建转账
export async function createTransfer(
  amount: number,
  destination: string,
  metadata?: Record<string, string>
) {
  return await stripe.transfers.create({
    amount: Math.round(amount * 100), // 转换为分
    currency: STRIPE_CONFIG.CURRENCY,
    destination,
    metadata,
  });
}

// 获取账户信息
export async function getStripeAccount(accountId: string) {
  return await stripe.accounts.retrieve(accountId);
}

// 创建快速账户
export async function createExpressAccount(
  email: string,
  metadata?: Record<string, string>
) {
  return await stripe.accounts.create({
    type: 'express',
    email,
    metadata,
    capabilities: {
      transfers: { requested: true },
    },
  });
}

// 创建账户链接
export async function createAccountLink(
  accountId: string,
  refreshUrl: string,
  returnUrl: string
) {
  return await stripe.accountLinks.create({
    account: accountId,
    refresh_url: refreshUrl,
    return_url: returnUrl,
    type: 'account_onboarding',
  });
}