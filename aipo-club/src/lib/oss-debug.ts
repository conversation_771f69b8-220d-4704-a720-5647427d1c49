/**
 * OSS调试和诊断工具
 */

/**
 * 检查OSS环境变量配置
 */
export function checkOSSEnvConfig() {
  const config = {
    OSS_REGION: process.env.OSS_REGION,
    OSS_ACCESS_KEY_ID: process.env.OSS_ACCESS_KEY_ID,
    OSS_ACCESS_KEY_SECRET: process.env.OSS_ACCESS_KEY_SECRET,
    OSS_BUCKET: process.env.OSS_BUCKET,
    OSS_ENDPOINT: process.env.OSS_ENDPOINT,
    NEXT_PUBLIC_OSS_BASE_URL: process.env.NEXT_PUBLIC_OSS_BASE_URL,
  };

  const status = {
    OSS_REGION: !!config.OSS_REGION,
    OSS_ACCESS_KEY_ID: !!config.OSS_ACCESS_KEY_ID,
    OSS_ACCESS_KEY_SECRET: !!config.OSS_ACCESS_KEY_SECRET,
    OSS_BUCKET: !!config.OSS_BUCKET,
    OSS_ENDPOINT: !!config.OSS_ENDPOINT,
    NEXT_PUBLIC_OSS_BASE_URL: !!config.NEXT_PUBLIC_OSS_BASE_URL,
  };

  const missing = Object.entries(status)
    .filter(([_, value]) => !value)
    .map(([key]) => key);

  return {
    config: {
      ...config,
      OSS_ACCESS_KEY_SECRET: config.OSS_ACCESS_KEY_SECRET ? '***' : undefined,
    },
    status,
    missing,
    isComplete: missing.length === 0,
  };
}

/**
 * 生成OSS配置建议
 */
export function generateOSSConfigSuggestions() {
  const check = checkOSSEnvConfig();
  
  if (check.isComplete) {
    return {
      status: 'complete',
      message: 'OSS配置完整',
      suggestions: [],
    };
  }

  const suggestions: string[] = [];

  if (!check.status.OSS_REGION) {
    suggestions.push('设置 OSS_REGION，例如：oss-cn-beijing');
  }

  if (!check.status.OSS_ACCESS_KEY_ID) {
    suggestions.push('设置 OSS_ACCESS_KEY_ID，从阿里云控制台获取');
  }

  if (!check.status.OSS_ACCESS_KEY_SECRET) {
    suggestions.push('设置 OSS_ACCESS_KEY_SECRET，从阿里云控制台获取');
  }

  if (!check.status.OSS_BUCKET) {
    suggestions.push('设置 OSS_BUCKET，您的OSS存储桶名称');
  }

  if (!check.status.NEXT_PUBLIC_OSS_BASE_URL) {
    suggestions.push('设置 NEXT_PUBLIC_OSS_BASE_URL，例如：https://your-bucket.oss-cn-beijing.aliyuncs.com');
  }

  return {
    status: 'incomplete',
    message: `缺少 ${check.missing.length} 个必需的配置项`,
    missing: check.missing,
    suggestions,
  };
}

/**
 * 验证OSS URL格式
 */
export function validateOSSUrl(url: string): {
  isValid: boolean;
  type: 'oss' | 'cdn' | 'unknown';
  bucket?: string;
  region?: string;
  key?: string;
  error?: string;
} {
  try {
    const urlObj = new URL(url);
    
    // 检查是否是阿里云OSS URL
    if (urlObj.hostname.includes('.aliyuncs.com')) {
      const parts = urlObj.hostname.split('.');
      if (parts.length >= 3 && parts[1] && parts[1].startsWith('oss-')) {
        return {
          isValid: true,
          type: 'oss',
          bucket: parts[0] || '',
          region: parts[1].replace('oss-', ''),
          key: urlObj.pathname.substring(1), // 移除开头的 /
        };
      }
    }

    // 检查是否是CDN URL
    if (urlObj.protocol === 'https:' || urlObj.protocol === 'http:') {
      return {
        isValid: true,
        type: 'cdn',
        key: urlObj.pathname.substring(1),
      };
    }

    return {
      isValid: false,
      type: 'unknown',
      error: '不是有效的OSS或CDN URL',
    };

  } catch {
    return {
      isValid: false,
      type: 'unknown',
      error: 'URL格式错误',
    };
  }
}

/**
 * 生成OSS诊断报告
 */
export function generateOSSDiagnosticReport() {
  const envCheck = checkOSSEnvConfig();
  const configSuggestions = generateOSSConfigSuggestions();

  return {
    timestamp: new Date().toISOString(),
    environment: {
      nodeEnv: process.env.NODE_ENV,
      nextjsVersion: process.env.npm_package_dependencies_next || 'unknown',
    },
    ossConfig: envCheck,
    suggestions: configSuggestions,
    commonIssues: [
      {
        issue: 'OSS配置不完整',
        solution: '检查所有必需的环境变量是否已设置',
        priority: 'high',
      },
      {
        issue: 'OSS访问权限错误',
        solution: '确保AccessKey有读写OSS的权限',
        priority: 'high',
      },
      {
        issue: 'OSS Bucket不存在',
        solution: '在阿里云控制台创建对应的OSS Bucket',
        priority: 'high',
      },
      {
        issue: 'OSS Bucket权限设置',
        solution: '设置Bucket为公共读权限，或配置正确的访问策略',
        priority: 'medium',
      },
      {
        issue: 'CORS配置',
        solution: '在OSS控制台配置CORS规则，允许前端域名访问',
        priority: 'medium',
      },
      {
        issue: '图片处理服务未开启',
        solution: '在OSS控制台开启图片处理服务',
        priority: 'low',
      },
    ],
  };
}

/**
 * 打印OSS调试信息到控制台
 */
export function debugOSSConfig() {
  const report = generateOSSDiagnosticReport();
  
  console.log('=== OSS配置诊断报告 ===');
  console.log('时间:', report.timestamp);
  console.log('环境:', report.environment);
  console.log('');
  
  console.log('OSS配置状态:');
  Object.entries(report.ossConfig.status).forEach(([key, value]) => {
    console.log(`  ${key}: ${value ? '✓' : '✗'}`);
  });
  console.log('');
  
  if (report.suggestions.missing && report.suggestions.missing.length > 0) {
    console.log('缺少的配置:');
    report.suggestions.missing.forEach(key => {
      console.log(`  - ${key}`);
    });
    console.log('');
  }

  if (report.suggestions.suggestions && report.suggestions.suggestions.length > 0) {
    console.log('配置建议:');
    report.suggestions.suggestions.forEach((suggestion, index) => {
      console.log(`  ${index + 1}. ${suggestion}`);
    });
    console.log('');
  }
  
  console.log('常见问题排查:');
  report.commonIssues.forEach((issue, index) => {
    console.log(`  ${index + 1}. ${issue.issue} (${issue.priority})`);
    console.log(`     解决方案: ${issue.solution}`);
  });
  
  console.log('=== 诊断报告结束 ===');
  
  return report;
}
