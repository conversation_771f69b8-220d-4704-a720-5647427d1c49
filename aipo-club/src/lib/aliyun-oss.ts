/**
 * 阿里云OSS上传工具
 */
import OSS from 'ali-oss';

// OSS客户端配置
export interface AliOSSConfig {
  region: string;
  accessKeyId: string;
  accessKeySecret: string;
  bucket: string;
  endpoint?: string;
  secure?: boolean;
}

// 上传结果
export interface AliOSSUploadResult {
  success: boolean;
  url?: string;
  key?: string;
  error?: string;
}

/**
 * 创建OSS客户端
 */
export function createOSSClient(config: AliOSSConfig): OSS {
  return new OSS({
    region: config.region,
    accessKeyId: config.accessKeyId,
    accessKeySecret: config.accessKeySecret,
    bucket: config.bucket,
    endpoint: config.endpoint,
    secure: config.secure !== false, // 默认使用HTTPS
  });
}

/**
 * 上传文件到阿里云OSS
 */
export async function uploadToAliOSS(
  buffer: Buffer,
  key: string,
  config: AliOSSConfig,
  options: {
    contentType?: string;
    headers?: Record<string, string>;
  } = {}
): Promise<AliOSSUploadResult> {
  try {
    const client = createOSSClient(config);
    
    // 上传文件
    const result = await client.put(key, buffer, {
      headers: {
        'Content-Type': options.contentType || 'application/octet-stream',
        ...options.headers,
      },
    });

    if (result.res?.status === 200) {
      return {
        success: true,
        url: result.url,
        key: result.name,
      };
    } else {
      return {
        success: false,
        error: `上传失败: ${result.res?.status || 'Unknown error'}`,
      };
    }
  } catch (error) {
    console.error('OSS上传错误:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '上传失败',
    };
  }
}

/**
 * 删除OSS文件
 */
export async function deleteFromAliOSS(
  key: string,
  config: AliOSSConfig
): Promise<boolean> {
  try {
    const client = createOSSClient(config);
    await client.delete(key);
    return true;
  } catch {
    // console.error('OSS删除错误:', error);
    return false;
  }
}

/**
 * 检查文件是否存在
 */
export async function checkFileExists(
  key: string,
  config: AliOSSConfig
): Promise<boolean> {
  try {
    const client = createOSSClient(config);
    await client.head(key);
    return true;
  } catch {
    return false;
  }
}

/**
 * 生成签名URL（用于私有bucket）
 */
export async function generateSignedUrl(
  key: string,
  config: AliOSSConfig,
  expires: number = 3600 // 1小时
): Promise<string> {
  try {
    const client = createOSSClient(config);
    return client.signatureUrl(key, { expires });
  } catch (error) {
    console.error('生成签名URL错误:', error);
    throw error;
  }
}

/**
 * 获取OSS配置（从环境变量）
 */
export function getAliOSSConfig(): AliOSSConfig {
  const config = {
    region: process.env.OSS_REGION || '',
    accessKeyId: process.env.OSS_ACCESS_KEY_ID || '',
    accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET || '',
    bucket: process.env.OSS_BUCKET || '',
    endpoint: process.env.OSS_ENDPOINT,
    secure: true,
  };

  // 验证必需的配置
  if (!config.region || !config.accessKeyId || !config.accessKeySecret || !config.bucket) {
    throw new Error('OSS配置不完整，请检查环境变量');
  }

  return config;
}

/**
 * 构建OSS文件URL
 */
export function buildOSSUrl(key: string, config?: AliOSSConfig): string {
  const ossConfig = config || getAliOSSConfig();
  
  // 如果已经是完整URL，直接返回
  if (key.startsWith('http://') || key.startsWith('https://')) {
    return key;
  }

  // 使用自定义域名或默认OSS域名
  const baseUrl = process.env.NEXT_PUBLIC_OSS_BASE_URL || 
    `https://${ossConfig.bucket}.${ossConfig.endpoint || `oss-${ossConfig.region}.aliyuncs.com`}`;
  
  return `${baseUrl}/${key}`;
}

/**
 * 构建带图片处理参数的URL
 */
export function buildOSSImageUrl(
  key: string,
  width?: number,
  height?: number,
  mode: 'lfit' | 'mfit' | 'fill' | 'pad' | 'fixed' = 'fill',
  config?: AliOSSConfig
): string {
  const baseUrl = buildOSSUrl(key, config);
  
  if (!width && !height) {
    return baseUrl;
  }

  const params: string[] = [];
  
  if (width) params.push(`w_${width}`);
  if (height) params.push(`h_${height}`);
  params.push(`m_${mode}`);

  return `${baseUrl}?x-oss-process=image/resize,${params.join(',')}`;
}

/**
 * 验证OSS连接
 */
export async function testOSSConnection(config?: AliOSSConfig): Promise<boolean> {
  try {
    const ossConfig = config || getAliOSSConfig();
    const client = createOSSClient(ossConfig);

    // 尝试列出bucket信息
    await client.getBucketInfo(ossConfig.bucket);
    return true;
  } catch {
    // console.error('OSS连接测试失败:', error);
    return false;
  }
}
