/**
 * OSS文件上传工具
 * 支持阿里云OSS、腾讯云COS、AWS S3等
 */

// OSS配置接口
export interface OSSConfig {
  endpoint: string;
  accessKeyId: string;
  accessKeySecret: string;
  bucket: string;
  region?: string;
  baseUrl?: string; // CDN域名
}

// 上传结果接口
export interface UploadResult {
  success: boolean;
  url?: string;
  key?: string;
  error?: string;
}

// 文件类型验证
export const ALLOWED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/gif',
  'image/webp'
];

// 文件大小限制（5MB）
export const MAX_FILE_SIZE = 5 * 1024 * 1024;

/**
 * 验证图片文件
 */
export function validateImageFile(file: File): { valid: boolean; error?: string } {
  // 检查文件类型
  if (!ALLOWED_IMAGE_TYPES.includes(file.type)) {
    return {
      valid: false,
      error: '只支持 JPG、PNG、GIF、WebP 格式的图片'
    };
  }

  // 检查文件大小
  if (file.size > MAX_FILE_SIZE) {
    return {
      valid: false,
      error: '图片大小不能超过 5MB'
    };
  }

  return { valid: true };
}

/**
 * 生成唯一文件名
 */
export function generateFileName(originalName: string, prefix = ''): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  const extension = originalName.split('.').pop()?.toLowerCase() || 'jpg';
  
  const fileName = `${timestamp}_${random}.${extension}`;
  return prefix ? `${prefix}/${fileName}` : fileName;
}

/**
 * 压缩图片
 */
export function compressImage(file: File, maxWidth = 800, quality = 0.8): Promise<File> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // 计算压缩后的尺寸
      let { width, height } = img;
      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }

      canvas.width = width;
      canvas.height = height;

      // 绘制压缩后的图片
      ctx?.drawImage(img, 0, 0, width, height);

      canvas.toBlob(
        (blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now(),
            });
            resolve(compressedFile);
          } else {
            reject(new Error('图片压缩失败'));
          }
        },
        file.type,
        quality
      );
    };

    img.onerror = () => reject(new Error('图片加载失败'));
    img.src = URL.createObjectURL(file);
  });
}

/**
 * 上传文件到OSS
 * 支持阿里云OSS、腾讯云COS、AWS S3等
 */
export async function uploadToOSS(
  file: File,
  _config: OSSConfig,
  options: {
    prefix?: string;
    compress?: boolean;
    maxWidth?: number;
    quality?: number;
  } = {}
): Promise<UploadResult> {
  try {
    // 验证文件
    const validation = validateImageFile(file);
    if (!validation.valid) {
      return {
        success: false,
        error: validation.error
      };
    }

    // 压缩图片（可选）
    let uploadFile = file;
    if (options.compress) {
      uploadFile = await compressImage(
        file,
        options.maxWidth || 800,
        options.quality || 0.8
      );
    }

    // 生成文件名
    const fileName = generateFileName(file.name, options.prefix);

    // 准备上传数据
    const formData = new FormData();
    formData.append('file', uploadFile);
    formData.append('key', fileName);

    // 上传到后端API
    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || '上传失败');
    }

    const result = await response.json();

    return {
      success: true,
      url: result.url,
      key: result.key || fileName
    };

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '上传失败'
    };
  }
}

/**
 * 删除OSS文件
 */
export async function deleteFromOSS(key: string, _config: OSSConfig): Promise<boolean> {
  try {
    // 实际项目中需要调用OSS删除API
    const response = await fetch('/api/upload', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ key }),
    });

    return response.ok;
  } catch (error) {
    console.error('删除文件失败:', error);
    return false;
  }
}

/**
 * 获取OSS配置（仅前端可访问的配置）
 */
export function getOSSConfig(): OSSConfig {
  return {
    endpoint: process.env.NEXT_PUBLIC_OSS_ENDPOINT || '',
    accessKeyId: '', // 前端不应该访问敏感信息
    accessKeySecret: '', // 前端不应该访问敏感信息
    bucket: process.env.NEXT_PUBLIC_OSS_BUCKET || '',
    region: process.env.NEXT_PUBLIC_OSS_REGION || '',
    baseUrl: process.env.NEXT_PUBLIC_OSS_BASE_URL || '',
  };
}

/**
 * 构建完整的图片URL
 */
export function buildImageUrl(key: string, config?: OSSConfig): string {
  // 如果已经是完整URL，直接返回
  if (key.startsWith('http://') || key.startsWith('https://')) {
    return key;
  }

  // 使用环境变量中的基础URL或构建默认URL
  const baseUrl = process.env.NEXT_PUBLIC_OSS_BASE_URL;
  if (baseUrl) {
    return `${baseUrl}/${key}`;
  }

  // 降级到默认构建方式
  const ossConfig = config || getOSSConfig();
  const defaultUrl = `https://${ossConfig.bucket}.${ossConfig.endpoint}`;
  return `${defaultUrl}/${key}`;
}

/**
 * 生成图片缩略图URL（阿里云OSS图片处理）
 */
export function buildThumbnailUrl(
  key: string,
  width: number,
  height?: number,
  config?: OSSConfig
): string {
  const imageUrl = buildImageUrl(key, config);

  // 阿里云OSS图片处理参数
  const params: string[] = [];
  params.push(`w_${width}`);
  if (height) {
    params.push(`h_${height}`);
  }
  params.push('m_fill'); // 填充模式，保持比例并填充

  const processParams = `?x-oss-process=image/resize,${params.join(',')}`;
  return `${imageUrl}${processParams}`;
}
