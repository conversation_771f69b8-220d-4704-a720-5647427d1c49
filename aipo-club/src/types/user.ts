// 用户管理相关的类型定义

// 用户状态枚举
export const USER_STATUS = {
  PENDING: 'pending',
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  REJECTED: 'rejected',
} as const;

export type UserStatus = typeof USER_STATUS[keyof typeof USER_STATUS];

// 用户角色枚举
export const USER_ROLE = {
  ADMIN: 'admin',
  MANAGER: 'manager',
  MEMBER: 'member',
} as const;

export type UserRole = typeof USER_ROLE[keyof typeof USER_ROLE];

// 用户等级枚举
export const USER_LEVEL = {
  BRONZE: 'bronze',
  SILVER: 'silver',
  GOLD: 'gold',
  PLATINUM: 'platinum',
  DIAMOND: 'diamond',
} as const;

export type UserLevel = typeof USER_LEVEL[keyof typeof USER_LEVEL];

// 订阅状态枚举
export const SUBSCRIPTION_STATUS = {
  ACTIVE: 'active',
  EXPIRED: 'expired',
  CANCELLED: 'cancelled',
  TRIAL: 'trial',
} as const;

export type SubscriptionStatus = typeof SUBSCRIPTION_STATUS[keyof typeof SUBSCRIPTION_STATUS];

// 用户接口
export interface User {
  id: string;
  name: string;
  realname: string;
  email: string;
  avatar?: string | null;
  role: UserRole;
  status: UserStatus;
  level: UserLevel;
  subscriptionStatus: SubscriptionStatus;
  subscriptionExpiry?: Date | null;
  bio?: string | null;
  location?: string | null;
  website?: string | null;
  github?: string | null;
  linkedin?: string | null;
  twitter?: string | null;
  skills?: string | null;
  experience?: string | null;
  education?: string | null;
  timezone?: string | null;
  language?: string | null;
  emailVerified?: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

// 用户配置文件更新数据
export interface UpdateUserProfileData {
  name?: string;
  realname?: string;
  bio?: string;
  location?: string;
  website?: string;
  github?: string;
  linkedin?: string;
  twitter?: string;
  skills?: string;
  experience?: string;
  education?: string;
  timezone?: string;
  language?: string;
}

// 用户注册数据
export interface RegisterUserData {
  name: string;
  realname: string;
  email: string;
  password: string;
  education?: string;
}

// 用户登录数据
export interface LoginUserData {
  email: string;
  password: string;
}

// 用户筛选条件
export interface UserFilters {
  status?: UserStatus;
  role?: UserRole;
  search?: string;
}

// 用户列表查询参数
export interface UserListQuery {
  page: number;
  limit: number;
  status?: UserStatus;
  role?: UserRole;
  search?: string;
}

// 用户列表响应
export interface UserListResponse {
  users: User[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 用户状态更新数据
export interface UpdateUserStatusData {
  userId: string;
  status: UserStatus;
}

// 用户角色更新数据
export interface UpdateUserRoleData {
  userId: string;
  role: UserRole;
}

// 用户状态配置
export const USER_STATUS_CONFIG = {
  [USER_STATUS.PENDING]: {
    label: '待审核',
    color: 'bg-yellow-100 text-yellow-800',
    description: '用户注册待管理员审核'
  },
  [USER_STATUS.ACTIVE]: {
    label: '正常',
    color: 'bg-green-100 text-green-800',
    description: '用户状态正常，可正常使用'
  },
  [USER_STATUS.INACTIVE]: {
    label: '禁用',
    color: 'bg-red-100 text-red-800',
    description: '用户被禁用，无法登录'
  },
  [USER_STATUS.REJECTED]: {
    label: '已拒绝',
    color: 'bg-gray-100 text-gray-800',
    description: '用户注册申请被拒绝'
  },
} as const;

// 用户角色配置
export const USER_ROLE_CONFIG = {
  [USER_ROLE.ADMIN]: {
    label: '管理员',
    color: 'bg-purple-100 text-purple-800',
    description: '系统管理员，拥有所有权限'
  },
  [USER_ROLE.MANAGER]: {
    label: '经理',
    color: 'bg-blue-100 text-blue-800',
    description: '项目经理，可管理项目和成员'
  },
  [USER_ROLE.MEMBER]: {
    label: '成员',
    color: 'bg-gray-100 text-gray-800',
    description: '普通成员，可参与项目'
  },
} as const;

// 用户等级配置
export const USER_LEVEL_CONFIG = {
  [USER_LEVEL.BRONZE]: {
    label: '青铜',
    color: 'bg-amber-100 text-amber-800',
    description: '新手用户',
    icon: '🥉'
  },
  [USER_LEVEL.SILVER]: {
    label: '白银',
    color: 'bg-gray-100 text-gray-800',
    description: '进阶用户',
    icon: '🥈'
  },
  [USER_LEVEL.GOLD]: {
    label: '黄金',
    color: 'bg-yellow-100 text-yellow-800',
    description: '高级用户',
    icon: '🥇'
  },
  [USER_LEVEL.PLATINUM]: {
    label: '铂金',
    color: 'bg-blue-100 text-blue-800',
    description: '专业用户',
    icon: '💎'
  },
  [USER_LEVEL.DIAMOND]: {
    label: '钻石',
    color: 'bg-purple-100 text-purple-800',
    description: '顶级用户',
    icon: '💠'
  },
} as const;

// 订阅状态配置
export const SUBSCRIPTION_STATUS_CONFIG = {
  [SUBSCRIPTION_STATUS.ACTIVE]: {
    label: '活跃',
    color: 'bg-green-100 text-green-800',
    description: '订阅正常'
  },
  [SUBSCRIPTION_STATUS.EXPIRED]: {
    label: '已过期',
    color: 'bg-red-100 text-red-800',
    description: '订阅已过期'
  },
  [SUBSCRIPTION_STATUS.CANCELLED]: {
    label: '已取消',
    color: 'bg-gray-100 text-gray-800',
    description: '订阅已取消'
  },
  [SUBSCRIPTION_STATUS.TRIAL]: {
    label: '试用',
    color: 'bg-blue-100 text-blue-800',
    description: '试用期'
  },
} as const;

// 工具函数
export function getUserStatusConfig(status: string) {
  return USER_STATUS_CONFIG[status as UserStatus] || USER_STATUS_CONFIG[USER_STATUS.PENDING];
}

export function getUserRoleConfig(role: string) {
  return USER_ROLE_CONFIG[role as UserRole] || USER_ROLE_CONFIG[USER_ROLE.MEMBER];
}

export function getUserLevelConfig(level: string) {
  return USER_LEVEL_CONFIG[level as UserLevel] || USER_LEVEL_CONFIG[USER_LEVEL.BRONZE];
}

export function getSubscriptionStatusConfig(status: string) {
  return SUBSCRIPTION_STATUS_CONFIG[status as SubscriptionStatus] || SUBSCRIPTION_STATUS_CONFIG[SUBSCRIPTION_STATUS.TRIAL];
}

// 权限检查函数
export function hasAdminPermission(user: User): boolean {
  return user.role === USER_ROLE.ADMIN;
}

export function hasManagerPermission(user: User): boolean {
  return user.role === USER_ROLE.ADMIN || user.role === USER_ROLE.MANAGER;
}

export function canManageUsers(user: User): boolean {
  return hasAdminPermission(user);
}

export function canManageProjects(user: User): boolean {
  return hasManagerPermission(user);
}

// 用户显示名称
export function getUserDisplayName(user: User): string {
  return user.name;
}

// 用户头像初始字母
export function getUserInitials(user: User): string {
  const displayName = getUserDisplayName(user);
  return displayName.charAt(0).toUpperCase();
}

// 检查用户是否可以登录
export function canUserLogin(user: User): boolean {
  return user.status === USER_STATUS.ACTIVE;
}

// 检查用户是否需要审核
export function isUserPending(user: User): boolean {
  return user.status === USER_STATUS.PENDING;
}

// 检查用户是否被禁用
export function isUserDisabled(user: User): boolean {
  return user.status === USER_STATUS.INACTIVE;
}

// 检查订阅是否有效
export function isSubscriptionActive(user: User): boolean {
  if (user.subscriptionStatus === SUBSCRIPTION_STATUS.ACTIVE) {
    return !user.subscriptionExpiry || user.subscriptionExpiry > new Date();
  }
  return false;
}

// 检查订阅是否即将过期（7天内）
export function isSubscriptionExpiringSoon(user: User): boolean {
  if (!user.subscriptionExpiry) return false;
  const sevenDaysFromNow = new Date();
  sevenDaysFromNow.setDate(sevenDaysFromNow.getDate() + 7);
  return user.subscriptionExpiry <= sevenDaysFromNow;
}

// 获取订阅剩余天数
export function getSubscriptionDaysRemaining(user: User): number | null {
  if (!user.subscriptionExpiry) return null;
  const now = new Date();
  const diffTime = user.subscriptionExpiry.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return Math.max(0, diffDays);
}
