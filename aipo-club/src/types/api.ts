// API 相关的通用类型定义

// 通用API响应类型
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// 分页查询参数
export interface PaginationQuery {
  page: number;
  limit: number;
}

// 分页响应数据
export interface PaginationResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// 排序参数
export interface SortQuery {
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 搜索参数
export interface SearchQuery {
  search?: string;
  searchFields?: string[];
}

// 筛选参数基类
export interface FilterQuery {
  [key: string]: string | number | boolean | Date | string[] | null | undefined;
}

// 完整的查询参数
export interface QueryParams extends PaginationQuery, SortQuery, SearchQuery, FilterQuery {}

// API错误类型
export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, unknown>;
  timestamp: Date;
}

// 文件上传响应
export interface FileUploadResponse {
  url: string;
  filename: string;
  size: number;
  mimetype: string;
}

// 批量操作响应
export interface BatchOperationResponse {
  success: number;
  failed: number;
  total: number;
  errors?: Array<{
    id: string;
    error: string;
  }>;
}

// 统计数据响应
export interface StatsResponse {
  [key: string]: number | string;
}

// 时间范围查询
export interface DateRangeQuery {
  startDate?: Date;
  endDate?: Date;
}

// 导出数据参数
export interface ExportQuery extends QueryParams {
  format: 'csv' | 'xlsx' | 'json';
  fields?: string[];
}

// WebSocket消息类型
export interface WebSocketMessage<T = unknown> {
  type: string;
  data: T;
  timestamp: Date;
  userId?: string;
}

// 通知类型
export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  read: boolean;
  createdAt: Date;
  userId: string;
}

// 系统配置类型
export interface SystemConfig {
  [key: string]: string | number | boolean;
}

// 健康检查响应
export interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy';
  timestamp: Date;
  services: {
    database: 'up' | 'down';
    redis?: 'up' | 'down';
    email?: 'up' | 'down';
  };
  version: string;
  uptime: number;
}

// 审计日志类型
export interface AuditLog {
  id: string;
  action: string;
  resource: string;
  resourceId: string;
  userId: string;
  userEmail: string;
  details?: Record<string, unknown>;
  ipAddress?: string;
  userAgent?: string;
  createdAt: Date;
}

// 权限检查响应
export interface PermissionCheckResponse {
  hasPermission: boolean;
  permissions: string[];
  roles: string[];
}

// 缓存配置
export interface CacheConfig {
  ttl: number; // 生存时间（秒）
  key: string;
  tags?: string[];
}

// 限流配置
export interface RateLimitConfig {
  windowMs: number; // 时间窗口（毫秒）
  max: number; // 最大请求数
  message?: string;
}

// 验证错误类型
export interface ValidationError {
  field: string;
  message: string;
  value?: unknown;
}

// 表单验证响应
export interface ValidationResponse {
  valid: boolean;
  errors: ValidationError[];
}

// 邮件发送响应
export interface EmailResponse {
  messageId: string;
  accepted: string[];
  rejected: string[];
}

// 短信发送响应
export interface SmsResponse {
  messageId: string;
  status: 'sent' | 'failed';
  cost?: number;
}

// 支付响应
export interface PaymentResponse {
  paymentId: string;
  status: 'pending' | 'completed' | 'failed';
  amount: number;
  currency: string;
  paymentUrl?: string;
}

// 地理位置信息
export interface GeoLocation {
  latitude: number;
  longitude: number;
  address?: string;
  city?: string;
  country?: string;
}

// IP信息
export interface IpInfo {
  ip: string;
  country?: string;
  region?: string;
  city?: string;
  timezone?: string;
  isp?: string;
}

// 设备信息
export interface DeviceInfo {
  userAgent: string;
  browser?: string;
  os?: string;
  device?: string;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
}

// 会话信息
export interface SessionInfo {
  sessionId: string;
  userId: string;
  ipAddress: string;
  userAgent: string;
  createdAt: Date;
  lastActivity: Date;
  expiresAt: Date;
}

// 备份信息
export interface BackupInfo {
  id: string;
  filename: string;
  size: number;
  type: 'full' | 'incremental';
  status: 'creating' | 'completed' | 'failed';
  createdAt: Date;
  completedAt?: Date;
}

// 系统监控指标
export interface SystemMetrics {
  cpu: {
    usage: number;
    cores: number;
  };
  memory: {
    used: number;
    total: number;
    usage: number;
  };
  disk: {
    used: number;
    total: number;
    usage: number;
  };
  network: {
    bytesIn: number;
    bytesOut: number;
  };
  timestamp: Date;
}
