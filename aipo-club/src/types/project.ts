/**
 * 项目管理相关的类型定义
 */

// 从配置中心导入所有枚举和类型
export {
  PROJECT_STATUS,
  PROJECT_TYPE,
  PROJECT_PRIORITY,
  MEMBER_ROLE,
  MEMBER_STATUS,
  MILESTONE_STATUS,
  SHARE_TYPE,
  type ProjectStatus,
  type ProjectType,
  type ProjectPriority,
  type MemberRole,
  type MemberStatus,
  type MilestoneStatus,
  type ShareType,
} from '~/config/project-management';

// 分成周期枚举
export const SHARE_PERIOD = {
  MONTHLY: 'monthly',
  QUARTERLY: 'quarterly',
  YEARLY: 'yearly',
  ONE_TIME: 'one_time',
} as const;

export type SharePeriod = typeof SHARE_PERIOD[keyof typeof SHARE_PERIOD];

// 项目健康状态枚举
export const PROJECT_HEALTH_STATUS = {
  HEALTHY: 'healthy',
  WARNING: 'warning',
  CRITICAL: 'critical',
} as const;

export type ProjectHealthStatus = typeof PROJECT_HEALTH_STATUS[keyof typeof PROJECT_HEALTH_STATUS];

// 项目健康状态配置
export const PROJECT_HEALTH_STATUS_CONFIG = {
  [PROJECT_HEALTH_STATUS.HEALTHY]: {
    label: '健康',
    color: 'bg-green-100 text-green-800',
    description: '项目运行正常，无重大问题',
    icon: '✅',
  },
  [PROJECT_HEALTH_STATUS.WARNING]: {
    label: '警告',
    color: 'bg-yellow-100 text-yellow-800',
    description: '项目存在一些问题，需要关注',
    icon: '⚠️',
  },
  [PROJECT_HEALTH_STATUS.CRITICAL]: {
    label: '严重',
    color: 'bg-red-100 text-red-800',
    description: '项目存在严重问题，需要立即处理',
    icon: '🚨',
  },
} as const;

// 项目分类配置（基于数字ID）
export const PROJECT_CATEGORY_CONFIG = {
  '1': { label: '教育培训' },
  '2': { label: '电商零售' },
  '3': { label: '社交娱乐' },
  '4': { label: '工具软件' },
  '5': { label: '企业服务' },
  '6': { label: '金融科技' },
  '7': { label: '医疗健康' },
  '8': { label: '生活服务' },
  '9': { label: '内容媒体' },
  '10': { label: '其他' },
} as const;

// 从配置中心导入工具函数
export {
  getStatusConfig as getProjectStatusConfig,
  getTypeConfig as getProjectTypeConfig,
  getPriorityConfig as getProjectPriorityConfig,
  getRoleConfig as getMemberRoleConfig,
  getMilestoneStatusConfig,
  getAvailableTransitions as getAvailableStatusTransitions,
  isValidTransition as isValidStatusTransition,
} from '~/config/project-management';

// 工具函数
export function getProjectHealthStatusConfig(status: string) {
  return PROJECT_HEALTH_STATUS_CONFIG[status as ProjectHealthStatus] || PROJECT_HEALTH_STATUS_CONFIG[PROJECT_HEALTH_STATUS.HEALTHY];
}

// URL验证函数
export function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

// 版本号验证函数（支持语义化版本）
export function isValidVersion(version: string): boolean {
  const semverRegex = /^(0|[1-9]\d*)\.(0|[1-9]\d*)\.(0|[1-9]\d*)(?:-((?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\.(?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?$/;
  return semverRegex.test(version);
}

// 技术栈文本处理函数
export function formatTechStack(techStack: string | null): string {
  return techStack?.trim() || '';
}

// 技术栈验证函数
export function isValidTechStack(techStack: string): boolean {
  return techStack.length <= 500; // 限制技术栈描述长度
}

import { getAvailableTransitions, PROJECT_STATUS } from '~/config/project-management';

export function getProjectCategoryConfig(category: string) {
  return PROJECT_CATEGORY_CONFIG[category as keyof typeof PROJECT_CATEGORY_CONFIG] || PROJECT_CATEGORY_CONFIG['10'];
}

// 获取状态的下一步建议
export function getNextStatusSuggestions(currentStatus: string) {
  const transitions = getAvailableTransitions(currentStatus);
  // 排除取消状态，优先显示正向流转
  return transitions.filter((status) => status !== PROJECT_STATUS.CANCELLED);
}

// ============ 接口类型定义 ============

export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string | null;
}

export interface ProjectMember {
  id: string;
  projectId: string;
  userId: string;
  role: string; // 使用 string 而不是 MemberRole 以匹配数据库
  status: string; // 使用 string 而不是 MemberStatus 以匹配数据库
  permissions: string;
  profitRate?: number | null;
  joinedAt: Date;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
  createdById: string;
  updatedById: string;
  user: User;
}

export interface ProjectShare {
  id: string;
  projectId: string;
  userId: string;
  shareType: string; // 使用 string 而不是 ShareType 以匹配数据库
  percentage: number;
  amount?: number | null;
  period: SharePeriod;
  startDate: Date;
  endDate?: Date | null;
  conditions?: string | null;
  rules?: string | null;
  status: "active" | "inactive" | "expired";
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
  createdById: string;
  updatedById: string;
  user: User;
}

export interface ProjectMilestone {
  id: string;
  projectId: string;
  name: string;
  description?: string | null;
  status: string; // 使用 string 而不是 MilestoneStatus 以匹配数据库
  priority: string; // 使用 string 而不是 ProjectPriority 以匹配数据库
  dueDate?: Date | null;
  completedAt?: Date | null;
  assignedToId?: string | null;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
  createdById: string;
  updatedById: string;
}

export interface Project {
  id: string;
  name: string;
  code: string;
  description?: string | null;
  type: string; // 使用 string 而不是 ProjectType 以匹配数据库
  status: string; // 使用 string 而不是 ProjectStatus 以匹配数据库
  category?: string | null;
  priority: string; // 使用 string 而不是 ProjectPriority 以匹配数据库
  parentId?: string | null; // 保持 null 以匹配数据库
  poUserId: string;
  startDate?: Date | null;
  endDate?: Date | null;
  deadline?: Date | null;
  budget?: number | null;
  tags: string;
  // 新增项目管理字段
  logo?: string | null; // 项目logo图片地址/URL
  visitUrl?: string | null; // 项目访问地址（生产环境URL）
  wikiUrl?: string | null; // 项目知识库/文档地址
  gitUrl?: string | null; // 代码仓库地址（GitHub/GitLab链接）
  healthStatus: string; // 项目健康状态：'healthy'/'warning'/'critical'
  isDeleted: boolean; // 逻辑删除标记，默认false
  version: string; // 项目版本号，支持语义化版本
  // 扩展管理字段
  techStack?: string | null; // 技术栈信息，文本描述
  lastDeployAt?: Date | null; // 最后部署时间
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
  createdById: string;
  updatedById: string;
  // 关联数据
  user_project_poUserIdTouser?: User;
  user_project_createdByIdTouser?: User;
  user_project_updatedByIdTouser?: User;
  parent?: Pick<Project, 'id' | 'name' | 'code'>;
  children?: Project[];
  members?: ProjectMember[];
  shares?: ProjectShare[];
  milestones?: ProjectMilestone[];
}

// ============ 表单数据类型 ============

// 项目创建表单数据类型
export interface CreateProjectFormData {
  name: string;
  code: string;
  description?: string;
  type: string; // 使用 string 而不是 ProjectType
  status?: string; // 使用 string 而不是 ProjectStatus
  category?: string;
  priority: string; // 使用 string 而不是 ProjectPriority
  parentId?: string;
  poUserId?: string;
  startDate?: Date;
  endDate?: Date;
  deadline?: Date;
  budget?: number;
  // 新增项目管理字段
  logo?: string;
  visitUrl?: string;
  wikiUrl?: string;
  gitUrl?: string;
  healthStatus?: string;
  version?: string;
  techStack?: string;
}

// 项目更新表单数据类型
export interface UpdateProjectFormData extends Partial<CreateProjectFormData> {
  id: string;
}

// 项目筛选条件类型
export interface ProjectFilters {
  status?: string; // 使用 string 而不是 ProjectStatus
  search?: string;
  myProjects?: boolean;
  type?: string; // 使用 string 而不是 ProjectType
  priority?: string; // 使用 string 而不是 ProjectPriority
}

// 项目列表查询参数类型
export interface ProjectListQuery {
  page: number;
  limit: number;
  status?: string; // 使用 string 而不是 ProjectStatus
  search?: string;
  myProjects?: boolean;
}

// 项目列表响应类型
export interface ProjectListResponse {
  projects: Project[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 成员管理相关类型
export interface AddMemberFormData {
  projectId: string;
  userId: string;
  role: string; // 使用 string 而不是 MemberRole
  permissions: string[];
  profitRate?: number;
}

export interface UpdateMemberRoleData {
  projectId: string;
  userId: string;
  role: string; // 使用 string 而不是 MemberRole
}

export interface UpdateMemberProfitRateData {
  projectId: string;
  userId: string;
  profitRate: number;
}

// 分成管理相关类型
export interface CreateShareFormData {
  projectId: string;
  userId: string;
  shareType: string; // 使用 string 而不是 ShareType
  percentage: number;
  amount?: number;
  period: SharePeriod;
  startDate: Date;
  endDate?: Date;
  conditions?: string;
  rules?: string;
}

// 里程碑管理相关类型
export interface CreateMilestoneFormData {
  projectId: string;
  name: string;
  description?: string;
  priority: string; // 使用 string 而不是 ProjectPriority
  dueDate?: Date;
  assignedToId?: string;
}

export interface UpdateMilestoneStatusData {
  id: string;
  status: string; // 使用 string 而不是 MilestoneStatus
}
