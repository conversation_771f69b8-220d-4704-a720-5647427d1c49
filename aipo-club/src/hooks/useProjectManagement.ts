/**
 * 项目管理 Hook
 * 
 * 封装所有项目管理相关的逻辑
 * 提供统一的管理接口
 */

import { useMemo } from 'react';
import { 
  // 项目状态
  PROJECT_STATUS_CONFIG, 
  getStatusConfig, 
  getStatusLabel,
  getStatusOptions,
  getAvailableTransitions,
  isValidTransition,
  type ProjectStatus,
  
  // 项目类型
  PROJECT_TYPE_CONFIG,
  getTypeConfig,
  getTypeLabel,
  getTypeOptions,
  type ProjectType,
  
  // 优先级
  PROJECT_PRIORITY_CONFIG,
  getPriorityConfig,
  getPriorityLabel,
  getPriorityOptions,
  type ProjectPriority,
  
  // 成员角色
  MEMBER_ROLE_CONFIG,
  getRoleConfig,
  getRoleLabel,
  getRoleOptions,
  type MemberRole,
  
  // 成员状态
  MEMBER_STATUS_CONFIG,
  getMemberStatusConfig,
  getMemberStatusLabel,
  type MemberStatus,
  
  // 里程碑状态
  MILESTONE_STATUS_CONFIG,
  getMilestoneStatusConfig,
  getMilestoneStatusLabel,
  type MilestoneStatus,
} from '~/config/project-management';

export function useProjectManagement() {
  // 项目状态相关
  const statusConfig = PROJECT_STATUS_CONFIG;
  const statusOptions = useMemo(() => getStatusOptions(), []);
  
  // 项目类型相关
  const typeConfig = PROJECT_TYPE_CONFIG;
  const typeOptions = useMemo(() => getTypeOptions(), []);
  
  // 优先级相关
  const priorityConfig = PROJECT_PRIORITY_CONFIG;
  const priorityOptions = useMemo(() => getPriorityOptions(), []);
  
  // 成员角色相关
  const roleConfig = MEMBER_ROLE_CONFIG;
  const roleOptions = useMemo(() => getRoleOptions(), []);
  
  // 成员状态相关
  const memberStatusConfig = MEMBER_STATUS_CONFIG;
  
  // 里程碑状态相关
  const milestoneStatusConfig = MILESTONE_STATUS_CONFIG;
  
  return {
    // 配置对象
    statusConfig,
    typeConfig,
    priorityConfig,
    roleConfig,
    memberStatusConfig,
    milestoneStatusConfig,
    
    // 选项数组
    statusOptions,
    typeOptions,
    priorityOptions,
    roleOptions,
    
    // 状态相关方法
    getStatusConfig,
    getStatusLabel,
    getAvailableTransitions,
    isValidTransition,
    
    // 类型相关方法
    getTypeConfig,
    getTypeLabel,
    
    // 优先级相关方法
    getPriorityConfig,
    getPriorityLabel,
    
    // 角色相关方法
    getRoleConfig,
    getRoleLabel,
    
    // 成员状态相关方法
    getMemberStatusConfig,
    getMemberStatusLabel,
    
    // 里程碑状态相关方法
    getMilestoneStatusConfig,
    getMilestoneStatusLabel,
  };
}

// 导出类型
export type {
  ProjectStatus,
  ProjectType,
  ProjectPriority,
  MemberRole,
  MemberStatus,
  MilestoneStatus,
};
