"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { redirect } from "next/navigation";
import Image from "next/image";
import { api } from "~/trpc/react";
import { AdminLayout } from "~/components/admin-layout";
import { UserLevelBadge, SubscriptionStatusBadge, SubscriptionCountdown } from "~/components/ui/UserBadges";
import { InviteCodeGenerator, InviteCodeCard, InviteeCard } from "~/components/ui/InviteCodeComponents";
import { AvatarUpload } from "~/components/ui/ImageUpload";
import { buildThumbnailUrl, buildImageUrl } from "~/lib/oss";

export default function ProfilePage() {
  const { data: session, status } = useSession();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    realname: "",
    avatar: "",
    birthday: "",
    gender: "",
    country: "",
    city: "",
    bio: "",
    occupation: "",
    education: "",
    github: "",
    linkedin: "",
    twitter: "",
    website: "",
    skills: "",
    interests: "",
  });

  // 始终调用所有Hooks，避免条件性调用
  const { data: userProfile, isLoading, refetch } = api.user.getProfile.useQuery(undefined, {
    enabled: !!session,
  });
  const { data: myInviteCodes, refetch: refetchInviteCodes } = api.user.getMyInviteCodes.useQuery({ page: 1, limit: 10 }, {
    enabled: !!session,
  });
  const { data: myInvitees } = api.user.getMyInvitees.useQuery({ page: 1, limit: 10 }, {
    enabled: !!session,
  });

  // 所有Hooks必须在条件性渲染之前调用
  const updateProfileMutation = api.user.updateProfile.useMutation({
    onSuccess: () => {
      setIsEditing(false);
      void refetch();
      alert("资料更新成功！");
    },
    onError: (error) => {
      alert(`更新失败: ${error.message}`);
    },
  });

  // 使用useEffect来处理数据加载后的状态更新
  useEffect(() => {
    if (userProfile) {
      setFormData({
        name: userProfile.name || "",
        realname: userProfile.realname || "",
        avatar: userProfile.avatar || "",
        birthday: userProfile.birthday ? new Date(userProfile.birthday).toISOString().split('T')[0]! : "",
        gender: userProfile.gender || "",
        country: userProfile.country || "",
        city: userProfile.city || "",
        bio: userProfile.bio || "",
        occupation: userProfile.occupation || "",
        education: userProfile.education || "",
        github: userProfile.github || "",
        linkedin: userProfile.linkedin || "",
        twitter: userProfile.twitter || "",
        website: userProfile.website || "",
        skills: userProfile.skills || "",
        interests: userProfile.interests || "",
      });
    }
  }, [userProfile]);

  // 在所有Hooks调用之后进行条件性渲染
  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    );
  }

  if (!session) {
    redirect("/login");
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const updateData = {
      name: formData.name || undefined,
      realname: formData.realname || undefined,
      avatar: formData.avatar || undefined,
      birthday: formData.birthday ? new Date(formData.birthday) : undefined,
      gender: (formData.gender as "male" | "female" | "unknown") || undefined,
      country: formData.country || undefined,
      city: formData.city || undefined,
      bio: formData.bio || undefined,
      occupation: formData.occupation || undefined,
      education: formData.education || undefined,
      github: formData.github || undefined,
      linkedin: formData.linkedin || undefined,
      twitter: formData.twitter || undefined,
      website: formData.website || undefined,
      skills: formData.skills || undefined,
      interests: formData.interests || undefined,
    };

    await updateProfileMutation.mutateAsync(updateData);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { label: "待审核", color: "bg-yellow-100 text-yellow-800" },
      active: { label: "活跃", color: "bg-green-100 text-green-800" },
      inactive: { label: "非活跃", color: "bg-gray-100 text-gray-800" },
      rejected: { label: "已拒绝", color: "bg-red-100 text-red-800" },
    };
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    );
  };

  const getRoleBadge = (role: string) => {
    const roleConfig = {
      admin: { label: "超管", color: "bg-purple-100 text-purple-800" },
      manager: { label: "经理", color: "bg-blue-100 text-blue-800" },
      member: { label: "成员", color: "bg-gray-100 text-gray-800" },
    };
    const config = roleConfig[role as keyof typeof roleConfig] || roleConfig.member;
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    );
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    );
  }

  return (
    <AdminLayout>
      {/* 页面标题 */}
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-gray-900">个人资料</h1>
        <p className="mt-1 text-sm text-gray-600">管理您的个人信息和偏好设置</p>
      </div>

      {userProfile && (
        <div className="bg-white shadow rounded-lg">
            {/* 头部信息 */}
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    {userProfile.avatar ? (
                      <div className="relative h-16 w-16 rounded-full overflow-hidden bg-gray-100 border-2 border-gray-200">
                        <Image
                          src={buildThumbnailUrl(userProfile.avatar, 64, 64)}
                          alt={userProfile.name || "用户头像"}
                          fill
                          className="object-cover"
                          onError={(e) => {
                            // 如果缩略图加载失败，尝试加载原图
                            const target = e.target as HTMLImageElement;
                            target.src = buildImageUrl(userProfile.avatar!);
                          }}
                        />
                      </div>
                    ) : (
                      <div className="h-16 w-16 rounded-full bg-gray-300 flex items-center justify-center border-2 border-gray-200">
                        <span className="text-xl font-medium text-gray-700">
                          {userProfile.name?.charAt(0) || "?"}
                        </span>
                      </div>
                    )}
                  </div>
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900">{userProfile.name}</h2>
                    <p className="text-gray-600">{userProfile.realname}</p>
                    <p className="text-gray-500 text-sm">{userProfile.email}</p>
                    <div className="flex items-center space-x-2 mt-2">
                      {getRoleBadge(userProfile.role)}
                      {getStatusBadge(userProfile.status)}
                      <UserLevelBadge level={userProfile.level as any} />
                      <SubscriptionStatusBadge status={userProfile.subscriptionStatus as any} />
                    </div>
                    <SubscriptionCountdown user={userProfile as any} className="mt-2" />
                  </div>
                </div>
                <button
                  onClick={() => setIsEditing(!isEditing)}
                  className="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700"
                >
                  {isEditing ? "取消编辑" : "编辑资料"}
                </button>
              </div>
            </div>

            {/* 表单内容 */}
            <div className="p-6">
              {isEditing ? (
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">昵称</label>
                      <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">姓名</label>
                      <input
                        type="text"
                        name="realname"
                        value={formData.realname}
                        onChange={handleChange}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">头像</label>
                      <AvatarUpload
                        value={formData.avatar}
                        onChange={(url) => setFormData(prev => ({ ...prev, avatar: url }))}
                        onError={(error) => alert(`头像上传失败: ${error}`)}
                        disabled={updateProfileMutation.isPending}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">生日</label>
                      <input
                        type="date"
                        name="birthday"
                        value={formData.birthday}
                        onChange={handleChange}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">性别</label>
                      <select
                        name="gender"
                        value={formData.gender}
                        onChange={handleChange}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                      >
                        <option value="">请选择</option>
                        <option value="male">男</option>
                        <option value="female">女</option>
                        <option value="unknown">不愿透露</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">职业</label>
                      <input
                        type="text"
                        name="occupation"
                        value={formData.occupation}
                        onChange={handleChange}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">国家</label>
                      <input
                        type="text"
                        name="country"
                        value={formData.country}
                        onChange={handleChange}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">城市</label>
                      <input
                        type="text"
                        name="city"
                        value={formData.city}
                        onChange={handleChange}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">个人简介</label>
                    <textarea
                      name="bio"
                      rows={3}
                      value={formData.bio}
                      onChange={handleChange}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">教育信息</label>
                    <textarea
                      name="education"
                      rows={3}
                      value={formData.education}
                      onChange={handleChange}
                      placeholder="请输入您的教育背景"
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">GitHub</label>
                      <input
                        type="url"
                        name="github"
                        value={formData.github}
                        onChange={handleChange}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">LinkedIn</label>
                      <input
                        type="url"
                        name="linkedin"
                        value={formData.linkedin}
                        onChange={handleChange}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Twitter</label>
                      <input
                        type="url"
                        name="twitter"
                        value={formData.twitter}
                        onChange={handleChange}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">个人网站</label>
                      <input
                        type="url"
                        name="website"
                        value={formData.website}
                        onChange={handleChange}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">技能</label>
                      <textarea
                        name="skills"
                        rows={3}
                        value={formData.skills}
                        onChange={handleChange}
                        placeholder="请用逗号分隔多个技能"
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">兴趣爱好</label>
                      <textarea
                        name="interests"
                        rows={3}
                        value={formData.interests}
                        onChange={handleChange}
                        placeholder="请用逗号分隔多个兴趣"
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                      />
                    </div>
                  </div>

                  <div className="flex justify-end space-x-4">
                    <button
                      type="button"
                      onClick={() => setIsEditing(false)}
                      className="bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-300"
                    >
                      取消
                    </button>
                    <button
                      type="submit"
                      disabled={updateProfileMutation.isPending}
                      className="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700 disabled:opacity-50"
                    >
                      {updateProfileMutation.isPending ? "保存中..." : "保存"}
                    </button>
                  </div>
                </form>
              ) : (
                <div className="space-y-6">
                  {/* 基本信息 */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">基本信息</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <span className="text-sm text-gray-500">昵称：</span>
                        <span className="ml-2">{userProfile.name}</span>
                      </div>
                      <div>
                        <span className="text-sm text-gray-500">姓名：</span>
                        <span className="ml-2">{userProfile.realname}</span>
                      </div>
                      <div>
                        <span className="text-sm text-gray-500">邮箱：</span>
                        <span className="ml-2">{userProfile.email}</span>
                      </div>
                      <div>
                        <span className="text-sm text-gray-500">手机：</span>
                        <span className="ml-2">{userProfile.phone}</span>
                      </div>
                      <div>
                        <span className="text-sm text-gray-500">生日：</span>
                        <span className="ml-2">
                          {userProfile.birthday ? new Date(userProfile.birthday).toLocaleDateString('zh-CN') : "未设置"}
                        </span>
                      </div>
                      <div>
                        <span className="text-sm text-gray-500">性别：</span>
                        <span className="ml-2">
                          {userProfile.gender === "male" ? "男" : userProfile.gender === "female" ? "女" : "未设置"}
                        </span>
                      </div>
                      <div>
                        <span className="text-sm text-gray-500">职业：</span>
                        <span className="ml-2">{userProfile.occupation || "未设置"}</span>
                      </div>
                      <div>
                        <span className="text-sm text-gray-500">地区：</span>
                        <span className="ml-2">
                          {[userProfile.country, userProfile.city].filter(Boolean).join(", ") || "未设置"}
                        </span>
                      </div>
                      <div>
                        <span className="text-sm text-gray-500">用户等级：</span>
                        <span className="ml-2">
                          <UserLevelBadge level={userProfile.level as any} />
                        </span>
                      </div>
                      <div>
                        <span className="text-sm text-gray-500">订阅状态：</span>
                        <span className="ml-2">
                          <SubscriptionStatusBadge status={userProfile.subscriptionStatus as any} />
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* 个人简介 */}
                  {userProfile.bio && (
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-4">个人简介</h3>
                      <p className="text-gray-700">{userProfile.bio}</p>
                    </div>
                  )}

                  {/* 教育信息 */}
                  {userProfile.education && (
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-4">教育信息</h3>
                      <p className="text-gray-700">{userProfile.education}</p>
                    </div>
                  )}

                  {/* 社交链接 */}
                  {(userProfile.github || userProfile.linkedin || userProfile.twitter || userProfile.website) && (
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-4">社交链接</h3>
                      <div className="flex flex-wrap gap-4">
                        {userProfile.github && (
                          <a href={userProfile.github} target="_blank" rel="noopener noreferrer" className="text-indigo-600 hover:text-indigo-500">
                            GitHub
                          </a>
                        )}
                        {userProfile.linkedin && (
                          <a href={userProfile.linkedin} target="_blank" rel="noopener noreferrer" className="text-indigo-600 hover:text-indigo-500">
                            LinkedIn
                          </a>
                        )}
                        {userProfile.twitter && (
                          <a href={userProfile.twitter} target="_blank" rel="noopener noreferrer" className="text-indigo-600 hover:text-indigo-500">
                            Twitter
                          </a>
                        )}
                        {userProfile.website && (
                          <a href={userProfile.website} target="_blank" rel="noopener noreferrer" className="text-indigo-600 hover:text-indigo-500">
                            个人网站
                          </a>
                        )}
                      </div>
                    </div>
                  )}

                  {/* 技能和兴趣 */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {userProfile.skills && (
                      <div>
                        <h3 className="text-lg font-medium text-gray-900 mb-4">技能</h3>
                        <p className="text-gray-700">{userProfile.skills}</p>
                      </div>
                    )}
                    {userProfile.interests && (
                      <div>
                        <h3 className="text-lg font-medium text-gray-900 mb-4">兴趣爱好</h3>
                        <p className="text-gray-700">{userProfile.interests}</p>
                      </div>
                    )}
                  </div>

                  {/* 系统信息 */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">系统信息</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <span className="text-sm text-gray-500">加入时间：</span>
                        <span className="ml-2">{new Date(userProfile.joinDate).toLocaleDateString('zh-CN')}</span>
                      </div>
                      <div>
                        <span className="text-sm text-gray-500">最后登录：</span>
                        <span className="ml-2">
                          {userProfile.lastLoginAt ? new Date(userProfile.lastLoginAt).toLocaleDateString('zh-CN') : "未记录"}
                        </span>
                      </div>
                      {userProfile.invitedBy && (
                        <div>
                          <span className="text-sm text-gray-500">邀请人：</span>
                          <span className="ml-2">通过邀请码加入</span>
                        </div>
                      )}
                      {userProfile.inviteCode && (
                        <div>
                          <span className="text-sm text-gray-500">使用邀请码：</span>
                          <span className="ml-2 font-mono text-sm">{userProfile.inviteCode}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 邀请管理 */}
                  <div>
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-medium text-gray-900">邀请管理</h3>
                      <InviteCodeGenerator onGenerated={() => void refetchInviteCodes()} />
                    </div>

                    {/* 我的邀请码 */}
                    <div className="mb-6">
                      <h4 className="text-md font-medium text-gray-700 mb-3">我的邀请码</h4>
                      {myInviteCodes && myInviteCodes.inviteCodes.length > 0 ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {myInviteCodes.inviteCodes.map((inviteCode) => (
                            <InviteCodeCard key={inviteCode.id} inviteCode={inviteCode} />
                          ))}
                        </div>
                      ) : (
                        <p className="text-gray-500 text-sm">暂无邀请码</p>
                      )}
                    </div>

                    {/* 我邀请的用户 */}
                    <div>
                      <h4 className="text-md font-medium text-gray-700 mb-3">我邀请的用户</h4>
                      {myInvitees && myInvitees.invitees.length > 0 ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {myInvitees.invitees.map((invitee) => (
                            <InviteeCard key={invitee.id} invitee={invitee} />
                          ))}
                        </div>
                      ) : (
                        <p className="text-gray-500 text-sm">暂无邀请用户</p>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
    </AdminLayout>
  );
}
