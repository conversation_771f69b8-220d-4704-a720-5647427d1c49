import { NextResponse } from 'next/server';
import { auth } from '~/server/auth';
import { testOSSConnection, getAliOSSConfig } from '~/lib/aliyun-oss';

/**
 * 测试OSS连接API
 * GET /api/upload/test
 */
export async function GET() {
  try {
    // 验证用户身份（可选，根据需要决定是否需要登录）
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 获取OSS配置
    let ossConfig;
    try {
      ossConfig = getAliOSSConfig();
    } catch (configError) {
      return NextResponse.json({
        success: false,
        error: 'OSS配置错误',
        details: configError instanceof Error ? configError.message : 'Unknown config error',
        config: {
          region: process.env.OSS_REGION ? '✓' : '✗',
          accessKeyId: process.env.OSS_ACCESS_KEY_ID ? '✓' : '✗',
          accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET ? '✓' : '✗',
          bucket: process.env.OSS_BUCKET ? '✓' : '✗',
          baseUrl: process.env.NEXT_PUBLIC_OSS_BASE_URL ? '✓' : '✗',
        }
      }, { status: 500 });
    }

    // 测试OSS连接
    const connectionTest = await testOSSConnection(ossConfig);

    if (connectionTest) {
      return NextResponse.json({
        success: true,
        message: 'OSS连接测试成功',
        config: {
          region: ossConfig.region,
          bucket: ossConfig.bucket,
          endpoint: ossConfig.endpoint,
          baseUrl: process.env.NEXT_PUBLIC_OSS_BASE_URL,
        }
      });
    } else {
      return NextResponse.json({
        success: false,
        error: 'OSS连接测试失败',
        message: '请检查OSS配置和网络连接',
        config: {
          region: ossConfig.region,
          bucket: ossConfig.bucket,
          endpoint: ossConfig.endpoint,
          baseUrl: process.env.NEXT_PUBLIC_OSS_BASE_URL,
        }
      }, { status: 500 });
    }

  } catch (error) {
    console.error('OSS测试错误:', error);
    return NextResponse.json({
      success: false,
      error: '测试过程中发生错误',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
