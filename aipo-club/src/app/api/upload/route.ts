import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { auth } from '~/server/auth';
import { uploadToAliOSS, getAliOSSConfig, buildOSSUrl, deleteFromAliOSS } from '~/lib/aliyun-oss';

/**
 * 文件上传API
 * POST /api/upload
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;
    const key = formData.get('key') as string;

    if (!file) {
      return NextResponse.json(
        { error: '未找到文件' },
        { status: 400 }
      );
    }

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: '不支持的文件类型' },
        { status: 400 }
      );
    }

    // 验证文件大小（5MB）
    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: '文件大小超过限制' },
        { status: 400 }
      );
    }

    // 转换文件为Buffer
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    try {
      // 获取OSS配置
      const ossConfig = getAliOSSConfig();

      // 上传到阿里云OSS
      const uploadResult = await uploadToAliOSS(buffer, key, ossConfig, {
        contentType: file.type,
        headers: {
          'Cache-Control': 'public, max-age=31536000', // 1年缓存
        },
      });

      if (!uploadResult.success) {
        return NextResponse.json(
          { error: uploadResult.error || '上传到OSS失败' },
          { status: 500 }
        );
      }

      // 构建完整的URL
      const url = buildOSSUrl(key, ossConfig);

      // 这里可以将文件信息保存到数据库
      // await db.file.create({
      //   data: {
      //     key,
      //     url,
      //     originalName: file.name,
      //     size: file.size,
      //     mimeType: file.type,
      //     uploadedBy: session.user.id,
      //   }
      // });

      console.log(`文件上传成功: ${key} -> ${url}`);

      return NextResponse.json({
        success: true,
        url,
        key,
        size: file.size,
        type: file.type,
      });

    } catch (ossError) {
      console.error('OSS上传错误:', ossError);
      return NextResponse.json(
        { error: `OSS配置错误: ${ossError instanceof Error ? ossError.message : 'Unknown error'}` },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('文件上传失败:', error);
    return NextResponse.json(
      { error: '上传失败' },
      { status: 500 }
    );
  }
}

/**
 * 文件删除API
 * DELETE /api/upload
 */
export async function DELETE(request: NextRequest) {
  try {
    // 验证用户身份
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const { key } = await request.json();

    if (!key) {
      return NextResponse.json(
        { error: '缺少文件key' },
        { status: 400 }
      );
    }

    try {
      // 获取OSS配置并删除文件
      const ossConfig = getAliOSSConfig();
      const deleteSuccess = await deleteFromAliOSS(key, ossConfig);

      if (!deleteSuccess) {
        return NextResponse.json(
          { error: '从OSS删除文件失败' },
          { status: 500 }
        );
      }

      // 从数据库中删除文件记录
      // await db.file.deleteMany({
      //   where: {
      //     key,
      //     uploadedBy: session.user.id, // 只能删除自己上传的文件
      //   }
      // });

      console.log(`文件删除成功: ${key}`);
    } catch (ossError) {
      console.error('OSS删除错误:', ossError);
      return NextResponse.json(
        { error: `删除失败: ${ossError instanceof Error ? ossError.message : 'Unknown error'}` },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: '文件删除成功',
    });

  } catch (error) {
    console.error('文件删除失败:', error);
    return NextResponse.json(
      { error: '删除失败' },
      { status: 500 }
    );
  }
}

/**
 * 获取上传配置API
 * GET /api/upload/config
 */
export async function GET() {
  try {
    // 返回前端需要的配置信息（不包含敏感信息）
    return NextResponse.json({
      maxFileSize: 5 * 1024 * 1024, // 5MB
      allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
      baseUrl: process.env.NEXT_PUBLIC_OSS_BASE_URL,
    });
  } catch (error) {
    console.error('获取配置失败:', error);
    return NextResponse.json(
      { error: '获取配置失败' },
      { status: 500 }
    );
  }
}
