import { NextResponse } from 'next/server';
import { db } from '~/server/db';

/**
 * 健康检查API
 * GET /api/health
 * 
 * 用于阿里云函数计算的健康检查
 * 检查数据库连接和基本服务状态
 */
export async function GET() {
  try {
    const startTime = Date.now();
    
    // 检查数据库连接
    let dbStatus = 'unknown';
    let dbLatency = 0;
    
    try {
      const dbStartTime = Date.now();
      // 执行简单的数据库查询来测试连接
      await db.$queryRaw`SELECT 1 as health_check`;
      dbLatency = Date.now() - dbStartTime;
      dbStatus = 'healthy';
    } catch (dbError) {
      console.error('数据库健康检查失败:', dbError);
      dbStatus = 'unhealthy';
      dbLatency = Date.now() - startTime;
    }
    
    const totalLatency = Date.now() - startTime;
    
    // 检查环境变量配置
    const envCheck = {
      DATABASE_URL: !!process.env.DATABASE_URL,
      AUTH_SECRET: !!process.env.AUTH_SECRET,
      SMTP_HOST: !!process.env.SMTP_HOST,
      OSS_BUCKET: !!process.env.OSS_BUCKET,
    };
    
    const allEnvConfigured = Object.values(envCheck).every(Boolean);
    
    // 确定整体健康状态
    const isHealthy = dbStatus === 'healthy' && allEnvConfigured;
    const status = isHealthy ? 'healthy' : 'unhealthy';
    
    const healthData = {
      status,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      checks: {
        database: {
          status: dbStatus,
          latency: `${dbLatency}ms`,
        },
        environment: {
          status: allEnvConfigured ? 'healthy' : 'unhealthy',
          configured: envCheck,
        },
      },
      performance: {
        totalLatency: `${totalLatency}ms`,
        memoryUsage: process.memoryUsage(),
      },
    };
    
    // 如果不健康，返回503状态码
    const statusCode = isHealthy ? 200 : 503;
    
    return NextResponse.json(healthData, { 
      status: statusCode,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });
    
  } catch (error) {
    console.error('健康检查失败:', error);
    
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
      checks: {
        database: { status: 'unknown' },
        environment: { status: 'unknown' },
      },
    }, { 
      status: 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });
  }
}

/**
 * HEAD请求支持（某些健康检查工具使用HEAD请求）
 */
export async function HEAD() {
  try {
    // 简单的数据库连接检查
    await db.$queryRaw`SELECT 1 as health_check`;
    return new NextResponse(null, { status: 200 });
  } catch {
    return new NextResponse(null, { status: 503 });
  }
}
