"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import Image from "next/image";
import { redirect } from "next/navigation";
import { api } from "~/trpc/react";

export default function UsersPage() {
  const { data: session, status } = useSession();
  const [currentPage, setCurrentPage] = useState(1);
  const [filters, setFilters] = useState({
    search: "",
    skills: "",
    location: "",
  });

  // 获取当前用户资料以判断权限
  const { data: userProfile } = api.user.getProfile.useQuery(undefined, {
    enabled: !!session?.user?.id,
  });

  // 获取用户列表 - 基于权限返回不同数据
  const { data: usersData, isLoading } = api.user.getPublicUsers.useQuery({
    page: currentPage,
    limit: 12,
    search: filters.search || undefined,
    skills: filters.skills || undefined,
    location: filters.location || undefined,
  }, {
    enabled: !!session,
    staleTime: 60 * 1000,
  });

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    );
  }

  if (!session) {
    redirect("/login");
  }

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1);
  };

  const isAdmin = userProfile?.role === 'admin' || userProfile?.role === 'manager';

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">用户中心</h1>
          <p className="mt-2 text-gray-600">发现和联系AIPO俱乐部的成员</p>
        </div>

        {/* 搜索和筛选 */}
        <div className="bg-white shadow rounded-lg p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                搜索用户
              </label>
              <input
                type="text"
                value={filters.search}
                onChange={(e) => handleFilterChange("search", e.target.value)}
                placeholder="搜索昵称、技能或简介"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                技能筛选
              </label>
              <input
                type="text"
                value={filters.skills}
                onChange={(e) => handleFilterChange("skills", e.target.value)}
                placeholder="如：前端、设计、产品"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                地区筛选
              </label>
              <input
                type="text"
                value={filters.location}
                onChange={(e) => handleFilterChange("location", e.target.value)}
                placeholder="如：北京、上海、远程"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>
          </div>
          
          {isAdmin && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <Link
                href="/admin/users"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                用户管理
              </Link>
            </div>
          )}
        </div>

        {/* 用户列表 */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="bg-white rounded-lg shadow p-6 animate-pulse">
                <div className="w-16 h-16 bg-gray-300 rounded-full mx-auto mb-4"></div>
                <div className="h-4 bg-gray-300 rounded mb-2"></div>
                <div className="h-3 bg-gray-300 rounded mb-4"></div>
                <div className="flex space-x-1">
                  <div className="h-6 w-12 bg-gray-300 rounded"></div>
                  <div className="h-6 w-16 bg-gray-300 rounded"></div>
                </div>
              </div>
            ))}
          </div>
        ) : usersData?.users && usersData.users.length > 0 ? (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {usersData.users.map((user) => (
                <div key={user.id} className="bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6">
                  {/* 用户头像 */}
                  <div className="text-center mb-4">
                    {user.avatar ? (
                      <Image
                        className="w-16 h-16 rounded-full mx-auto"
                        src={user.avatar}
                        alt={user.name || "用户头像"}
                        width={64}
                        height={64}
                      />
                    ) : (
                      <div className="w-16 h-16 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center mx-auto">
                        <span className="text-white text-xl font-bold">
                          {user.name.charAt(0)}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* 用户信息 */}
                  <div className="text-center">
                    <h3 className="text-lg font-medium text-gray-900 mb-1">{user.name}</h3>
                    {user.bio && (
                      <p className="text-sm text-gray-600 mb-3 line-clamp-2">{user.bio}</p>
                    )}
                    
                    {/* 技能标签 */}
                    {user.skills && user.skills.length > 0 && (
                      <div className="flex flex-wrap justify-center gap-1 mb-4">
                        {user.skills.slice(0, 3).map((skill, index) => (
                          <span key={index} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {skill}
                          </span>
                        ))}
                        {user.skills.length > 3 && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                            +{user.skills.length - 3}
                          </span>
                        )}
                      </div>
                    )}

                    {/* 角色标识 */}
                    {isAdmin && (
                      <div className="mb-3">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          user.role === 'admin' ? 'bg-purple-100 text-purple-800' :
                          user.role === 'manager' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {user.role === 'admin' ? '超管' : user.role === 'manager' ? '经理' : '成员'}
                        </span>
                      </div>
                    )}

                    {/* 查看详情按钮 */}
                    <Link
                      href={`/users/${user.id}`}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 transition-colors"
                    >
                      查看详情
                    </Link>
                  </div>
                </div>
              ))}
            </div>

            {/* 分页 */}
            {usersData.pages > 1 && (
              <div className="mt-8 flex items-center justify-center">
                <div className="flex space-x-2">
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={currentPage === 1}
                    className="px-3 py-2 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                  >
                    上一页
                  </button>
                  <span className="px-3 py-2 text-sm text-gray-700">
                    第 {currentPage} / {usersData.pages} 页
                  </span>
                  <button
                    onClick={() => setCurrentPage(prev => Math.min(usersData.pages, prev + 1))}
                    disabled={currentPage === usersData.pages}
                    className="px-3 py-2 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                  >
                    下一页
                  </button>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-12">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">暂无用户</h3>
            <p className="mt-1 text-sm text-gray-500">没有找到符合条件的用户</p>
          </div>
        )}
      </div>
    </div>
  );
}
