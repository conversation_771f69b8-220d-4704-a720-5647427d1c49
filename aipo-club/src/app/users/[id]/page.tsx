"use client";

import { useState, use } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import Image from "next/image";
import { redirect } from "next/navigation";
import { api } from "~/trpc/react";
import { ProjectStatusBadge } from "~/components/project/ProjectStatusComponents";
import { getProjectTypeConfig } from "~/types/project";
import { buildThumbnailUrl, buildImageUrl } from "~/lib/oss";

interface UserDetailPageProps {
  params: Promise<{ id: string }>;
}

export default function UserDetailPage({ params }: UserDetailPageProps) {
  const resolvedParams = use(params);
  const { data: session, status } = useSession();
  const [activeTab, setActiveTab] = useState("overview");

  // 获取当前用户资料
  const { data: userProfile } = api.user.getProfile.useQuery(undefined, {
    enabled: !!session?.user?.id,
  });

  // 获取目标用户信息
  const { data: targetUser, isLoading, error } = api.user.getPublicUserById.useQuery({
    id: resolvedParams.id,
  }, {
    enabled: !!resolvedParams.id && !!session,
  });

  // 查询用户参与的项目
  const { data: projectsData, isLoading: projectsLoading } = api.project.getUserProjects.useQuery({
    userId: resolvedParams.id,
    page: 1,
    limit: 50,
  }, {
    enabled: !!session && activeTab === "projects" && !!resolvedParams.id,
  });

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    );
  }

  if (!session) {
    redirect("/login");
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="bg-white rounded-lg shadow p-8">
              <div className="flex items-center space-x-6">
                <div className="w-24 h-24 bg-gray-300 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-8 bg-gray-300 rounded mb-2"></div>
                  <div className="h-4 bg-gray-300 rounded mb-4"></div>
                  <div className="flex space-x-2">
                    <div className="h-6 w-16 bg-gray-300 rounded"></div>
                    <div className="h-6 w-20 bg-gray-300 rounded"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !targetUser) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-12">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            <h3 className="mt-2 text-lg font-medium text-gray-900">用户不存在</h3>
            <p className="mt-1 text-sm text-gray-500">请检查用户ID或联系管理员</p>
            <div className="mt-6">
              <Link
                href="/users"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
              >
                返回用户列表
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const isAdmin = userProfile?.role === 'admin' || userProfile?.role === 'manager';
  const isOwnProfile = userProfile?.id === targetUser.id;

  const tabs = [
    { id: "overview", name: "概览", icon: "📊" },
    { id: "projects", name: "项目", icon: "📋" },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 返回导航 */}
        <nav className="flex mb-6" aria-label="Breadcrumb">
          <ol className="flex items-center space-x-4">
            <li>
              <Link href="/users" className="text-gray-400 hover:text-gray-500">
                用户中心
              </Link>
            </li>
            <li>
              <div className="flex items-center">
                <svg className="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
                <span className="ml-4 text-sm font-medium text-gray-500">{targetUser.name}</span>
              </div>
            </li>
          </ol>
        </nav>

        {/* 用户信息头部 */}
        <div className="bg-white shadow rounded-lg p-8 mb-6">
          <div className="flex items-start space-x-6">
            {/* 头像 */}
            <div className="flex-shrink-0">
              {targetUser.avatar ? (
                <Image
                  className="w-24 h-24 rounded-full"
                  src={targetUser.avatar}
                  alt={targetUser.name || "用户头像"}
                  width={96}
                  height={96}
                />
              ) : (
                <div className="w-24 h-24 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center">
                  <span className="text-white text-3xl font-bold">
                    {targetUser.name.charAt(0)}
                  </span>
                </div>
              )}
            </div>

            {/* 用户信息 */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-3 mb-2">
                <h1 className="text-3xl font-bold text-gray-900">{targetUser.name}</h1>
                {isAdmin && (
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    targetUser.role === 'admin' ? 'bg-purple-100 text-purple-800' :
                    targetUser.role === 'manager' ? 'bg-blue-100 text-blue-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {targetUser.role === 'admin' ? '超管' : targetUser.role === 'manager' ? '经理' : '成员'}
                  </span>
                )}
              </div>
              
              {targetUser.bio && (
                <p className="text-gray-600 mb-4">{targetUser.bio}</p>
              )}

              {/* 技能标签 */}
              {targetUser.skills && targetUser.skills.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-4">
                  {targetUser.skills.map((skill, index) => (
                    <span key={index} className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                      {skill}
                    </span>
                  ))}
                </div>
              )}

              {/* 社交链接 */}
              {targetUser.socialLinks && Object.keys(targetUser.socialLinks).length > 0 && (
                <div className="flex space-x-4">
                  {Object.entries(targetUser.socialLinks).map(([platform, url]) => (
                    <a
                      key={platform}
                      href={url as string}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-indigo-600 hover:text-indigo-500 text-sm"
                    >
                      {platform === 'github' && '🐙 GitHub'}
                      {platform === 'linkedin' && '💼 LinkedIn'}
                      {platform === 'twitter' && '🐦 Twitter'}
                      {platform === 'website' && '🌐 网站'}
                    </a>
                  ))}
                </div>
              )}
            </div>

            {/* 操作按钮 */}
            <div className="flex space-x-3">
              {isOwnProfile && (
                <Link
                  href="/profile"
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  编辑资料
                </Link>
              )}
              {isAdmin && !isOwnProfile && (
                <Link
                  href={`/admin/users/${targetUser.id}`}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
                >
                  管理用户
                </Link>
              )}
            </div>
          </div>
        </div>

        {/* 标签页导航 */}
        <div className="border-b border-gray-200 mb-6">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`${
                  activeTab === tab.id
                    ? "border-indigo-500 text-indigo-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
              >
                <span>{tab.icon}</span>
                <span>{tab.name}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* 标签页内容 */}
        <div className="space-y-6">
          {activeTab === "overview" && (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* 基本信息 */}
              <div className="lg:col-span-2">
                <div className="bg-white shadow rounded-lg p-6">
                  <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">基本信息</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <dt className="text-sm font-medium text-gray-500">昵称</dt>
                      <dd className="mt-1 text-sm text-gray-900">{targetUser.name}</dd>
                    </div>
                    {(isAdmin || isOwnProfile) && targetUser.realname && (
                      <div>
                        <dt className="text-sm font-medium text-gray-500">姓名</dt>
                        <dd className="mt-1 text-sm text-gray-900">{targetUser.realname}</dd>
                      </div>
                    )}
                    {(isAdmin || isOwnProfile) && (
                      <div>
                        <dt className="text-sm font-medium text-gray-500">邮箱</dt>
                        <dd className="mt-1 text-sm text-gray-900">{targetUser.email}</dd>
                      </div>
                    )}
                    <div>
                      <dt className="text-sm font-medium text-gray-500">加入时间</dt>
                      <dd className="mt-1 text-sm text-gray-900">
                        {new Date(targetUser.createdAt).toLocaleDateString('zh-CN')}
                      </dd>
                    </div>
                  </div>
                </div>
              </div>

              {/* 统计信息 */}
              <div className="space-y-6">
                <div className="bg-white shadow rounded-lg p-6">
                  <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">统计信息</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">参与项目</span>
                      <span className="text-sm font-medium text-gray-900">
                        {projectsData?.total || 0}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === "projects" && (
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg leading-6 font-medium text-gray-900">参与项目</h3>
                <p className="mt-1 text-sm text-gray-500">该用户创建或参与的项目列表</p>
              </div>

              {projectsLoading ? (
                <div className="p-8 text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto mb-4"></div>
                  <div className="text-gray-500">加载项目列表中...</div>
                </div>
              ) : projectsData?.projects && projectsData.projects.length > 0 ? (
                <div className="divide-y divide-gray-200">
                  {projectsData.projects.map((project) => (
                    <div key={project.id} className="p-6 hover:bg-gray-50">
                      <div className="flex items-start space-x-4">
                        {/* 项目Logo */}
                        <div className="flex-shrink-0">
                          {(project as Record<string, unknown>).logo ? (
                            <div className="relative w-12 h-12 rounded-lg overflow-hidden bg-gray-100 border border-gray-200">
                              <Image
                                src={buildThumbnailUrl((project as Record<string, unknown>).logo as string, 48, 48)}
                                alt={`${project.name} Logo`}
                                fill
                                className="object-cover"
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.src = buildImageUrl((project as Record<string, unknown>).logo as string);
                                }}
                              />
                            </div>
                          ) : (
                            <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center">
                              <span className="text-white text-lg font-bold">
                                {project.name.charAt(0)}
                              </span>
                            </div>
                          )}
                        </div>

                        {/* 项目信息 */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-3 mb-2">
                            <h4 className="text-lg font-medium text-gray-900 truncate">
                              {project.name}
                            </h4>
                            <ProjectStatusBadge status={project.status} />
                            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                              {getProjectTypeConfig(project.type).label}
                            </span>
                          </div>

                          <p className="text-gray-600 text-sm line-clamp-2 mb-3">
                            {project.description || "暂无描述"}
                          </p>

                          <div className="flex items-center space-x-6 text-sm text-gray-500">
                            <div className="flex items-center">
                              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 515.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                              </svg>
                              成员: {project.memberCount}
                            </div>
                            <div className="flex items-center">
                              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                              </svg>
                              创建: {new Date(project.createdAt).toLocaleDateString('zh-CN')}
                            </div>
                          </div>
                        </div>

                        {/* 查看按钮 */}
                        <div className="flex-shrink-0">
                          <Link
                            href={`/projects/${project.id}`}
                            className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                          >
                            查看详情
                          </Link>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                  <h3 className="mt-2 text-sm font-medium text-gray-900">暂无项目</h3>
                  <p className="mt-1 text-sm text-gray-500">该用户还没有创建或参与任何项目</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
