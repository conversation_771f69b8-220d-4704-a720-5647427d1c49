import "~/styles/globals.css";

import { type Metadata } from "next";
import { <PERSON>ei<PERSON> } from "next/font/google";

import { Providers } from "./providers";

export const metadata: Metadata = {
  title: "AIPO俱乐部 - 数字游民项目管理平台",
  description: "面向数字游民群体打造的公共成就型平台系统，支持项目管理、团队协作和分成管理",
  icons: {
    icon: [
      { url: "/logo/aipo-logo-32.png", sizes: "32x32", type: "image/png" },
      { url: "/logo/aipo-logo-64.png", sizes: "64x64", type: "image/png" },
    ],
    apple: [
      { url: "/logo/aipo-logo-192.png", sizes: "192x192", type: "image/png" },
    ],
    other: [
      { rel: "icon", url: "/favicon.ico" },
      { rel: "apple-touch-icon", url: "/logo/aipo-logo-192.png" },
      { rel: "manifest", url: "/manifest.json" },
    ],
  },
};

const geist = Geist({
  subsets: ["latin"],
  variable: "--font-geist-sans",
});

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang="en" className={`${geist.variable}`}>
      <body>
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}
