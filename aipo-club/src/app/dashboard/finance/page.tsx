"use client";

import { useState } from "react";
import Image from "next/image";
import { api } from "~/trpc/react";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Badge } from "~/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "~/components/ui/dialog";
import { toast } from "~/hooks/use-toast";
import { formatCurrency, formatDate } from "~/lib/utils";
import { CreditCard, Plus, ExternalLink, Wallet, TrendingUp, Clock } from "lucide-react";

const PAYMENT_PROVIDERS = {
  stripe: { name: "Stripe", icon: "💳" },
  paypal: { name: "PayPal", icon: "🅿️" },
  alipay: { name: "支付宝", icon: "🔵" },
  wechat: { name: "微信支付", icon: "💚" },
  bank: { name: "银行转账", icon: "🏦" },
} as const;

type PaymentProvider = keyof typeof PAYMENT_PROVIDERS;

export default function FinancePage() {
  const [showAddAccount, setShowAddAccount] = useState(false);
  const [newAccount, setNewAccount] = useState({
    provider: "stripe" as PaymentProvider,
    accountEmail: "",
    accountName: "",
    isDefault: false,
  });

  const { data: paymentAccounts, refetch: refetchAccounts } = api.payment.getPaymentAccounts.useQuery();
  const { data: pendingPayouts } = api.payment.getPendingPayouts.useQuery();
  const { data: payoutHistory } = api.payment.getPayoutHistory.useQuery({ limit: 20 });

  const addAccountMutation = api.payment.addPaymentAccount.useMutation({
    onSuccess: () => {
      toast({
        title: "支付账户已添加",
        description: "新的支付账户已成功添加到您的账户中",
      });
      setShowAddAccount(false);
      setNewAccount({
        provider: "stripe" as PaymentProvider,
        accountEmail: "",
        accountName: "",
        isDefault: false,
      });
      void refetchAccounts();
    },
    onError: (error) => {
      toast({
        title: "添加失败",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const createAccountLinkMutation = api.payment.createStripeAccountLink.useMutation({
    onSuccess: (result) => {
      window.open(result.url, "_blank");
    },
    onError: (error) => {
      toast({
        title: "创建链接失败",
        description: error.message,
        variant: "destructive",
      });
    },
  });



  const handleAddAccount = (e: React.FormEvent) => {
    e.preventDefault();
    addAccountMutation.mutate(newAccount);
  };

  const handleSetupStripe = (accountId: string) => {
    createAccountLinkMutation.mutate({
      paymentAccountId: accountId,
      refreshUrl: window.location.href,
      returnUrl: window.location.href,
    });
  };

  // const handleProcessPayout = (payoutId: string, paymentAccountId: string) => {
  //   processPayoutMutation.mutate({
  //     payoutId,
  //     paymentAccountId,
  //   });
  // };

  // 计算统计数据
  const totalPending = pendingPayouts?.reduce((sum, payout) => sum + payout.amount, 0) ?? 0;
  const totalPaid = payoutHistory?.reduce((sum, payout) => 
    payout.status === "completed" ? sum + payout.amount : sum, 0) ?? 0;

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold flex items-center gap-2">
          <Wallet className="h-8 w-8" />
          财务管理
        </h1>
        <p className="text-muted-foreground">管理您的支付账户和收益分配</p>
      </div>

      <div className="grid gap-6 md:grid-cols-3 mb-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              待支付
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalPending)}</div>
            <p className="text-sm text-muted-foreground">
              {pendingPayouts?.length ?? 0} 个待处理
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              已收益
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalPaid)}</div>
            <p className="text-sm text-muted-foreground">
              累计收益金额
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-4 w-4" />
              支付账户
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{paymentAccounts?.length ?? 0}</div>
            <p className="text-sm text-muted-foreground">
              已配置账户
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="accounts" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="accounts">支付账户</TabsTrigger>
          <TabsTrigger value="pending">待支付</TabsTrigger>
          <TabsTrigger value="history">支付历史</TabsTrigger>
        </TabsList>

        <TabsContent value="accounts" className="mt-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>支付账户管理</CardTitle>
                <CardDescription>添加和管理您的收款账户</CardDescription>
              </div>
              <Dialog open={showAddAccount} onOpenChange={setShowAddAccount}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    添加账户
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>添加支付账户</DialogTitle>
                    <DialogDescription>
                      添加新的收款账户以接收项目收益分配
                    </DialogDescription>
                  </DialogHeader>
                  <form onSubmit={handleAddAccount} className="space-y-4">
                    <div>
                      <Label htmlFor="provider">支付服务商</Label>
                      <Select
                        value={newAccount.provider}
                        onValueChange={(value) =>
                          setNewAccount({ ...newAccount, provider: value as PaymentProvider })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {Object.entries(PAYMENT_PROVIDERS).map(([key, provider]) => (
                            <SelectItem key={key} value={key}>
                              {provider.icon} {provider.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="accountEmail">账户邮箱</Label>
                      <Input
                        id="accountEmail"
                        type="email"
                        placeholder="<EMAIL>"
                        value={newAccount.accountEmail}
                        onChange={(e) =>
                          setNewAccount({ ...newAccount, accountEmail: e.target.value })
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor="accountName">账户名称</Label>
                      <Input
                        id="accountName"
                        placeholder="账户显示名称"
                        value={newAccount.accountName}
                        onChange={(e) =>
                          setNewAccount({ ...newAccount, accountName: e.target.value })
                        }
                      />
                    </div>
                    <Button
                      type="submit"
                      disabled={addAccountMutation.isPending}
                      className="w-full"
                    >
                      {addAccountMutation.isPending ? "添加中..." : "添加账户"}
                    </Button>
                  </form>
                </DialogContent>
              </Dialog>
            </CardHeader>
            <CardContent>
              {paymentAccounts && paymentAccounts.length > 0 ? (
                <div className="space-y-4">
                  {paymentAccounts.map((account) => (
                    <div
                      key={account.id}
                      className="flex items-center justify-between p-4 border rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        <div className="text-2xl">
                          {PAYMENT_PROVIDERS[account.provider as keyof typeof PAYMENT_PROVIDERS]?.icon}
                        </div>
                        <div>
                          <div className="font-medium">
                            {account.accountName || 
                             PAYMENT_PROVIDERS[account.provider as keyof typeof PAYMENT_PROVIDERS]?.name}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {account.accountEmail}
                          </div>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge variant={account.isVerified ? "default" : "secondary"}>
                              {account.isVerified ? "已验证" : "未验证"}
                            </Badge>
                            {account.isDefault && (
                              <Badge variant="outline">默认</Badge>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {account.provider === "stripe" && !account.isVerified && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleSetupStripe(account.id)}
                            disabled={createAccountLinkMutation.isPending}
                          >
                            <ExternalLink className="h-4 w-4 mr-2" />
                            设置
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <CreditCard className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>暂无支付账户</p>
                  <p className="text-sm">添加支付账户以接收项目收益</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="pending" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>待支付收益</CardTitle>
              <CardDescription>等待处理的收益分配</CardDescription>
            </CardHeader>
            <CardContent>
              {pendingPayouts && pendingPayouts.length > 0 ? (
                <div className="space-y-4">
                  {pendingPayouts.map((payout) => (
                    <div
                      key={payout.id}
                      className="flex items-center justify-between p-4 border rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        {payout.projectShare.project.logo && (
                          <Image
                            src={payout.projectShare.project.logo}
                            alt={payout.projectShare.project.name}
                            width={40}
                            height={40}
                            className="h-10 w-10 rounded object-cover"
                          />
                        )}
                        <div>
                          <div className="font-medium">
                            {payout.projectShare.project.name}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            分成类型：{payout.projectShare.shareType} | 
                            比例：{payout.projectShare.percentage}%
                          </div>
                          <div className="text-sm text-muted-foreground">
                            周期：{formatDate(payout.periodStart)} - {formatDate(payout.periodEnd)}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold">
                          {formatCurrency(payout.amount)} {payout.currency}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {formatDate(payout.createdAt)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>暂无待支付收益</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>支付历史</CardTitle>
              <CardDescription>已处理的收益支付记录</CardDescription>
            </CardHeader>
            <CardContent>
              {payoutHistory && payoutHistory.length > 0 ? (
                <div className="space-y-4">
                  {payoutHistory.map((payout) => (
                    <div
                      key={payout.id}
                      className="flex items-center justify-between p-4 border rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        {payout.sharePayout.projectShare.project.logo && (
                          <Image
                            src={payout.sharePayout.projectShare.project.logo}
                            alt={payout.sharePayout.projectShare.project.name}
                            width={40}
                            height={40}
                            className="h-10 w-10 rounded object-cover"
                          />
                        )}
                        <div>
                          <div className="font-medium">
                            {payout.sharePayout.projectShare.project.name}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            支付方式：{PAYMENT_PROVIDERS[payout.paymentAccount.provider as keyof typeof PAYMENT_PROVIDERS]?.name}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {payout.paymentAccount.accountEmail}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold">
                          {formatCurrency(payout.amount)} {payout.currency}
                        </div>
                        <Badge
                          variant={
                            payout.status === "completed"
                              ? "default"
                              : payout.status === "failed"
                              ? "destructive"
                              : "secondary"
                          }
                        >
                          {payout.status === "completed"
                            ? "已完成"
                            : payout.status === "failed"
                            ? "失败"
                            : payout.status === "processing"
                            ? "处理中"
                            : "待处理"}
                        </Badge>
                        <div className="text-sm text-muted-foreground">
                          {payout.processedAt ? formatDate(payout.processedAt) : formatDate(payout.createdAt)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>暂无支付历史</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}