"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { redirect } from "next/navigation";
import { api } from "~/trpc/react";
import { AdminLayout } from "~/components/admin-layout";
import { ProjectStatusBadge, ProjectStatusFilter } from "~/components/project/ProjectStatusComponents";
import { getProjectTypeConfig } from "~/types/project";
import { buildThumbnailUrl, buildImageUrl } from "~/lib/oss";
import Image from "next/image";

export default function ProjectsPage() {
  const { data: session, status } = useSession();
  const [currentPage, setCurrentPage] = useState(1);
  const [filters, setFilters] = useState({
    status: "",
    search: "",
    myProjects: false,
  });

  // 始终调用所有Hooks，避免条件性调用
  const { data: projectsData, isLoading } = api.project.getProjects.useQuery({
    page: currentPage,
    limit: 10,
    status: (filters.status as "ideation" | "planning" | "developing" | "testing" | "launching" | "promoting" | "profiting" | "maintaining" | "declining" | "completed" | "cancelled" | undefined) || undefined,
    search: filters.search || undefined,
    myProjects: filters.myProjects,
  }, {
    enabled: !!session, // 只有在有session时才启用查询
    staleTime: 30 * 1000, // 30秒内数据被认为是新鲜的
    gcTime: 5 * 60 * 1000, // 5分钟后从缓存中清除
  });

  // 在所有Hooks调用之后进行条件性渲染
  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    );
  }

  if (!session) {
    redirect("/login");
  }

  const handleFilterChange = (key: string, value: string | boolean) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1); // 重置到第一页
  };







  return (
    <AdminLayout>
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">项目管理</h1>
          <p className="mt-1 text-sm text-gray-600">浏览和管理所有项目</p>
        </div>
        <Link
          href="/projects/create"
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
          创建项目
        </Link>
      </div>

      {/* 筛选器 */}
      <div className="bg-white shadow rounded-lg p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                搜索
              </label>
              <input
                type="text"
                value={filters.search}
                onChange={(e) => handleFilterChange("search", e.target.value)}
                placeholder="搜索项目名称或描述"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                状态
              </label>
              <ProjectStatusFilter
                value={filters.status}
                onChange={(value) => handleFilterChange("status", value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>

            <div className="flex items-end">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.myProjects}
                  onChange={(e) => handleFilterChange("myProjects", e.target.checked)}
                  className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                />
                <span className="ml-2 text-sm text-gray-700">只显示我的项目</span>
              </label>
            </div>
          </div>
        </div>

      {/* 项目列表 */}
      <div className="bg-white shadow rounded-lg">
          {isLoading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto mb-4"></div>
              <div className="text-gray-500">加载项目列表中...</div>
            </div>
          ) : projectsData?.projects && projectsData.projects.length > 0 ? (
            <>
              <div className="divide-y divide-gray-200">
                {projectsData.projects.map((project) => (
                  <div key={project.id} className="p-6 hover:bg-gray-50">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-4 flex-1">
                        {/* 项目Logo */}
                        <div className="flex-shrink-0">
                          {(project as Record<string, unknown>).logo ? (
                            <div className="relative w-16 h-16 rounded-lg overflow-hidden bg-gray-100 border border-gray-200">
                              <Image
                                src={buildThumbnailUrl((project as Record<string, unknown>).logo as string, 64, 64)}
                                alt={`${project.name} Logo`}
                                fill
                                className="object-cover"
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.src = buildImageUrl((project as Record<string, unknown>).logo as string);
                                }}
                              />
                            </div>
                          ) : (
                            <div className="w-16 h-16 rounded-lg bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center">
                              <span className="text-white text-xl font-bold">
                                {project.name.charAt(0)}
                              </span>
                            </div>
                          )}
                        </div>

                        {/* 项目信息 */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-3 mb-2">
                            <h3 className="text-lg font-medium text-gray-900 truncate">
                              {project.name}
                            </h3>
                            <span className="text-sm text-gray-500 font-mono">({project.code})</span>
                            <ProjectStatusBadge status={project.status} />
                            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                              {getProjectTypeConfig(project.type).label}
                            </span>
                          </div>
                          <p className="text-gray-600 line-clamp-2">
                            {project.description || "暂无描述"}
                          </p>
                          <div className="mt-3 flex items-center space-x-6 text-sm text-gray-500">
                          <div className="flex items-center">
                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            创建者: {(project as any).createdBy || "未知"}
                          </div>
                          <div className="flex items-center">
                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 515.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            成员: {project.memberCount}
                          </div>
                          {project.totalRevenue > 0 && (
                            <div className="flex items-center">
                              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                              </svg>
                              收益: ¥{project.totalRevenue.toLocaleString()}
                            </div>
                          )}
                          <div className="flex items-center">
                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            创建时间: {new Date(project.createdAt).toLocaleDateString('zh-CN')}
                          </div>
                          </div>
                        </div>
                      </div>

                      {/* 操作按钮 */}
                      <div className="flex-shrink-0 ml-6">
                        <Link
                          href={`/projects/${project.id}`}
                          className="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700 transition-colors"
                        >
                          查看详情
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* 分页 */}
              {projectsData.pages > 1 && (
                <div className="px-6 py-4 border-t border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-700">
                      显示第 {(currentPage - 1) * 10 + 1} - {Math.min(currentPage * 10, projectsData.total)} 条，
                      共 {projectsData.total} 条
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                        disabled={currentPage === 1}
                        className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                      >
                        上一页
                      </button>
                      <span className="px-3 py-1 text-sm">
                        第 {currentPage} / {projectsData.pages} 页
                      </span>
                      <button
                        onClick={() => setCurrentPage(prev => Math.min(projectsData.pages, prev + 1))}
                        disabled={currentPage === projectsData.pages}
                        className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                      >
                        下一页
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="p-8 text-center">
              <p className="text-gray-500 mb-4">
                {filters.myProjects ? "您还没有创建任何项目" : "暂无项目"}
              </p>
              <Link
                href="/projects/create"
                className="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700"
              >
                创建项目
              </Link>
            </div>
          )}
        </div>
    </AdminLayout>
  );
}
