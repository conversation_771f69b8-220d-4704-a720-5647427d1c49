"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { redirect } from "next/navigation";
import { api } from "~/trpc/react";
import { AdminLayout } from "~/components/admin-layout";
import { ProjectTypeSelect } from "~/components/project/ProjectTypeSelect";
import { ProjectLogoUpload } from "~/components/ui/ImageUpload";

export default function CreateProjectPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [formData, setFormData] = useState({
    name: "",
    code: "",
    description: "",
    type: "other" as const,
    category: "",
    priority: "medium" as const,
    parentId: "",
    poUserId: "",
    startDate: "",
    endDate: "",
    deadline: "",
    budget: "",
    // 新增项目管理字段
    logo: "",
    visitUrl: "",
    wikiUrl: "",
    gitUrl: "",
    healthStatus: "healthy" as const,
    version: "1.0.0",
    techStack: "",
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  // 如果未登录，重定向到登录页
  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    );
  }

  if (!session) {
    redirect("/login");
  }

  // 获取项目列表用于选择父项目
  const { data: projects } = api.project.getProjects.useQuery({
    page: 1,
    limit: 100,
  });

  // 获取用户列表用于选择项目PO
  const { data: users } = api.user.getUsers.useQuery({
    page: 1,
    limit: 100,
  });

  const createProjectMutation = api.project.create.useMutation({
    onSuccess: (project) => {
      router.push(`/projects/${project.id}`);
    },
    onError: (error) => {
      console.error("项目创建失败:", error);
      setErrors({
        submit: error.message || "创建项目失败，请稍后重试"
      });
    },
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    // 清除对应字段的错误
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: "" }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "项目名称不能为空";
    } else if (formData.name.length > 100) {
      newErrors.name = "项目名称不能超过100个字符";
    }

    if (!formData.code.trim()) {
      newErrors.code = "项目编码不能为空";
    } else if (formData.code.length > 50) {
      newErrors.code = "项目编码不能超过50个字符";
    } else if (!/^[A-Z0-9-]+$/.test(formData.code)) {
      newErrors.code = "项目编码只能包含大写字母、数字和连字符";
    }

    if (formData.description && formData.description.length > 1000) {
      newErrors.description = "项目描述不能超过1000个字符";
    }

    if (formData.category && formData.category.length > 50) {
      newErrors.category = "项目分类不能超过50个字符";
    }

    if (formData.budget && (isNaN(Number(formData.budget)) || Number(formData.budget) < 0)) {
      newErrors.budget = "预算必须是非负数";
    }

    if (formData.startDate && formData.endDate) {
      const start = new Date(formData.startDate);
      const end = new Date(formData.endDate);
      if (start >= end) {
        newErrors.endDate = "结束时间必须晚于开始时间";
      }
    }

    if (formData.deadline) {
      const deadline = new Date(formData.deadline);
      const now = new Date();
      if (deadline <= now) {
        newErrors.deadline = "截止时间必须是未来时间";
      }
    }

    // 新增字段验证
    const urlRegex = /^https?:\/\/.+/;
    if (formData.logo && !urlRegex.test(formData.logo)) {
      newErrors.logo = "请输入有效的URL地址";
    }
    if (formData.visitUrl && !urlRegex.test(formData.visitUrl)) {
      newErrors.visitUrl = "请输入有效的URL地址";
    }
    if (formData.wikiUrl && !urlRegex.test(formData.wikiUrl)) {
      newErrors.wikiUrl = "请输入有效的URL地址";
    }
    if (formData.gitUrl && !urlRegex.test(formData.gitUrl)) {
      newErrors.gitUrl = "请输入有效的URL地址";
    }
    if (formData.version && formData.version.length > 50) {
      newErrors.version = "版本号不能超过50个字符";
    }
    if (formData.techStack && formData.techStack.length > 500) {
      newErrors.techStack = "技术栈描述不能超过500个字符";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      const projectData: {
        name: string;
        code: string;
        description?: string;
        type: string;
        category?: string;
        priority: string;
        parentId?: string;
        poUserId?: string;
        startDate?: Date;
        endDate?: Date;
        deadline?: Date;
        budget?: number;
        logo?: string;
        visitUrl?: string;
        wikiUrl?: string;
        gitUrl?: string;
        healthStatus: "healthy" | "warning" | "critical";
        version: string;
        techStack?: string;
      } = {
        name: formData.name.trim(),
        code: formData.code.trim(),
        description: formData.description.trim() || undefined,
        type: formData.type,
        category: formData.category.trim() || undefined,
        priority: formData.priority,
        parentId: formData.parentId || undefined,
        poUserId: formData.poUserId || undefined,
        startDate: formData.startDate ? new Date(formData.startDate) : undefined,
        endDate: formData.endDate ? new Date(formData.endDate) : undefined,
        deadline: formData.deadline ? new Date(formData.deadline) : undefined,
        budget: formData.budget ? Number(formData.budget) : undefined,
        // 新增项目管理字段
        logo: formData.logo.trim() || undefined,
        visitUrl: formData.visitUrl.trim() || undefined,
        wikiUrl: formData.wikiUrl.trim() || undefined,
        gitUrl: formData.gitUrl.trim() || undefined,
        healthStatus: formData.healthStatus as "healthy" | "warning" | "critical",
        version: formData.version.trim(),
        techStack: formData.techStack.trim() || undefined,
      };

      await createProjectMutation.mutateAsync(projectData);
    } catch {
      // 错误已在 onError 中处理
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AdminLayout>
      {/* 页面标题 */}
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-gray-900">创建新项目</h1>
        <p className="mt-1 text-sm text-gray-600">填写项目基本信息，开始您的创业之旅</p>
      </div>

      <div className="max-w-3xl">
        <div className="bg-white shadow rounded-lg p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {errors.submit && (
              <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded">
                {errors.submit}
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  项目名称 *
                </label>
                <div className="mt-1">
                  <input
                    id="name"
                    name="name"
                    type="text"
                    required
                    value={formData.name}
                    onChange={handleChange}
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="请输入项目名称"
                  />
                  {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
                </div>
              </div>

              <div>
                <label htmlFor="code" className="block text-sm font-medium text-gray-700">
                  项目编码 *
                </label>
                <div className="mt-1">
                  <input
                    id="code"
                    name="code"
                    type="text"
                    required
                    value={formData.code}
                    onChange={handleChange}
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm font-mono"
                    placeholder="如：PROJ-2025-001"
                  />
                  {errors.code && <p className="mt-1 text-sm text-red-600">{errors.code}</p>}
                  <p className="mt-1 text-xs text-gray-500">只能包含大写字母、数字和连字符</p>
                </div>
              </div>
            </div>

            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                项目描述
              </label>
              <div className="mt-1">
                <textarea
                  id="description"
                  name="description"
                  rows={4}
                  value={formData.description}
                  onChange={handleChange}
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="请描述您的项目..."
                />
                {errors.description && <p className="mt-1 text-sm text-red-600">{errors.description}</p>}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="type" className="block text-sm font-medium text-gray-700">
                  项目类型
                </label>
                <div className="mt-1">
                  <ProjectTypeSelect
                    id="type"
                    name="type"
                    value={formData.type}
                    onChange={(value) => setFormData(prev => ({ ...prev, type: value as any }))}
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="选择项目类型"
                  />
                </div>
              </div>


            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="parentId" className="block text-sm font-medium text-gray-700">
                  父项目
                </label>
                <div className="mt-1">
                  <select
                    id="parentId"
                    name="parentId"
                    value={formData.parentId}
                    onChange={handleChange}
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  >
                    <option value="">无（顶级项目）</option>
                    {projects?.projects.map((project) => (
                      <option key={project.id} value={project.id}>
                        {project.name} ({project.code})
                      </option>
                    ))}
                  </select>
                  <p className="mt-1 text-xs text-gray-500">选择父项目可创建子项目</p>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-700">
                  项目分类
                </label>
                <div className="mt-1">
                  <input
                    id="category"
                    name="category"
                    type="text"
                    value={formData.category}
                    onChange={handleChange}
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="如：Web应用、移动应用、AI工具等"
                  />
                  {errors.category && <p className="mt-1 text-sm text-red-600">{errors.category}</p>}
                </div>
              </div>

              <div>
                <label htmlFor="priority" className="block text-sm font-medium text-gray-700">
                  优先级
                </label>
                <div className="mt-1">
                  <select
                    id="priority"
                    name="priority"
                    value={formData.priority}
                    onChange={handleChange}
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  >
                    <option value="low">低</option>
                    <option value="medium">中</option>
                    <option value="high">高</option>
                    <option value="urgent">紧急</option>
                  </select>
                </div>
              </div>

              <div>
                <label htmlFor="poUserId" className="block text-sm font-medium text-gray-700">
                  项目PO
                </label>
                <div className="mt-1">
                  <select
                    id="poUserId"
                    name="poUserId"
                    value={formData.poUserId}
                    onChange={handleChange}
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  >
                    <option value="">默认（创建者）</option>
                    {users?.users.map((user) => (
                      <option key={user.id} value={user.id}>
                        {user.name} ({user.email})
                      </option>
                    ))}
                  </select>
                  <p className="mt-1 text-xs text-gray-500">留空则默认为项目创建者</p>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label htmlFor="startDate" className="block text-sm font-medium text-gray-700">
                  开始时间
                </label>
                <div className="mt-1">
                  <input
                    id="startDate"
                    name="startDate"
                    type="date"
                    value={formData.startDate}
                    onChange={handleChange}
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="endDate" className="block text-sm font-medium text-gray-700">
                  结束时间
                </label>
                <div className="mt-1">
                  <input
                    id="endDate"
                    name="endDate"
                    type="date"
                    value={formData.endDate}
                    onChange={handleChange}
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  />
                  {errors.endDate && <p className="mt-1 text-sm text-red-600">{errors.endDate}</p>}
                </div>
              </div>

              <div>
                <label htmlFor="deadline" className="block text-sm font-medium text-gray-700">
                  截止时间
                </label>
                <div className="mt-1">
                  <input
                    id="deadline"
                    name="deadline"
                    type="date"
                    value={formData.deadline}
                    onChange={handleChange}
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  />
                  {errors.deadline && <p className="mt-1 text-sm text-red-600">{errors.deadline}</p>}
                </div>
              </div>
            </div>

            <div>
              <label htmlFor="budget" className="block text-sm font-medium text-gray-700">
                预算（元）
              </label>
              <div className="mt-1">
                <input
                  id="budget"
                  name="budget"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.budget}
                  onChange={handleChange}
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="请输入项目预算"
                />
                {errors.budget && <p className="mt-1 text-sm text-red-600">{errors.budget}</p>}
              </div>
            </div>

            {/* 项目管理字段 */}
            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">项目管理信息</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    项目Logo
                  </label>
                  <ProjectLogoUpload
                    value={formData.logo}
                    onChange={(url) => {
                      setFormData(prev => ({ ...prev, logo: url }));
                      // 清除logo字段的错误
                      if (errors.logo) {
                        setErrors(prev => ({ ...prev, logo: "" }));
                      }
                    }}
                    onError={(error) => {
                      setErrors(prev => ({ ...prev, logo: error }));
                    }}
                    disabled={isLoading}
                  />
                  {errors.logo && <p className="mt-1 text-sm text-red-600">{errors.logo}</p>}
                </div>

                <div>
                  <label htmlFor="visitUrl" className="block text-sm font-medium text-gray-700">
                    访问地址
                  </label>
                  <div className="mt-1">
                    <input
                      id="visitUrl"
                      name="visitUrl"
                      type="url"
                      value={formData.visitUrl}
                      onChange={handleChange}
                      placeholder="https://example.com"
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                    {errors.visitUrl && <p className="mt-1 text-sm text-red-600">{errors.visitUrl}</p>}
                    <p className="mt-1 text-xs text-gray-500">项目生产环境访问地址</p>
                  </div>
                </div>

                <div>
                  <label htmlFor="wikiUrl" className="block text-sm font-medium text-gray-700">
                    知识库地址
                  </label>
                  <div className="mt-1">
                    <input
                      id="wikiUrl"
                      name="wikiUrl"
                      type="url"
                      value={formData.wikiUrl}
                      onChange={handleChange}
                      placeholder="https://wiki.example.com"
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                    {errors.wikiUrl && <p className="mt-1 text-sm text-red-600">{errors.wikiUrl}</p>}
                    <p className="mt-1 text-xs text-gray-500">项目文档和知识库地址</p>
                  </div>
                </div>

                <div>
                  <label htmlFor="gitUrl" className="block text-sm font-medium text-gray-700">
                    代码仓库
                  </label>
                  <div className="mt-1">
                    <input
                      id="gitUrl"
                      name="gitUrl"
                      type="url"
                      value={formData.gitUrl}
                      onChange={handleChange}
                      placeholder="https://github.com/user/repo"
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                    {errors.gitUrl && <p className="mt-1 text-sm text-red-600">{errors.gitUrl}</p>}
                    <p className="mt-1 text-xs text-gray-500">GitHub/GitLab等代码仓库地址</p>
                  </div>
                </div>

                <div>
                  <label htmlFor="healthStatus" className="block text-sm font-medium text-gray-700">
                    健康状态
                  </label>
                  <div className="mt-1">
                    <select
                      id="healthStatus"
                      name="healthStatus"
                      value={formData.healthStatus}
                      onChange={handleChange}
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    >
                      <option value="healthy">✅ 健康</option>
                      <option value="warning">⚠️ 警告</option>
                      <option value="critical">🚨 严重</option>
                    </select>
                    <p className="mt-1 text-xs text-gray-500">项目当前运行状态</p>
                  </div>
                </div>

                <div>
                  <label htmlFor="version" className="block text-sm font-medium text-gray-700">
                    版本号
                  </label>
                  <div className="mt-1">
                    <input
                      id="version"
                      name="version"
                      type="text"
                      value={formData.version}
                      onChange={handleChange}
                      placeholder="1.0.0"
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                    {errors.version && <p className="mt-1 text-sm text-red-600">{errors.version}</p>}
                    <p className="mt-1 text-xs text-gray-500">项目初始版本号</p>
                  </div>
                </div>

                <div className="md:col-span-2">
                  <label htmlFor="techStack" className="block text-sm font-medium text-gray-700">
                    技术栈
                  </label>
                  <div className="mt-1">
                    <textarea
                      id="techStack"
                      name="techStack"
                      rows={3}
                      value={formData.techStack}
                      onChange={handleChange}
                      placeholder="React, Node.js, MySQL, Docker..."
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                    {errors.techStack && <p className="mt-1 text-sm text-red-600">{errors.techStack}</p>}
                    <p className="mt-1 text-xs text-gray-500">项目使用的技术栈描述</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-end space-x-4 pt-6">
              <Link
                href="/projects"
                className="bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-300"
              >
                取消
              </Link>
              <button
                type="submit"
                disabled={isLoading}
                className="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? "创建中..." : "创建项目"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </AdminLayout>
  );
}
