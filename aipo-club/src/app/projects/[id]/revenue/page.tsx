"use client";

import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { api } from "~/trpc/react";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Textarea } from "~/components/ui/textarea";
import { Label } from "~/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { toast } from "~/hooks/use-toast";
import { formatCurrency, formatDate } from "~/lib/utils";

export default function ProjectRevenuePage() {
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;

  const [revenueForm, setRevenueForm] = useState({
    amount: "",
    currency: "CNY",
    source: "manual",
    description: "",
    period: "monthly",
  });

  const { data: project } = api.project.getProject.useQuery({ id: projectId });
  const { data: revenues, refetch: refetchRevenues } = api.payment.getProjectRevenues.useQuery({
    projectId,
    limit: 20,
  });

  const recordRevenueMutation = api.payment.recordProjectRevenue.useMutation({
    onSuccess: () => {
      toast({
        title: "收入已记录",
        description: "项目收入已成功记录到系统中",
      });
      setRevenueForm({
        amount: "",
        currency: "CNY",
        source: "manual",
        description: "",
        period: "monthly",
      });
      void refetchRevenues();
    },
    onError: (error) => {
      toast({
        title: "记录失败",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const triggerDistributionMutation = api.payment.triggerProfitDistribution.useMutation({
    onSuccess: (result) => {
      toast({
        title: "利润分配成功",
        description: `总收入 ${formatCurrency(result.totalRevenue)}，创建了 ${result.payoutsCreated} 个分成支付`,
      });
    },
    onError: (error) => {
      toast({
        title: "分配失败",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const smartDistributionMutation = api.payment.smartProfitDistribution.useMutation({
    onSuccess: (result) => {
      toast({
        title: "智能分成完成",
        description: `净利润 ${formatCurrency(result.summary.netProfit)}，分配给 ${result.payoutsCreated} 个成员`,
      });
    },
    onError: (error) => {
      toast({
        title: "智能分成失败",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleRecordRevenue = (e: React.FormEvent) => {
    e.preventDefault();
    if (!revenueForm.amount) return;

    recordRevenueMutation.mutate({
      projectId,
      amount: parseFloat(revenueForm.amount),
      currency: revenueForm.currency,
      source: revenueForm.source,
      description: revenueForm.description,
      period: revenueForm.period,
    });
  };

  const handleTriggerDistribution = () => {
    const now = new Date();
    const periodStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const periodEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    triggerDistributionMutation.mutate({
      projectId,
      periodStart,
      periodEnd,
    });
  };

  const handleSmartDistribution = () => {
    const now = new Date();
    const periodStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const periodEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    smartDistributionMutation.mutate({
      projectId,
      periodStart,
      periodEnd,
    });
  };

  if (!project) {
    return <div>加载中...</div>;
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="mb-4"
        >
          ← 返回项目详情
        </Button>
        <h1 className="text-3xl font-bold">收入管理</h1>
        <p className="text-muted-foreground">项目：{project.name}</p>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">收入概览</TabsTrigger>
          <TabsTrigger value="record">记录收入</TabsTrigger>
          <TabsTrigger value="distribution">利润分配</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6">
          <div className="grid gap-6 md:grid-cols-3">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle>总收入</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(project.totalRevenue ?? 0)}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-3">
                <CardTitle>本月收入</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(project.currentMonthRevenue ?? 0)}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-3">
                <CardTitle>收入记录</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {revenues?.length ?? 0}
                </div>
              </CardContent>
            </Card>
          </div>

          <Card className="mt-6">
            <CardHeader>
              <CardTitle>收入历史</CardTitle>
              <CardDescription>最近的收入记录</CardDescription>
            </CardHeader>
            <CardContent>
              {revenues && revenues.length > 0 ? (
                <div className="space-y-4">
                  {revenues.map((revenue) => (
                    <div
                      key={revenue.id}
                      className="flex items-center justify-between border-b pb-2"
                    >
                      <div>
                        <div className="font-medium">
                          {formatCurrency(revenue.amount)} {revenue.currency}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {revenue.description || "无描述"}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          来源：{revenue.source} | 记录人：{revenue.user.name}
                        </div>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {formatDate(revenue.recordedAt)}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground">暂无收入记录</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="record" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>记录项目收入</CardTitle>
              <CardDescription>
                手动记录项目收入，用于计算利润分配
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleRecordRevenue} className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <Label htmlFor="amount">收入金额</Label>
                    <Input
                      id="amount"
                      type="number"
                      step="0.01"
                      placeholder="0.00"
                      value={revenueForm.amount}
                      onChange={(e) =>
                        setRevenueForm({ ...revenueForm, amount: e.target.value })
                      }
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="currency">货币</Label>
                    <Select
                      value={revenueForm.currency}
                      onValueChange={(value) =>
                        setRevenueForm({ ...revenueForm, currency: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="CNY">人民币 (CNY)</SelectItem>
                        <SelectItem value="USD">美元 (USD)</SelectItem>
                        <SelectItem value="EUR">欧元 (EUR)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <Label htmlFor="source">收入来源</Label>
                    <Select
                      value={revenueForm.source}
                      onValueChange={(value) =>
                        setRevenueForm({ ...revenueForm, source: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="manual">手动录入</SelectItem>
                        <SelectItem value="stripe">Stripe</SelectItem>
                        <SelectItem value="paypal">PayPal</SelectItem>
                        <SelectItem value="alipay">支付宝</SelectItem>
                        <SelectItem value="bank">银行转账</SelectItem>
                        <SelectItem value="other">其他</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="period">收入周期</Label>
                    <Select
                      value={revenueForm.period}
                      onValueChange={(value) =>
                        setRevenueForm({ ...revenueForm, period: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="daily">日收入</SelectItem>
                        <SelectItem value="weekly">周收入</SelectItem>
                        <SelectItem value="monthly">月收入</SelectItem>
                        <SelectItem value="quarterly">季度收入</SelectItem>
                        <SelectItem value="yearly">年收入</SelectItem>
                        <SelectItem value="one-time">一次性收入</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="description">描述</Label>
                  <Textarea
                    id="description"
                    placeholder="收入来源详细描述（可选）"
                    value={revenueForm.description}
                    onChange={(e) =>
                      setRevenueForm({ ...revenueForm, description: e.target.value })
                    }
                  />
                </div>

                <Button
                  type="submit"
                  disabled={recordRevenueMutation.isPending}
                  className="w-full"
                >
                  {recordRevenueMutation.isPending ? "记录中..." : "记录收入"}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="distribution" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>利润分配</CardTitle>
              <CardDescription>
                根据分成规则自动计算并创建利润分配记录
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="mb-4 p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium mb-2">分配规则说明</h4>
                <p className="text-sm text-muted-foreground mb-3">
                  智能分成会基于真实的财务数据（收入和支出）计算净利润后进行分配，
                  确保分成基于实际盈利情况。传统分成仅基于收入数据。
                </p>
                <div className="grid gap-2 text-sm">
                  <div className="flex justify-between">
                    <span>智能分成：</span>
                    <span className="text-green-600">基于净利润分配</span>
                  </div>
                  <div className="flex justify-between">
                    <span>传统分成：</span>
                    <span className="text-orange-600">基于收入分配</span>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <Button
                  onClick={handleSmartDistribution}
                  disabled={smartDistributionMutation.isPending}
                  className="w-full"
                  variant="default"
                >
                  {smartDistributionMutation.isPending
                    ? "智能分成执行中..."
                    : "🧠 智能利润分成（推荐）"}
                </Button>
                
                <Button
                  onClick={handleTriggerDistribution}
                  disabled={triggerDistributionMutation.isPending}
                  className="w-full"
                  variant="outline"
                >
                  {triggerDistributionMutation.isPending
                    ? "传统分成执行中..."
                    : "📊 传统收入分成"}
                </Button>
              </div>

              <div className="mt-4 p-3 bg-yellow-50 rounded-lg">
                <p className="text-xs text-yellow-800">
                  💡 建议：使用智能分成确保只有在项目盈利时才进行分配，
                  平台会自动扣除5%手续费并记录详细的分成依据。
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}