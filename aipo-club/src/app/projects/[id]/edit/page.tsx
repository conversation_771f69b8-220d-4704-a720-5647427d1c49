"use client";

import { useState, useEffect, use } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { redirect } from "next/navigation";
import { api } from "~/trpc/react";
import { AdminLayout } from "~/components/admin-layout";
import { ProjectStatusSelect, ProjectPrioritySelect } from "~/components/project/ProjectStatusComponents";
import { ProjectTypeSelect } from "~/components/project/ProjectTypeSelect";
import { ProjectLogoUpload } from "~/components/ui/ImageUpload";


interface ProjectEditPageProps {
  params: Promise<{ id: string }>;
}

export default function ProjectEditPage({ params }: ProjectEditPageProps) {
  const resolvedParams = use(params);
  const { data: session, status } = useSession();
  const router = useRouter();
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    type: "other",
    status: "ideation",
    category: "",
    priority: "medium",
    parentId: "",
    poUserId: "",
    startDate: "",
    endDate: "",
    deadline: "",
    budget: "",
    investment: "",
    revenue: "",
    // 新增项目管理字段
    logo: "",
    visitUrl: "",
    wikiUrl: "",
    gitUrl: "",
    healthStatus: "healthy",
    version: "1.0.0",
    techStack: "",
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  // 始终调用所有Hooks，避免条件性调用
  const { data: project, isLoading: projectLoading, error } = api.project.getProject.useQuery({
    id: resolvedParams.id,
  }, {
    enabled: !!resolvedParams.id,
  });

  // 获取项目列表用于选择父项目
  const { data: projects } = api.project.getProjects.useQuery({
    page: 1,
    limit: 100,
  });

  // 获取用户列表用于选择项目PO
  const { data: users } = api.user.getUsers.useQuery({
    page: 1,
    limit: 100,
  });

  const updateProjectMutation = api.project.update.useMutation({
    onSuccess: () => {
      router.push(`/projects/${resolvedParams.id}`);
    },
    onError: (error) => {
      setErrors({ submit: error.message });
    },
  });

  // 当项目数据加载完成时，填充表单
  useEffect(() => {
    if (project) {
      setFormData({
        name: project.name,
        description: project.description || "",
        type: (project.type as string) || "other",
        status: project.status,
        category: project.category || "",
        priority: project.priority,
        parentId: project.parentId || "",
        poUserId: project.poUserId || "",
        startDate: project.startDate ? new Date(project.startDate).toISOString().split('T')[0]! : "",
        endDate: project.endDate ? new Date(project.endDate).toISOString().split('T')[0]! : "",
        deadline: project.deadline ? new Date(project.deadline).toISOString().split('T')[0]! : "",
        budget: project.budget ? project.budget.toString() : "",
        investment: project.investment ? project.investment.toString() : "",
        revenue: project.revenue ? project.revenue.toString() : "",
        // 新增项目管理字段
        logo: (project as Record<string, unknown>).logo as string || "",
        visitUrl: (project as Record<string, unknown>).visitUrl as string || "",
        wikiUrl: (project as Record<string, unknown>).wikiUrl as string || "",
        gitUrl: (project as Record<string, unknown>).gitUrl as string || "",
        healthStatus: (project as Record<string, unknown>).healthStatus as string || "healthy",
        version: (project as Record<string, unknown>).version as string || "1.0.0",
        techStack: (project as Record<string, unknown>).techStack as string || "",
      });
    }
  }, [project]);

  // 在所有Hooks调用之后进行条件性渲染
  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    );
  }

  if (!session) {
    redirect("/login");
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    // 清除对应字段的错误
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: "" }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "项目名称不能为空";
    } else if (formData.name.length > 100) {
      newErrors.name = "项目名称不能超过100个字符";
    }

    if (formData.description && formData.description.length > 1000) {
      newErrors.description = "项目描述不能超过1000个字符";
    }

    if (formData.category && formData.category.length > 50) {
      newErrors.category = "项目分类不能超过50个字符";
    }

    if (formData.budget && (isNaN(Number(formData.budget)) || Number(formData.budget) < 0)) {
      newErrors.budget = "预算必须是非负数";
    }

    if (formData.investment && (isNaN(Number(formData.investment)) || Number(formData.investment) < 0)) {
      newErrors.investment = "投资金额必须是非负数";
    }

    if (formData.revenue && (isNaN(Number(formData.revenue)) || Number(formData.revenue) < 0)) {
      newErrors.revenue = "收入金额必须是非负数";
    }

    if (formData.startDate && formData.endDate) {
      const start = new Date(formData.startDate);
      const end = new Date(formData.endDate);
      if (start >= end) {
        newErrors.endDate = "结束时间必须晚于开始时间";
      }
    }

    // 新增字段验证
    const urlRegex = /^https?:\/\/.+/;
    if (formData.logo && !urlRegex.test(formData.logo)) {
      newErrors.logo = "请输入有效的URL地址";
    }
    if (formData.visitUrl && !urlRegex.test(formData.visitUrl)) {
      newErrors.visitUrl = "请输入有效的URL地址";
    }
    if (formData.wikiUrl && !urlRegex.test(formData.wikiUrl)) {
      newErrors.wikiUrl = "请输入有效的URL地址";
    }
    if (formData.gitUrl && !urlRegex.test(formData.gitUrl)) {
      newErrors.gitUrl = "请输入有效的URL地址";
    }
    if (formData.version && formData.version.length > 50) {
      newErrors.version = "版本号不能超过50个字符";
    }
    if (formData.techStack && formData.techStack.length > 500) {
      newErrors.techStack = "技术栈描述不能超过500个字符";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      const updateData = {
        id: resolvedParams.id,
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        type: formData.type,
        status: formData.status,
        category: formData.category.trim() || undefined,
        priority: formData.priority,
        parentId: formData.parentId || undefined,
        poUserId: formData.poUserId || undefined,
        startDate: formData.startDate ? new Date(formData.startDate) : undefined,
        endDate: formData.endDate ? new Date(formData.endDate) : undefined,
        deadline: formData.deadline ? new Date(formData.deadline) : undefined,
        budget: formData.budget ? Number(formData.budget) : undefined,
        investment: formData.investment ? Number(formData.investment) : undefined,
        revenue: formData.revenue ? Number(formData.revenue) : undefined,
        // 新增项目管理字段
        logo: formData.logo.trim() || undefined,
        visitUrl: formData.visitUrl.trim() || undefined,
        wikiUrl: formData.wikiUrl.trim() || undefined,
        gitUrl: formData.gitUrl.trim() || undefined,
        healthStatus: formData.healthStatus as "healthy" | "warning" | "critical",
        version: formData.version.trim(),
        techStack: formData.techStack.trim() || undefined,
      };

      await updateProjectMutation.mutateAsync(updateData);
    } catch {
      // 错误已在 onError 中处理
    } finally {
      setIsLoading(false);
    }
  };

  if (projectLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">加载项目信息中...</div>
        </div>
      </AdminLayout>
    );
  }

  if (error || !project) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">项目不存在或无权限访问</h3>
          <p className="text-gray-600 mb-4">{error?.message || "请检查项目ID或联系管理员"}</p>
          <Link
            href="/projects"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
          >
            返回项目列表
          </Link>
        </div>
      </AdminLayout>
    );
  }

  // 检查编辑权限
  const isOwner = project.createdById === session.user.id;
  const currentMember = project.members?.find((m: any) => m.userId === session.user.id);
  const canEdit = isOwner || (currentMember && ["po", "platform"].includes(currentMember.role));

  if (!canEdit) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">权限不足</h3>
          <p className="text-gray-600 mb-4">您没有编辑此项目的权限</p>
          <Link
            href={`/projects/${resolvedParams.id}`}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
          >
            返回项目详情
          </Link>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      {/* 页面标题 */}
      <div className="mb-6">
        <nav className="flex" aria-label="Breadcrumb">
          <ol className="flex items-center space-x-4">
            <li>
              <Link href="/projects" className="text-gray-400 hover:text-gray-500">
                项目管理
              </Link>
            </li>
            <li>
              <div className="flex items-center">
                <svg className="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
                <Link href={`/projects/${resolvedParams.id}`} className="ml-4 text-gray-400 hover:text-gray-500">
                  {project.name}
                </Link>
              </div>
            </li>
            <li>
              <div className="flex items-center">
                <svg className="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
                <span className="ml-4 text-sm font-medium text-gray-500">编辑</span>
              </div>
            </li>
          </ol>
        </nav>
        <h1 className="mt-2 text-2xl font-semibold text-gray-900">编辑项目</h1>
        <p className="mt-1 text-sm text-gray-600">修改项目信息和设置</p>
      </div>

      <div className="max-w-4xl">
        <div className="bg-white shadow rounded-lg p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {errors.submit && (
              <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded">
                {errors.submit}
              </div>
            )}

            {/* 基本信息 */}
            <div>
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">基本信息</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="md:col-span-2">
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                    项目名称 *
                  </label>
                  <div className="mt-1">
                    <input
                      id="name"
                      name="name"
                      type="text"
                      required
                      value={formData.name}
                      onChange={handleChange}
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                    {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
                  </div>
                </div>

                <div className="md:col-span-2">
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                    项目描述
                  </label>
                  <div className="mt-1">
                    <textarea
                      id="description"
                      name="description"
                      rows={4}
                      value={formData.description}
                      onChange={handleChange}
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                    {errors.description && <p className="mt-1 text-sm text-red-600">{errors.description}</p>}
                  </div>
                </div>

                <div>
                  <label htmlFor="type" className="block text-sm font-medium text-gray-700">
                    项目类型
                  </label>
                  <div className="mt-1">
                    <ProjectTypeSelect
                      id="type"
                      name="type"
                      value={formData.type}
                      onChange={(value) => setFormData(prev => ({ ...prev, type: value as any }))}
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      placeholder="选择项目类型"
                    />
                  </div>
                </div>



                <div>
                  <label htmlFor="parentId" className="block text-sm font-medium text-gray-700">
                    父项目
                  </label>
                  <div className="mt-1">
                    <select
                      id="parentId"
                      name="parentId"
                      value={formData.parentId}
                      onChange={handleChange}
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    >
                      <option value="">无（顶级项目）</option>
                      {projects?.projects.filter(p => p.id !== project?.id).map((project) => (
                        <option key={project.id} value={project.id}>
                          {project.name} ({project.code})
                        </option>
                      ))}
                    </select>
                    <p className="mt-1 text-xs text-gray-500">选择父项目可创建子项目</p>
                  </div>
                </div>

                <div>
                  <label htmlFor="category" className="block text-sm font-medium text-gray-700">
                    项目分类
                  </label>
                  <div className="mt-1">
                    <input
                      id="category"
                      name="category"
                      type="text"
                      value={formData.category}
                      onChange={handleChange}
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                    {errors.category && <p className="mt-1 text-sm text-red-600">{errors.category}</p>}
                  </div>
                </div>

                <div>
                  <label htmlFor="poUserId" className="block text-sm font-medium text-gray-700">
                    项目PO
                  </label>
                  <div className="mt-1">
                    <select
                      id="poUserId"
                      name="poUserId"
                      value={formData.poUserId}
                      onChange={handleChange}
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    >
                      <option value="">默认（创建者）</option>
                      {users?.users.map((user) => (
                        <option key={user.id} value={user.id}>
                          {user.name} ({user.email})
                        </option>
                      ))}
                    </select>
                    <p className="mt-1 text-xs text-gray-500">留空则默认为项目创建者</p>
                  </div>
                </div>
              </div>
            </div>

            {/* 状态管理 */}
            <div>
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">状态管理</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="status" className="block text-sm font-medium text-gray-700">
                    项目状态
                  </label>
                  <div className="mt-1">
                    <ProjectStatusSelect
                      value={formData.status}
                      onChange={(value) => setFormData({ ...formData, status: value })}
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      placeholder="选择项目状态"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="priority" className="block text-sm font-medium text-gray-700">
                    优先级
                  </label>
                  <div className="mt-1">
                    <ProjectPrioritySelect
                      value={formData.priority}
                      onChange={(value) => setFormData({ ...formData, priority: value })}
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      placeholder="选择优先级"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* 时间管理 */}
            <div>
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">时间管理</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label htmlFor="startDate" className="block text-sm font-medium text-gray-700">
                    开始时间
                  </label>
                  <div className="mt-1">
                    <input
                      id="startDate"
                      name="startDate"
                      type="date"
                      value={formData.startDate}
                      onChange={handleChange}
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="endDate" className="block text-sm font-medium text-gray-700">
                    结束时间
                  </label>
                  <div className="mt-1">
                    <input
                      id="endDate"
                      name="endDate"
                      type="date"
                      value={formData.endDate}
                      onChange={handleChange}
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                    {errors.endDate && <p className="mt-1 text-sm text-red-600">{errors.endDate}</p>}
                  </div>
                </div>

                <div>
                  <label htmlFor="deadline" className="block text-sm font-medium text-gray-700">
                    截止时间
                  </label>
                  <div className="mt-1">
                    <input
                      id="deadline"
                      name="deadline"
                      type="date"
                      value={formData.deadline}
                      onChange={handleChange}
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* 财务管理 */}
            <div>
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">财务管理</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label htmlFor="budget" className="block text-sm font-medium text-gray-700">
                    预算（元）
                  </label>
                  <div className="mt-1">
                    <input
                      id="budget"
                      name="budget"
                      type="number"
                      min="0"
                      step="0.01"
                      value={formData.budget}
                      onChange={handleChange}
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                    {errors.budget && <p className="mt-1 text-sm text-red-600">{errors.budget}</p>}
                  </div>
                </div>

                <div>
                  <label htmlFor="investment" className="block text-sm font-medium text-gray-700">
                    投资金额（元）
                  </label>
                  <div className="mt-1">
                    <input
                      id="investment"
                      name="investment"
                      type="number"
                      min="0"
                      step="0.01"
                      value={formData.investment}
                      onChange={handleChange}
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                    {errors.investment && <p className="mt-1 text-sm text-red-600">{errors.investment}</p>}
                  </div>
                </div>

                <div>
                  <label htmlFor="revenue" className="block text-sm font-medium text-gray-700">
                    收入金额（元）
                  </label>
                  <div className="mt-1">
                    <input
                      id="revenue"
                      name="revenue"
                      type="number"
                      min="0"
                      step="0.01"
                      value={formData.revenue}
                      onChange={handleChange}
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                    {errors.revenue && <p className="mt-1 text-sm text-red-600">{errors.revenue}</p>}
                  </div>
                </div>
              </div>
            </div>

            {/* 项目管理字段 */}
            <div>
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">项目管理</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    项目Logo
                  </label>
                  <ProjectLogoUpload
                    value={formData.logo}
                    onChange={(url) => {
                      setFormData(prev => ({ ...prev, logo: url }));
                      // 清除logo字段的错误
                      if (errors.logo) {
                        setErrors(prev => ({ ...prev, logo: "" }));
                      }
                    }}
                    onError={(error) => {
                      setErrors(prev => ({ ...prev, logo: error }));
                    }}
                    disabled={isLoading}
                  />
                  {errors.logo && <p className="mt-1 text-sm text-red-600">{errors.logo}</p>}
                </div>

                <div>
                  <label htmlFor="visitUrl" className="block text-sm font-medium text-gray-700">
                    访问地址
                  </label>
                  <div className="mt-1">
                    <input
                      id="visitUrl"
                      name="visitUrl"
                      type="url"
                      value={formData.visitUrl}
                      onChange={handleChange}
                      placeholder="https://example.com"
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                    {errors.visitUrl && <p className="mt-1 text-sm text-red-600">{errors.visitUrl}</p>}
                    <p className="mt-1 text-xs text-gray-500">项目生产环境访问地址</p>
                  </div>
                </div>

                <div>
                  <label htmlFor="wikiUrl" className="block text-sm font-medium text-gray-700">
                    知识库地址
                  </label>
                  <div className="mt-1">
                    <input
                      id="wikiUrl"
                      name="wikiUrl"
                      type="url"
                      value={formData.wikiUrl}
                      onChange={handleChange}
                      placeholder="https://wiki.example.com"
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                    {errors.wikiUrl && <p className="mt-1 text-sm text-red-600">{errors.wikiUrl}</p>}
                    <p className="mt-1 text-xs text-gray-500">项目文档和知识库地址</p>
                  </div>
                </div>

                <div>
                  <label htmlFor="gitUrl" className="block text-sm font-medium text-gray-700">
                    代码仓库
                  </label>
                  <div className="mt-1">
                    <input
                      id="gitUrl"
                      name="gitUrl"
                      type="url"
                      value={formData.gitUrl}
                      onChange={handleChange}
                      placeholder="https://github.com/user/repo"
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                    {errors.gitUrl && <p className="mt-1 text-sm text-red-600">{errors.gitUrl}</p>}
                    <p className="mt-1 text-xs text-gray-500">GitHub/GitLab等代码仓库地址</p>
                  </div>
                </div>

                <div>
                  <label htmlFor="healthStatus" className="block text-sm font-medium text-gray-700">
                    健康状态
                  </label>
                  <div className="mt-1">
                    <select
                      id="healthStatus"
                      name="healthStatus"
                      value={formData.healthStatus}
                      onChange={handleChange}
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    >
                      <option value="healthy">✅ 健康</option>
                      <option value="warning">⚠️ 警告</option>
                      <option value="critical">🚨 严重</option>
                    </select>
                    <p className="mt-1 text-xs text-gray-500">项目当前运行状态</p>
                  </div>
                </div>

                <div>
                  <label htmlFor="version" className="block text-sm font-medium text-gray-700">
                    版本号
                  </label>
                  <div className="mt-1">
                    <input
                      id="version"
                      name="version"
                      type="text"
                      value={formData.version}
                      onChange={handleChange}
                      placeholder="1.0.0"
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                    {errors.version && <p className="mt-1 text-sm text-red-600">{errors.version}</p>}
                    <p className="mt-1 text-xs text-gray-500">项目当前版本号</p>
                  </div>
                </div>

                <div className="md:col-span-2">
                  <label htmlFor="techStack" className="block text-sm font-medium text-gray-700">
                    技术栈
                  </label>
                  <div className="mt-1">
                    <textarea
                      id="techStack"
                      name="techStack"
                      rows={3}
                      value={formData.techStack}
                      onChange={handleChange}
                      placeholder="React, Node.js, MySQL, Docker..."
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                    {errors.techStack && <p className="mt-1 text-sm text-red-600">{errors.techStack}</p>}
                    <p className="mt-1 text-xs text-gray-500">项目使用的技术栈描述</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
              <Link
                href={`/projects/${resolvedParams.id}`}
                className="bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-300"
              >
                取消
              </Link>
              <button
                type="submit"
                disabled={isLoading}
                className="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? "保存中..." : "保存更改"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </AdminLayout>
  );
}
