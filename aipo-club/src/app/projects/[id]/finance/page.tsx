"use client";

import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { api } from "~/trpc/react";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Textarea } from "~/components/ui/textarea";
import { Label } from "~/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Badge } from "~/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "~/components/ui/dialog";
import { toast } from "~/hooks/use-toast";
import { formatCurrency, formatDate } from "~/lib/utils";
import { 
  Plus, 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  FileText,
  Edit,
  Trash2,
  BarChart3,
  <PERSON><PERSON>hart
} from "lucide-react";

export default function ProjectFinancePage() {
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;

  const [showAddTransaction, setShowAddTransaction] = useState(false);
  const [editingTransaction, setEditingTransaction] = useState<any>(null);
  const [transactionForm, setTransactionForm] = useState({
    type: "income" as "income" | "expense",
    accountId: "",
    amount: "",
    currency: "CNY",
    description: "",
    category: "",
    subCategory: "",
    transactionDate: new Date().toISOString().split('T')[0],
    invoiceNumber: "",
    receiptUrl: "",
    paymentMethod: "",
    vendor: "",
    tags: [] as string[],
  });

  const { data: project } = api.project.getProject.useQuery({ id: projectId });
  const { data: financialAccounts } = api.finance.getFinancialAccounts.useQuery();
  const { data: transactions, refetch: refetchTransactions } = api.finance.getProjectTransactions.useQuery({
    projectId,
    limit: 50,
  });
  const { data: financialStats } = api.finance.getProjectFinancialStats.useQuery({
    projectId,
    year: new Date().getFullYear(),
  });

  const createTransactionMutation = api.finance.createTransaction.useMutation({
    onSuccess: () => {
      toast({
        title: "交易记录已创建",
        description: "财务交易记录已成功添加",
      });
      setShowAddTransaction(false);
      resetForm();
      void refetchTransactions();
    },
    onError: (error) => {
      toast({
        title: "创建失败",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const updateTransactionMutation = api.finance.updateTransaction.useMutation({
    onSuccess: () => {
      toast({
        title: "交易记录已更新",
        description: "财务交易记录已成功修改",
      });
      setEditingTransaction(null);
      resetForm();
      void refetchTransactions();
    },
    onError: (error) => {
      toast({
        title: "更新失败",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const deleteTransactionMutation = api.finance.deleteTransaction.useMutation({
    onSuccess: () => {
      toast({
        title: "交易记录已删除",
        description: "财务交易记录已成功删除",
      });
      void refetchTransactions();
    },
    onError: (error) => {
      toast({
        title: "删除失败",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const resetForm = () => {
    setTransactionForm({
      type: "income",
      accountId: "",
      amount: "",
      currency: "CNY",
      description: "",
      category: "",
      subCategory: "",
      transactionDate: new Date().toISOString().split('T')[0],
      invoiceNumber: "",
      receiptUrl: "",
      paymentMethod: "",
      vendor: "",
      tags: [],
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!transactionForm.accountId || !transactionForm.amount || !transactionForm.description) {
      toast({
        title: "请填写必需字段",
        description: "请确保填写了科目、金额和描述",
        variant: "destructive",
      });
      return;
    }

    const transactionData = {
      projectId,
      accountId: transactionForm.accountId,
      type: transactionForm.type,
      amount: parseFloat(transactionForm.amount),
      currency: transactionForm.currency,
      description: transactionForm.description,
      category: transactionForm.category,
      subCategory: transactionForm.subCategory || undefined,
      transactionDate: new Date(transactionForm.transactionDate || new Date()),
      invoiceNumber: transactionForm.invoiceNumber || undefined,
      receiptUrl: transactionForm.receiptUrl || undefined,
      paymentMethod: transactionForm.paymentMethod || undefined,
      vendor: transactionForm.vendor || undefined,
      tags: transactionForm.tags.length > 0 ? transactionForm.tags : undefined,
    };

    if (editingTransaction) {
      updateTransactionMutation.mutate({
        id: editingTransaction.id,
        data: transactionData,
      });
    } else {
      createTransactionMutation.mutate(transactionData);
    }
  };

  const handleEdit = (transaction: any) => {
    setEditingTransaction(transaction);
    setTransactionForm({
      type: transaction.type,
      accountId: transaction.accountId,
      amount: transaction.amount.toString(),
      currency: transaction.currency,
      description: transaction.description,
      category: transaction.category,
      subCategory: transaction.subCategory || "",
      transactionDate: transaction.transactionDate.split('T')[0],
      invoiceNumber: transaction.invoiceNumber || "",
      receiptUrl: transaction.receiptUrl || "",
      paymentMethod: transaction.paymentMethod || "",
      vendor: transaction.vendor || "",
      tags: transaction.tags ? JSON.parse(transaction.tags) : [],
    });
    setShowAddTransaction(true);
  };

  const handleDelete = (transactionId: string) => {
    if (confirm("确定要删除这条财务记录吗？")) {
      deleteTransactionMutation.mutate({ id: transactionId });
    }
  };

  // 过滤科目选项
  const incomeAccounts = financialAccounts?.filter((account: any) => 
    account.type === "收入" || account.category.includes("收入")
  ) || [];
  
  const expenseAccounts = financialAccounts?.filter((account: any) => 
    account.type === "费用" || account.type === "成本" || 
    account.category.includes("费用") || account.category.includes("成本")
  ) || [];

  const availableAccounts = transactionForm.type === "income" ? incomeAccounts : expenseAccounts;

  // 常用分类
  const incomeCategories = ["产品销售", "服务收入", "订阅收入", "广告收入", "咨询收入", "其他收入"];
  const expenseCategories = ["人员成本", "营销推广", "办公费用", "技术服务", "差旅费", "培训费", "其他费用"];
  const availableCategories = transactionForm.type === "income" ? incomeCategories : expenseCategories;

  if (!project) {
    return <div>加载中...</div>;
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="mb-4"
        >
          ← 返回项目详情
        </Button>
        <h1 className="text-3xl font-bold">财务管理</h1>
        <p className="text-muted-foreground">项目：{project.name}</p>
      </div>

      {/* 财务概览 */}
      <div className="grid gap-6 md:grid-cols-4 mb-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-sm">
              <TrendingUp className="h-4 w-4 text-green-600" />
              总收入
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(financialStats?.totalIncome ?? 0)}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-sm">
              <TrendingDown className="h-4 w-4 text-red-600" />
              总支出
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {formatCurrency(financialStats?.totalExpense ?? 0)}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-sm">
              <DollarSign className="h-4 w-4 text-blue-600" />
              净利润
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${
              (financialStats?.netProfit ?? 0) >= 0 ? "text-green-600" : "text-red-600"
            }`}>
              {formatCurrency(financialStats?.netProfit ?? 0)}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-sm">
              <BarChart3 className="h-4 w-4 text-purple-600" />
              利润率
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {financialStats?.profitMargin?.toFixed(1) ?? 0}%
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="transactions" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="transactions">交易记录</TabsTrigger>
          <TabsTrigger value="reports">财务报表</TabsTrigger>
          <TabsTrigger value="analytics">数据分析</TabsTrigger>
        </TabsList>

        <TabsContent value="transactions" className="mt-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>财务交易记录</CardTitle>
                <CardDescription>管理项目的收入和支出记录</CardDescription>
              </div>
              <Dialog open={showAddTransaction} onOpenChange={setShowAddTransaction}>
                <DialogTrigger asChild>
                  <Button onClick={() => { resetForm(); setEditingTransaction(null); }}>
                    <Plus className="h-4 w-4 mr-2" />
                    添加交易
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>
                      {editingTransaction ? "编辑交易记录" : "添加财务交易"}
                    </DialogTitle>
                    <DialogDescription>
                      记录项目的收入或支出交易
                    </DialogDescription>
                  </DialogHeader>
                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <Label htmlFor="type">交易类型</Label>
                        <Select
                          value={transactionForm.type}
                          onValueChange={(value: "income" | "expense") =>
                            setTransactionForm({ ...transactionForm, type: value, accountId: "", category: "" })
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="income">收入</SelectItem>
                            <SelectItem value="expense">支出</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="accountId">财务科目</Label>
                        <Select
                          value={transactionForm.accountId}
                          onValueChange={(value) =>
                            setTransactionForm({ ...transactionForm, accountId: value })
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="选择科目" />
                          </SelectTrigger>
                          <SelectContent>
                            {availableAccounts.map((account: any) => (
                              <SelectItem key={account.id} value={account.id}>
                                {account.code} - {account.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <Label htmlFor="amount">金额</Label>
                        <Input
                          id="amount"
                          type="number"
                          step="0.01"
                          placeholder="0.00"
                          value={transactionForm.amount}
                          onChange={(e) =>
                            setTransactionForm({ ...transactionForm, amount: e.target.value })
                          }
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="currency">货币</Label>
                        <Select
                          value={transactionForm.currency}
                          onValueChange={(value) =>
                            setTransactionForm({ ...transactionForm, currency: value })
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="CNY">人民币 (CNY)</SelectItem>
                            <SelectItem value="USD">美元 (USD)</SelectItem>
                            <SelectItem value="EUR">欧元 (EUR)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="description">交易描述</Label>
                      <Textarea
                        id="description"
                        placeholder="详细描述这笔交易"
                        value={transactionForm.description}
                        onChange={(e) =>
                          setTransactionForm({ ...transactionForm, description: e.target.value })
                        }
                        required
                      />
                    </div>

                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <Label htmlFor="category">交易分类</Label>
                        <Select
                          value={transactionForm.category}
                          onValueChange={(value) =>
                            setTransactionForm({ ...transactionForm, category: value })
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="选择分类" />
                          </SelectTrigger>
                          <SelectContent>
                            {availableCategories.map((category) => (
                              <SelectItem key={category} value={category}>
                                {category}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="transactionDate">交易日期</Label>
                        <Input
                          id="transactionDate"
                          type="date"
                          value={transactionForm.transactionDate}
                          onChange={(e) =>
                            setTransactionForm({ ...transactionForm, transactionDate: e.target.value })
                          }
                          required
                        />
                      </div>
                    </div>

                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <Label htmlFor="vendor">供应商/客户</Label>
                        <Input
                          id="vendor"
                          placeholder="供应商或客户名称"
                          value={transactionForm.vendor}
                          onChange={(e) =>
                            setTransactionForm({ ...transactionForm, vendor: e.target.value })
                          }
                        />
                      </div>
                      <div>
                        <Label htmlFor="paymentMethod">支付方式</Label>
                        <Select
                          value={transactionForm.paymentMethod}
                          onValueChange={(value) =>
                            setTransactionForm({ ...transactionForm, paymentMethod: value })
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="选择支付方式" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="cash">现金</SelectItem>
                            <SelectItem value="bank_transfer">银行转账</SelectItem>
                            <SelectItem value="alipay">支付宝</SelectItem>
                            <SelectItem value="wechat">微信支付</SelectItem>
                            <SelectItem value="credit_card">信用卡</SelectItem>
                            <SelectItem value="other">其他</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <Label htmlFor="invoiceNumber">发票号码</Label>
                        <Input
                          id="invoiceNumber"
                          placeholder="发票或收据号码"
                          value={transactionForm.invoiceNumber}
                          onChange={(e) =>
                            setTransactionForm({ ...transactionForm, invoiceNumber: e.target.value })
                          }
                        />
                      </div>
                      <div>
                        <Label htmlFor="receiptUrl">收据/发票图片</Label>
                        <Input
                          id="receiptUrl"
                          type="url"
                          placeholder="图片URL"
                          value={transactionForm.receiptUrl}
                          onChange={(e) =>
                            setTransactionForm({ ...transactionForm, receiptUrl: e.target.value })
                          }
                        />
                      </div>
                    </div>

                    <div className="flex justify-end gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setShowAddTransaction(false)}
                      >
                        取消
                      </Button>
                      <Button
                        type="submit"
                        disabled={createTransactionMutation.isPending || updateTransactionMutation.isPending}
                      >
                        {editingTransaction ? "更新" : "创建"}
                      </Button>
                    </div>
                  </form>
                </DialogContent>
              </Dialog>
            </CardHeader>
            <CardContent>
              {transactions && transactions.transactions.length > 0 ? (
                <div className="space-y-4">
                  {transactions.transactions.map((transaction) => (
                    <div
                      key={transaction.id}
                      className="flex items-center justify-between p-4 border rounded-lg"
                    >
                      <div className="flex items-center gap-4">
                        <div className={`p-2 rounded-full ${
                          transaction.type === "income" 
                            ? "bg-green-100 text-green-600" 
                            : "bg-red-100 text-red-600"
                        }`}>
                          {transaction.type === "income" ? (
                            <TrendingUp className="h-4 w-4" />
                          ) : (
                            <TrendingDown className="h-4 w-4" />
                          )}
                        </div>
                        <div>
                          <div className="font-medium">{transaction.description}</div>
                          <div className="text-sm text-muted-foreground">
                            {transaction.account.name} • {transaction.category}
                            {transaction.vendor && ` • ${transaction.vendor}`}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {formatDate(transaction.transactionDate)} • 
                            由 {transaction.creator.name} 创建
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="text-right">
                          <div className={`text-lg font-bold ${
                            transaction.type === "income" ? "text-green-600" : "text-red-600"
                          }`}>
                            {transaction.type === "income" ? "+" : "-"}
                            {formatCurrency(transaction.amount)} {transaction.currency}
                          </div>
                          <Badge
                            variant={
                              transaction.status === "confirmed" ? "default" :
                              transaction.status === "pending" ? "secondary" : "destructive"
                            }
                          >
                            {transaction.status === "confirmed" ? "已确认" :
                             transaction.status === "pending" ? "待审批" : "已取消"}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEdit(transaction)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDelete(transaction.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>暂无财务交易记录</p>
                  <p className="text-sm">添加第一笔收入或支出记录</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports" className="mt-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5" />
                  收入分析
                </CardTitle>
              </CardHeader>
              <CardContent>
                {financialStats?.incomeByCategory && financialStats.incomeByCategory.length > 0 ? (
                  <div className="space-y-3">
                    {financialStats.incomeByCategory.map((item, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-sm">{item.category}</span>
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium">
                            {formatCurrency(item.amount)}
                          </span>
                          <div className="w-20 h-2 bg-gray-200 rounded-full">
                            <div
                              className="h-2 bg-green-500 rounded-full"
                              style={{
                                width: `${(item.amount / (financialStats.totalIncome || 1)) * 100}%`,
                              }}
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted-foreground">暂无收入数据</p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5" />
                  支出分析
                </CardTitle>
              </CardHeader>
              <CardContent>
                {financialStats?.expenseByCategory && financialStats.expenseByCategory.length > 0 ? (
                  <div className="space-y-3">
                    {financialStats.expenseByCategory.map((item, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-sm">{item.category}</span>
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium">
                            {formatCurrency(item.amount)}
                          </span>
                          <div className="w-20 h-2 bg-gray-200 rounded-full">
                            <div
                              className="h-2 bg-red-500 rounded-full"
                              style={{
                                width: `${(item.amount / (financialStats.totalExpense || 1)) * 100}%`,
                              }}
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted-foreground">暂无支出数据</p>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>财务健康度分析</CardTitle>
              <CardDescription>基于当前财务数据的项目健康度评估</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="p-4 border rounded-lg text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {financialStats?.profitMargin?.toFixed(1) ?? 0}%
                  </div>
                  <div className="text-sm text-muted-foreground">利润率</div>
                  <div className="text-xs mt-1">
                    {(financialStats?.profitMargin ?? 0) > 20 ? "优秀" : 
                     (financialStats?.profitMargin ?? 0) > 10 ? "良好" : "需改善"}
                  </div>
                </div>
                <div className="p-4 border rounded-lg text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {financialStats?.incomeByCategory?.length ?? 0}
                  </div>
                  <div className="text-sm text-muted-foreground">收入来源</div>
                  <div className="text-xs mt-1">多样化程度</div>
                </div>
                <div className="p-4 border rounded-lg text-center">
                  <div className="text-2xl font-bold text-orange-600">
                    {financialStats?.expenseByCategory?.length ?? 0}
                  </div>
                  <div className="text-sm text-muted-foreground">支出类别</div>
                  <div className="text-xs mt-1">成本结构</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}