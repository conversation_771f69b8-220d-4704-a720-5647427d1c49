"use client";

import { useState, use } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { redirect } from "next/navigation";
import { api } from "~/trpc/react";
import { AdminLayout } from "~/components/admin-layout";
import { ProjectHierarchy } from "~/components/project/ProjectHierarchy";
import { ProjectStatusBadge } from "~/components/project/ProjectStatusComponents";
import { MilestoneManager } from "~/components/project/MilestoneManager";
import { ProfitSharingManager } from "~/components/project/ProfitSharingManager";
import { ProjectManagementFields } from "~/components/project/ProjectManagementFields";
import { getProjectTypeConfig, getProjectPriorityConfig, getProjectCategoryConfig } from "~/types/project";
import { UnifiedMemberManager } from "~/components/project/UnifiedMemberManager";

interface ProjectDetailPageProps {
  params: Promise<{ id: string }>;
}

export default function ProjectDetailPage({ params }: ProjectDetailPageProps) {
  const resolvedParams = use(params);
  const { data: session, status } = useSession();
  const [activeTab, setActiveTab] = useState("overview");

  // 始终调用所有Hooks，避免条件性调用
  const { data: project, isLoading, error, refetch } = api.project.getProject.useQuery({
    id: resolvedParams.id,
  }, {
    enabled: !!resolvedParams.id && !!session,
    staleTime: 60 * 1000, // 1分钟内数据被认为是新鲜的
    gcTime: 10 * 60 * 1000, // 10分钟后从缓存中清除
  });

  // 获取项目里程碑数据
  const { data: milestones } = api.project.getMilestones.useQuery(
    { projectId: resolvedParams.id },
    { enabled: !!resolvedParams.id && !!session }
  );

  // 获取项目层级关系
  const { data: hierarchyData } = api.project.getProjectHierarchy.useQuery(
    { projectId: resolvedParams.id },
    { enabled: !!resolvedParams.id && !!session }
  );

  const handleMemberUpdate = () => {
    void refetch();
  };



  // 在所有Hooks调用之后进行条件性渲染
  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    );
  }

  if (!session) {
    redirect("/login");
  }







  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">加载项目信息中...</div>
        </div>
      </AdminLayout>
    );
  }

  if (error || !project) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">项目不存在或无权限访问</h3>
          <p className="text-gray-600 mb-4">{error?.message || "请检查项目ID或联系管理员"}</p>
          <Link
            href="/projects"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
          >
            返回项目列表
          </Link>
        </div>
      </AdminLayout>
    );
  }

  const isOwner = project.createdById === session.user.id;
  const currentMember = project.members?.find((m: any) => m.userId === session.user.id);
  const canEdit = isOwner || (currentMember && ["po", "platform"].includes(currentMember.role)) || false;

  const tabs = [
    { id: "overview", name: "概览", icon: "📊" },
    { id: "hierarchy", name: "项目层级", icon: "🏗️" },
    { id: "milestones", name: "里程碑", icon: "🎯" },
    { id: "members", name: "成员", icon: "👥" },
    { id: "shares", name: "分成", icon: "💰" },
    { id: "finance", name: "财务管理", icon: "💸", hidden: !canEdit },
    { id: "settings", name: "设置", icon: "⚙️", hidden: !canEdit },
  ].filter(tab => !tab.hidden);

  return (
    <AdminLayout>
      {/* 页面标题和操作 */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <nav className="flex" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-4">
                <li>
                  <Link href="/projects" className="text-gray-400 hover:text-gray-500">
                    项目管理
                  </Link>
                </li>
                <li>
                  <div className="flex items-center">
                    <svg className="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="ml-4 text-sm font-medium text-gray-500">{project.name}</span>
                  </div>
                </li>
              </ol>
            </nav>
            <div className="mt-2 flex items-center space-x-3">
              <h1 className="text-2xl font-semibold text-gray-900">{project.name}</h1>
              <ProjectStatusBadge status={project.status} />
            </div>
            <p className="mt-1 text-sm text-gray-600">{project.description || "暂无描述"}</p>
          </div>
          {canEdit && (
            <div className="flex space-x-3">
              <Link
                href={`/projects/${project.id}/edit`}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                编辑项目
              </Link>
            </div>
          )}
        </div>
      </div>

      {/* 标签页导航 */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`${
                activeTab === tab.id
                  ? "border-indigo-500 text-indigo-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
            >
              <span>{tab.icon}</span>
              <span>{tab.name}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* 标签页内容 */}
      <div className="space-y-6">
        {activeTab === "overview" && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* 项目信息 */}
            <div className="lg:col-span-2 space-y-6">
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">项目信息</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">项目编码</dt>
                    <dd className="mt-1 text-sm text-gray-900 font-mono">{project.code}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">项目类型</dt>
                    <dd className="mt-1 text-sm text-gray-900">{getProjectTypeConfig(project.type).label}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">创建者</dt>
                    <dd className="mt-1 text-sm text-gray-900">{(project as any).createdBy || "未知"}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">项目PO</dt>
                    <dd className="mt-1 text-sm text-gray-900">{(project as any).user_project_poUserIdTouser?.name || "未设置"}</dd>
                  </div>

                  <div>
                    <dt className="text-sm font-medium text-gray-500">分类</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {project.category ? getProjectCategoryConfig(project.category).label : "未分类"}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">优先级</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getProjectPriorityConfig(project.priority).color}`}>
                        {getProjectPriorityConfig(project.priority).label}
                      </span>
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">创建时间</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {new Date(project.createdAt).toLocaleDateString('zh-CN')}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">更新时间</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {new Date(project.updatedAt).toLocaleDateString('zh-CN')}
                    </dd>
                  </div>
                  {project.startDate && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">开始时间</dt>
                      <dd className="mt-1 text-sm text-gray-900">
                        {new Date(project.startDate).toLocaleDateString('zh-CN')}
                      </dd>
                    </div>
                  )}
                  {project.deadline && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">截止时间</dt>
                      <dd className="mt-1 text-sm text-gray-900">
                        {new Date(project.deadline).toLocaleDateString('zh-CN')}
                      </dd>
                    </div>
                  )}
                </div>
              </div>

              {/* 财务信息 */}
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">财务信息</h3>
                <div className="grid grid-cols-2 lg:grid-cols-5 gap-4">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">预算</dt>
                    <dd className="mt-1 text-lg font-semibold text-gray-900">
                      ¥{(project.budget || 0).toLocaleString()}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">投资</dt>
                    <dd className="mt-1 text-lg font-semibold text-blue-600">
                      ¥{(project.investment || 0).toLocaleString()}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">历史收入</dt>
                    <dd className="mt-1 text-lg font-semibold text-green-600">
                      ¥{(project.revenue || 0).toLocaleString()}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">当月收益</dt>
                    <dd className="mt-1 text-lg font-semibold text-emerald-600">
                      ¥{(project.currentMonthRevenue || 0).toLocaleString()}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">累计收益</dt>
                    <dd className="mt-1 text-lg font-semibold text-green-700">
                      ¥{(project.totalRevenue || 0).toLocaleString()}
                    </dd>
                  </div>
                </div>
              </div>

              {/* 项目管理信息 */}
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">项目管理</h3>
                <ProjectManagementFields
                  project={project as Record<string, unknown> & {
                    logo?: string | null;
                    visitUrl?: string | null;
                    wikiUrl?: string | null;
                    gitUrl?: string | null;
                    healthStatus?: string;
                    version?: string;
                    techStack?: string | null;
                    lastDeployAt?: Date | null;
                  }}
                  showLabels={true}
                />
              </div>
            </div>

            {/* 侧边栏 */}
            <div className="space-y-6">
              {/* 项目统计 */}
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">项目统计</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">团队成员</span>
                    <span className="text-sm font-medium text-gray-900">{project.members?.length || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">项目里程碑</span>
                    <span className="text-sm font-medium text-gray-900">{milestones?.length || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">分成规则</span>
                    <span className="text-sm font-medium text-gray-900">{project.shares?.length || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">运行天数</span>
                    <span className="text-sm font-medium text-gray-900">
                      {Math.floor((new Date().getTime() - new Date(project.createdAt).getTime()) / (1000 * 60 * 60 * 24))}
                    </span>
                  </div>
                </div>
              </div>

              {/* 快速操作 */}
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">快速操作</h3>
                <div className="space-y-3">
                  {canEdit && (
                    <>
                      <Link
                        href={`/projects/${project.id}/edit`}
                        className="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
                      >
                        编辑项目
                      </Link>
                      <button
                        onClick={() => setActiveTab("hierarchy")}
                        className="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                      >
                        项目层级
                      </button>
                      <button
                        onClick={() => setActiveTab("milestones")}
                        className="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                      >
                        里程碑管理
                      </button>
                      <button
                        onClick={() => setActiveTab("members")}
                        className="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                      >
                        管理成员
                      </button>
                      <button
                        onClick={() => setActiveTab("shares")}
                        className="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                      >
                        分成管理
                      </button>
                      <Link
                        href={`/projects/${project.id}/finance`}
                        className="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                      >
                        财务管理
                      </Link>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === "hierarchy" && (
          <div className="bg-white shadow rounded-lg p-6">
            <ProjectHierarchy
              projects={hierarchyData?.projects || []}
              currentProjectId={hierarchyData?.currentProjectId}
              onProjectSelect={(selectedProject) => {
                // 可以添加项目选择逻辑，比如跳转到选中的项目
                window.location.href = `/projects/${selectedProject.id}`;
              }}
            />
          </div>
        )}

        {activeTab === "milestones" && (
          <MilestoneManager projectId={project.id} />
        )}

        {activeTab === "members" && (
          <UnifiedMemberManager
            projectId={project.id}
            members={project.members || []}
            canEdit={canEdit}
            onMemberUpdate={handleMemberUpdate}
          />
        )}

        {activeTab === "shares" && (
          <div className="bg-white shadow rounded-lg p-6">
            <div className="mb-6">
              <h2 className="text-xl font-semibold text-gray-900">分成管理</h2>
              <p className="mt-1 text-sm text-gray-500">
                管理项目的分成记录和收益计算。成员的基本分成比例请在「成员」标签页中设置。
              </p>
            </div>
            <ProfitSharingManager
              projectId={project.id}
              members={project.members || []}
              shares={project.shares || []}
              currentMonthRevenue={project.currentMonthRevenue || 0}
              totalRevenue={project.totalRevenue || 0}
            />
          </div>
        )}

        {activeTab === "finance" && canEdit && (
          <div className="bg-white shadow rounded-lg p-6">
            <div className="mb-6">
              <h2 className="text-xl font-semibold text-gray-900">财务管理</h2>
              <p className="mt-1 text-sm text-gray-500">
                管理项目的收入和支出记录，计算成本和利润，进行分成管理。
              </p>
            </div>
            <div className="flex justify-center">
              <Link
                href={`/projects/${project.id}/finance`}
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                打开财务管理界面
              </Link>
            </div>
          </div>
        )}

        {activeTab === "settings" && canEdit && (
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">项目设置</h3>
            <div className="space-y-6">
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-2">危险操作</h4>
                <div className="border border-red-200 rounded-lg p-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <h5 className="text-sm font-medium text-red-900">删除项目</h5>
                      <p className="text-sm text-red-700">删除后无法恢复，请谨慎操作</p>
                    </div>
                    <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700">
                      删除项目
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
}
