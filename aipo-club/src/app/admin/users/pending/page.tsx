"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import Image from "next/image";
import { redirect } from "next/navigation";
import { api } from "~/trpc/react";
import { AdminLayout } from "~/components/admin-layout";

export default function PendingUsersPage() {
  const { data: session, status } = useSession();
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");

  // 获取待审核用户列表
  const { data: usersData, isLoading, refetch } = api.user.getUsers.useQuery({
    page: currentPage,
    limit: 20,
    status: "pending",
    search: searchTerm || undefined,
  }, {
    enabled: !!session,
    staleTime: 30 * 1000, // 30秒内数据被认为是新鲜的
  });

  // 激活用户
  const activateUserMutation = api.user.activateUser.useMutation({
    onSuccess: () => {
      void refetch();
      alert("用户已激活，激活通知邮件已发送");
    },
    onError: (error) => {
      alert(`激活失败: ${error.message}`);
    },
  });

  // 拒绝用户
  const updateStatusMutation = api.user.updateUserStatus.useMutation({
    onSuccess: () => {
      void refetch();
    },
    onError: (error) => {
      alert(`操作失败: ${error.message}`);
    },
  });

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    );
  }

  if (!session) {
    redirect("/login");
  }

  const handleActivateUser = async (userId: string) => {
    if (confirm("确定要激活此用户吗？激活后将自动发送通知邮件。")) {
      await activateUserMutation.mutateAsync({ userId });
    }
  };

  const handleRejectUser = async (userId: string) => {
    if (confirm("确定要拒绝此用户的注册申请吗？")) {
      await updateStatusMutation.mutateAsync({
        userId,
        status: "rejected",
      });
    }
  };

  const handleBatchActivate = async () => {
    if (!usersData?.users || usersData.users.length === 0) return;
    
    if (confirm(`确定要批量激活所有 ${usersData.users.length} 个待审核用户吗？`)) {
      try {
        for (const user of usersData.users) {
          await activateUserMutation.mutateAsync({ userId: user.id });
        }
        alert("批量激活完成！");
      } catch (error) {
        console.error("批量激活失败:", error);
      }
    }
  };

  return (
    <AdminLayout>
      {/* 页面标题和导航 */}
      <div className="mb-6">
        <nav className="flex mb-4" aria-label="Breadcrumb">
          <ol className="flex items-center space-x-4">
            <li>
              <Link href="/users" className="text-gray-400 hover:text-gray-500">
                用户中心
              </Link>
            </li>
            <li>
              <div className="flex items-center">
                <svg className="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
                <Link href="/admin/users" className="ml-4 text-gray-400 hover:text-gray-500">
                  用户管理
                </Link>
              </div>
            </li>
            <li>
              <div className="flex items-center">
                <svg className="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
                <span className="ml-4 text-sm font-medium text-gray-500">待审核用户</span>
              </div>
            </li>
          </ol>
        </nav>
        
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">待审核用户</h1>
            <p className="mt-1 text-sm text-gray-600">
              审核新注册用户，激活后用户将收到欢迎邮件
              {usersData?.total && (
                <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800">
                  {usersData.total} 个待审核
                </span>
              )}
            </p>
          </div>
          <div className="flex space-x-3">
            {usersData?.users && usersData.users.length > 0 && (
              <button
                onClick={handleBatchActivate}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                批量激活
              </button>
            )}
            <Link
              href="/admin/users"
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              返回用户管理
            </Link>
          </div>
        </div>
      </div>

      {/* 搜索框 */}
      <div className="bg-white shadow rounded-lg p-6 mb-6">
        <div className="max-w-md">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            搜索待审核用户
          </label>
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="搜索昵称、姓名或邮箱"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
          />
        </div>
      </div>

      {/* 用户列表 */}
      <div className="bg-white shadow rounded-lg">
        {isLoading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto mb-4"></div>
            <div className="text-gray-500">加载待审核用户中...</div>
          </div>
        ) : usersData?.users && usersData.users.length > 0 ? (
          <>
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                待审核用户列表 ({usersData.total} 个)
              </h3>
            </div>
            
            <div className="divide-y divide-gray-200">
              {usersData.users.map((user) => (
                <div key={user.id} className="p-6 hover:bg-gray-50">
                  <div className="flex items-start space-x-4">
                    {/* 用户头像 */}
                    <div className="flex-shrink-0">
                      {user.avatar ? (
                        <Image
                          className="h-12 w-12 rounded-full"
                          src={user.avatar}
                          alt={user.name || "用户头像"}
                          width={48}
                          height={48}
                        />
                      ) : (
                        <div className="h-12 w-12 rounded-full bg-gradient-to-br from-orange-500 to-red-600 flex items-center justify-center">
                          <span className="text-white text-lg font-bold">
                            {user.name.charAt(0)}
                          </span>
                        </div>
                      )}
                    </div>

                    {/* 用户信息 */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-3 mb-2">
                        <h4 className="text-lg font-medium text-gray-900">{user.name}</h4>
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                          待审核
                        </span>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                        <div>
                          <span className="font-medium">邮箱:</span> {user.email}
                        </div>
                        {user.realname && (
                          <div>
                            <span className="font-medium">姓名:</span> {user.realname}
                          </div>
                        )}
                        <div>
                          <span className="font-medium">注册时间:</span> {new Date(user.createdAt).toLocaleString('zh-CN')}
                        </div>
                        {user.lastLoginAt && (
                          <div>
                            <span className="font-medium">最后登录:</span> {new Date(user.lastLoginAt).toLocaleString('zh-CN')}
                          </div>
                        )}
                      </div>
                    </div>

                    {/* 操作按钮 */}
                    <div className="flex space-x-3">
                      <Link
                        href={`/admin/users/${user.id}`}
                        className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                      >
                        查看详情
                      </Link>
                      <button
                        onClick={() => handleActivateUser(user.id)}
                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
                      >
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        激活
                      </button>
                      <button
                        onClick={() => handleRejectUser(user.id)}
                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700"
                      >
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                        拒绝
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* 分页 */}
            {usersData.pages > 1 && (
              <div className="px-6 py-4 border-t border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-700">
                    显示第 {(currentPage - 1) * 20 + 1} - {Math.min(currentPage * 20, usersData.total)} 条，
                    共 {usersData.total} 条
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                      className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                    >
                      上一页
                    </button>
                    <span className="px-3 py-1 text-sm">
                      第 {currentPage} / {usersData.pages} 页
                    </span>
                    <button
                      onClick={() => setCurrentPage(prev => Math.min(usersData.pages, prev + 1))}
                      disabled={currentPage === usersData.pages}
                      className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                    >
                      下一页
                    </button>
                  </div>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-12">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">暂无待审核用户</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm ? "没有找到符合搜索条件的待审核用户" : "目前没有需要审核的用户"}
            </p>
            <div className="mt-6">
              <Link
                href="/admin/users"
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
              >
                返回用户管理
              </Link>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
}
