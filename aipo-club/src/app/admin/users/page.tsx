"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import Image from "next/image";
import { redirect } from "next/navigation";
import { api } from "~/trpc/react";
import { AdminLayout } from "~/components/admin-layout";
import { UserLevelBadge, SubscriptionStatusBadge } from "~/components/ui/UserBadges";

export default function AdminUsersPage() {
  const { data: session, status } = useSession();
  const [currentPage, setCurrentPage] = useState(1);
  const [filters, setFilters] = useState({
    status: "",
    role: "",
    search: "",
  });

  // 始终调用所有Hooks，避免条件性调用
  const { data: usersData, isLoading, refetch } = api.user.getUsers.useQuery({
    page: currentPage,
    limit: 10,
    status: (filters.status as "pending" | "active" | "inactive" | "rejected" | undefined) || undefined,
    role: (filters.role as "admin" | "manager" | "member" | undefined) || undefined,
    search: filters.search || undefined,
  }, {
    enabled: !!session,
    staleTime: 60 * 1000, // 1分钟内数据被认为是新鲜的
    gcTime: 5 * 60 * 1000, // 5分钟后从缓存中清除
  });

  // 所有Hooks必须在条件性渲染之前调用
  const updateStatusMutation = api.user.updateUserStatus.useMutation({
    onSuccess: () => {
      void refetch();
    },
    onError: (error) => {
      alert(`操作失败: ${error.message}`);
    },
  });

  const activateUserMutation = api.user.activateUser.useMutation({
    onSuccess: () => {
      void refetch();
      alert("用户已激活，激活通知邮件已发送");
    },
    onError: (error) => {
      alert(`激活失败: ${error.message}`);
    },
  });

  // 在所有Hooks调用之后进行条件性渲染
  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    );
  }

  if (!session) {
    redirect("/login");
  }

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1); // 重置到第一页
  };

  const handleStatusUpdate = async (userId: string, status: string) => {
    if (confirm(`确定要将用户状态更改为"${status}"吗？`)) {
      await updateStatusMutation.mutateAsync({
        userId,
        status: status as any,
      });
    }
  };

  const handleActivateUser = async (userId: string) => {
    if (confirm("确定要激活此用户吗？激活后将自动发送通知邮件。")) {
      await activateUserMutation.mutateAsync({ userId });
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { label: "待审核", color: "bg-yellow-100 text-yellow-800" },
      active: { label: "活跃", color: "bg-green-100 text-green-800" },
      inactive: { label: "非活跃", color: "bg-gray-100 text-gray-800" },
      rejected: { label: "已拒绝", color: "bg-red-100 text-red-800" },
    };
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    );
  };

  const getRoleBadge = (role: string) => {
    const roleConfig = {
      admin: { label: "超管", color: "bg-purple-100 text-purple-800" },
      manager: { label: "经理", color: "bg-blue-100 text-blue-800" },
      member: { label: "成员", color: "bg-gray-100 text-gray-800" },
    };
    const config = roleConfig[role as keyof typeof roleConfig] || roleConfig.member;
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    );
  };

  return (
    <AdminLayout>
      {/* 页面标题和导航 */}
      <div className="mb-6">
        <nav className="flex mb-4" aria-label="Breadcrumb">
          <ol className="flex items-center space-x-4">
            <li>
              <Link href="/users" className="text-gray-400 hover:text-gray-500">
                用户中心
              </Link>
            </li>
            <li>
              <div className="flex items-center">
                <svg className="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
                <span className="ml-4 text-sm font-medium text-gray-500">管理员用户管理</span>
              </div>
            </li>
          </ol>
        </nav>

        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">用户管理</h1>
            <p className="mt-1 text-sm text-gray-600">管理系统中的所有用户，包括审核、状态管理和权限分配</p>
          </div>
          <div className="flex space-x-3">
            <Link
              href="/admin/users/pending"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              待审核用户
            </Link>
            <Link
              href="/users"
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 515.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              用户中心
            </Link>
          </div>
        </div>
      </div>

      {/* 筛选器 */}
      <div className="bg-white shadow rounded-lg p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                搜索
              </label>
              <input
                type="text"
                value={filters.search}
                onChange={(e) => handleFilterChange("search", e.target.value)}
                placeholder="搜索昵称、姓名或邮箱"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                状态
              </label>
              <select
                value={filters.status}
                onChange={(e) => handleFilterChange("status", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value="">全部状态</option>
                <option value="pending">待审核</option>
                <option value="active">活跃</option>
                <option value="inactive">非活跃</option>
                <option value="rejected">已拒绝</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                角色
              </label>
              <select
                value={filters.role}
                onChange={(e) => handleFilterChange("role", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value="">全部角色</option>
                <option value="admin">超管</option>
                <option value="manager">经理</option>
                <option value="member">成员</option>
              </select>
            </div>
          </div>
        </div>

      {/* 用户列表 */}
      <div className="bg-white shadow rounded-lg">
          {isLoading ? (
            <div className="p-8 text-center">
              <div className="text-gray-500">加载中...</div>
            </div>
          ) : usersData?.users && usersData.users.length > 0 ? (
            <>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        用户信息
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        角色
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        状态
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        等级
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        订阅
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        加入时间
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        操作
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {usersData.users.map((user) => (
                      <tr key={user.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10">
                              {user.avatar ? (
                                <Image
                                  className="h-10 w-10 rounded-full"
                                  src={user.avatar}
                                  alt={user.name || "用户头像"}
                                  width={40}
                                  height={40}
                                />
                              ) : (
                                <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                  <span className="text-sm font-medium text-gray-700">
                                    {user.name.charAt(0)}
                                  </span>
                                </div>
                              )}
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">{user.name}</div>
                              <div className="text-sm text-gray-500">{user.email}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {getRoleBadge(user.role)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {getStatusBadge(user.status)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <UserLevelBadge level={user.level as any} />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <SubscriptionStatusBadge status={user.subscriptionStatus as any} />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(user.joinDate).toLocaleDateString('zh-CN')}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <Link
                              href={`/admin/users/${user.id}`}
                              className="text-indigo-600 hover:text-indigo-900"
                            >
                              查看
                            </Link>
                            {user.status === "pending" && (
                              <>
                                <button
                                  onClick={() => handleActivateUser(user.id)}
                                  className="text-green-600 hover:text-green-900"
                                  title="激活用户并发送通知邮件"
                                >
                                  激活
                                </button>
                                <button
                                  onClick={() => handleStatusUpdate(user.id, "rejected")}
                                  className="text-red-600 hover:text-red-900"
                                >
                                  拒绝
                                </button>
                              </>
                            )}
                            {user.status === "active" && (
                              <button
                                onClick={() => handleStatusUpdate(user.id, "inactive")}
                                className="text-yellow-600 hover:text-yellow-900"
                              >
                                停用
                              </button>
                            )}
                            {user.status === "inactive" && (
                              <button
                                onClick={() => handleStatusUpdate(user.id, "active")}
                                className="text-green-600 hover:text-green-900"
                              >
                                启用
                              </button>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* 分页 */}
              {usersData.pages > 1 && (
                <div className="px-6 py-4 border-t border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-700">
                      显示第 {(currentPage - 1) * 10 + 1} - {Math.min(currentPage * 10, usersData.total)} 条，
                      共 {usersData.total} 条
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                        disabled={currentPage === 1}
                        className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                      >
                        上一页
                      </button>
                      <span className="px-3 py-1 text-sm">
                        第 {currentPage} / {usersData.pages} 页
                      </span>
                      <button
                        onClick={() => setCurrentPage(prev => Math.min(usersData.pages, prev + 1))}
                        disabled={currentPage === usersData.pages}
                        className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                      >
                        下一页
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="p-8 text-center">
              <p className="text-gray-500">暂无用户数据</p>
            </div>
          )}
        </div>
    </AdminLayout>
  );
}
