"use client";

import { useState, useEffect, use } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { redirect } from "next/navigation";
import { api } from "~/trpc/react";
import { AdminLayout } from "~/components/admin-layout";
import { ErrorCard, FormError } from "~/components/ui/error";
import { LoadingCard, LoadingButton } from "~/components/ui/loading";

interface UserEditPageProps {
  params: Promise<{ id: string }>;
}

export default function UserEditPage({ params }: UserEditPageProps) {
  const resolvedParams = use(params);
  const { data: session, status } = useSession();
  const router = useRouter();
  const [formData, setFormData] = useState({
    name: "",
    realname: "",
    email: "",
    phone: "",
    bio: "",
    education: "",
    level: "bronze" as "bronze" | "silver" | "gold" | "platinum" | "diamond",
    subscriptionStatus: "trial" as "active" | "expired" | "cancelled" | "trial",
    subscriptionExpiry: "",
    status: "pending" as "pending" | "active" | "inactive" | "rejected",
    role: "member" as "admin" | "manager" | "member",
    skills: [] as string[],
    socialLinks: {} as Record<string, string>,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [skillInput, setSkillInput] = useState("");

  // 始终调用所有Hooks，避免条件性调用
  const { data: userProfile } = api.user.getProfile.useQuery(undefined, {
    enabled: !!session?.user?.id,
  });

  const { data: targetUser, isLoading: userLoading, error } = api.user.getUserById.useQuery({
    id: resolvedParams.id,
  }, {
    enabled: !!resolvedParams.id,
  });

  const updateUserMutation = api.user.updateUser.useMutation({
    onSuccess: () => {
      router.push(`/admin/users/${resolvedParams.id}`);
    },
    onError: (error) => {
      setErrors({ submit: error.message });
    },
  });

  // 当用户数据加载完成时，填充表单
  useEffect(() => {
    if (targetUser) {
      setFormData({
        name: targetUser.name,
        realname: targetUser.realname,
        email: targetUser.email,
        phone: targetUser.phone || "",
        bio: targetUser.bio || "",
        education: targetUser.education || "",
        level: targetUser.level as "bronze" | "silver" | "gold" | "platinum" | "diamond",
        subscriptionStatus: targetUser.subscriptionStatus as "active" | "expired" | "cancelled" | "trial",
        subscriptionExpiry: targetUser.subscriptionExpiry ? new Date(targetUser.subscriptionExpiry).toISOString().split('T')[0]! : "",
        status: targetUser.status as "pending" | "active" | "inactive" | "rejected",
        role: targetUser.role as "admin" | "manager" | "member",
        skills: targetUser.skills || [],
        socialLinks: targetUser.socialLinks || {},
      });
    }
  }, [targetUser]);

  // 在所有Hooks调用之后进行条件性渲染
  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    );
  }

  if (!session) {
    redirect("/login");
  }

  const isAdmin = userProfile?.role === 'admin' || userProfile?.role === 'manager';

  if (!isAdmin) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">权限不足</h3>
          <p className="text-gray-600 mb-4">您没有访问用户管理的权限</p>
          <Link
            href="/dashboard"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
          >
            返回控制台
          </Link>
        </div>
      </AdminLayout>
    );
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    // 清除对应字段的错误
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: "" }));
    }
  };

  const handleSocialLinkChange = (platform: string, url: string) => {
    setFormData(prev => ({
      ...prev,
      socialLinks: {
        ...prev.socialLinks,
        [platform]: url,
      },
    }));
  };

  const addSkill = () => {
    if (skillInput.trim() && !formData.skills.includes(skillInput.trim())) {
      setFormData(prev => ({
        ...prev,
        skills: [...prev.skills, skillInput.trim()],
      }));
      setSkillInput("");
    }
  };

  const removeSkill = (skillToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      skills: prev.skills.filter(skill => skill !== skillToRemove),
    }));
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "用户名不能为空";
    }

    if (!formData.email.trim()) {
      newErrors.email = "邮箱不能为空";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "邮箱格式不正确";
    }

    if (formData.phone && !/^1[3-9]\d{9}$/.test(formData.phone)) {
      newErrors.phone = "手机号格式不正确";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      const updateData = {
        id: resolvedParams.id,
        ...formData,
        subscriptionExpiry: formData.subscriptionExpiry ? new Date(formData.subscriptionExpiry) : undefined,
      };
      await updateUserMutation.mutateAsync(updateData);
    } catch (updateError) {
      // 错误已在 onError 中处理
      console.error("更新用户失败:", updateError);
    } finally {
      setIsLoading(false);
    }
  };

  if (userLoading) {
    return (
      <AdminLayout>
        <LoadingCard message="加载用户信息中..." />
      </AdminLayout>
    );
  }

  if (error || !targetUser) {
    return (
      <AdminLayout>
        <ErrorCard
          title="用户不存在"
          message={error?.message || "请检查用户ID或联系管理员"}
        />
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      {/* 页面标题 */}
      <div className="mb-6">
        <nav className="flex" aria-label="Breadcrumb">
          <ol className="flex items-center space-x-4">
            <li>
              <Link href="/admin/users" className="text-gray-400 hover:text-gray-500">
                用户管理
              </Link>
            </li>
            <li>
              <div className="flex items-center">
                <svg className="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
                <Link href={`/admin/users/${resolvedParams.id}`} className="ml-4 text-gray-400 hover:text-gray-500">
                  {targetUser.name}
                </Link>
              </div>
            </li>
            <li>
              <div className="flex items-center">
                <svg className="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
                <span className="ml-4 text-sm font-medium text-gray-500">编辑</span>
              </div>
            </li>
          </ol>
        </nav>
        <h1 className="mt-2 text-2xl font-semibold text-gray-900">编辑用户</h1>
        <p className="mt-1 text-sm text-gray-600">修改用户信息和权限设置</p>
      </div>

      <div className="max-w-4xl">
        <div className="bg-white shadow rounded-lg p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <FormError errors={errors} />

            {/* 基本信息 */}
            <div>
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">基本信息</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                    昵称 *
                  </label>
                  <div className="mt-1">
                    <input
                      id="name"
                      name="name"
                      type="text"
                      required
                      value={formData.name}
                      onChange={handleChange}
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="realname" className="block text-sm font-medium text-gray-700">
                    姓名 *
                  </label>
                  <div className="mt-1">
                    <input
                      id="realname"
                      name="realname"
                      type="text"
                      required
                      value={formData.realname}
                      onChange={handleChange}
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                  </div>
                </div>



                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                    邮箱 *
                  </label>
                  <div className="mt-1">
                    <input
                      id="email"
                      name="email"
                      type="email"
                      required
                      value={formData.email}
                      onChange={handleChange}
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
                    手机号
                  </label>
                  <div className="mt-1">
                    <input
                      id="phone"
                      name="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={handleChange}
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                  </div>
                </div>

                <div className="md:col-span-2">
                  <label htmlFor="bio" className="block text-sm font-medium text-gray-700">
                    个人简介
                  </label>
                  <div className="mt-1">
                    <textarea
                      id="bio"
                      name="bio"
                      rows={3}
                      value={formData.bio}
                      onChange={handleChange}
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                  </div>
                </div>

                <div className="md:col-span-2">
                  <label htmlFor="education" className="block text-sm font-medium text-gray-700">
                    教育信息
                  </label>
                  <div className="mt-1">
                    <textarea
                      id="education"
                      name="education"
                      rows={3}
                      value={formData.education}
                      onChange={handleChange}
                      placeholder="请输入教育背景"
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* 权限设置 */}
            <div>
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">权限设置</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="status" className="block text-sm font-medium text-gray-700">
                    用户状态
                  </label>
                  <div className="mt-1">
                    <select
                      id="status"
                      name="status"
                      value={formData.status}
                      onChange={handleChange}
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    >
                      <option value="pending">待审核</option>
                      <option value="active">活跃</option>
                      <option value="inactive">非活跃</option>
                      <option value="rejected">已拒绝</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label htmlFor="role" className="block text-sm font-medium text-gray-700">
                    用户角色
                  </label>
                  <div className="mt-1">
                    <select
                      id="role"
                      name="role"
                      value={formData.role}
                      onChange={handleChange}
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    >
                      <option value="member">成员</option>
                      <option value="manager">经理</option>
                      <option value="admin">管理员</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label htmlFor="level" className="block text-sm font-medium text-gray-700">
                    用户等级
                  </label>
                  <div className="mt-1">
                    <select
                      id="level"
                      name="level"
                      value={formData.level}
                      onChange={handleChange}
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    >
                      <option value="bronze">🥉 青铜</option>
                      <option value="silver">🥈 白银</option>
                      <option value="gold">🥇 黄金</option>
                      <option value="platinum">💎 铂金</option>
                      <option value="diamond">💠 钻石</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label htmlFor="subscriptionStatus" className="block text-sm font-medium text-gray-700">
                    订阅状态
                  </label>
                  <div className="mt-1">
                    <select
                      id="subscriptionStatus"
                      name="subscriptionStatus"
                      value={formData.subscriptionStatus}
                      onChange={handleChange}
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    >
                      <option value="trial">试用</option>
                      <option value="active">活跃</option>
                      <option value="expired">已过期</option>
                      <option value="cancelled">已取消</option>
                    </select>
                  </div>
                </div>

                <div className="md:col-span-2">
                  <label htmlFor="subscriptionExpiry" className="block text-sm font-medium text-gray-700">
                    订阅到期时间
                  </label>
                  <div className="mt-1">
                    <input
                      id="subscriptionExpiry"
                      name="subscriptionExpiry"
                      type="date"
                      value={formData.subscriptionExpiry}
                      onChange={handleChange}
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* 技能标签 */}
            <div>
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">技能标签</h3>
              <div className="space-y-4">
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={skillInput}
                    onChange={(e) => setSkillInput(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addSkill())}
                    placeholder="输入技能标签"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  />
                  <button
                    type="button"
                    onClick={addSkill}
                    className="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    添加
                  </button>
                </div>
                {formData.skills.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {formData.skills.map((skill, index) => (
                      <span key={index} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {skill}
                        <button
                          type="button"
                          onClick={() => removeSkill(skill)}
                          className="ml-1 h-3 w-3 rounded-full inline-flex items-center justify-center hover:bg-blue-200 focus:outline-none"
                        >
                          <svg className="h-2 w-2" stroke="currentColor" fill="none" viewBox="0 0 8 8">
                            <path strokeLinecap="round" strokeWidth="1.5" d="m1 1 6 6m0-6L1 7" />
                          </svg>
                        </button>
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* 社交链接 */}
            <div>
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">社交链接</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {['github', 'linkedin', 'twitter', 'website'].map((platform) => (
                  <div key={platform}>
                    <label htmlFor={platform} className="block text-sm font-medium text-gray-700 capitalize">
                      {platform}
                    </label>
                    <div className="mt-1">
                      <input
                        id={platform}
                        type="url"
                        value={formData.socialLinks[platform] || ""}
                        onChange={(e) => handleSocialLinkChange(platform, e.target.value)}
                        className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        placeholder={`https://${platform}.com/username`}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
              <Link
                href={`/admin/users/${resolvedParams.id}`}
                className="bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-300"
              >
                取消
              </Link>
              <LoadingButton
                type="submit"
                loading={isLoading}
                className="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700"
              >
                保存更改
              </LoadingButton>
            </div>
          </form>
        </div>
      </div>
    </AdminLayout>
  );
}
