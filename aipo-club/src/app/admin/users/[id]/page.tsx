"use client";

import { useState, use } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import Image from "next/image";
import { redirect } from "next/navigation";
import { api } from "~/trpc/react";
import { AdminLayout } from "~/components/admin-layout";
import { UserStatusBadge, UserRoleBadge } from "~/components/ui/badge";
import { LoadingCard } from "~/components/ui/loading";
import { ErrorCard } from "~/components/ui/error";
import { UserLevelBadge, SubscriptionStatusBadge, SubscriptionCountdown } from "~/components/ui/UserBadges";
import { ProjectStatusBadge } from "~/components/project/ProjectStatusComponents";
import { getProjectTypeConfig } from "~/types/project";
import { buildThumbnailUrl, buildImageUrl } from "~/lib/oss";

interface UserDetailPageProps {
  params: Promise<{ id: string }>;
}

export default function UserDetailPage({ params }: UserDetailPageProps) {
  const resolvedParams = use(params);
  const { data: session, status } = useSession();
  const [activeTab, setActiveTab] = useState("overview");

  // 始终调用所有Hooks，避免条件性调用
  const { data: userProfile } = api.user.getProfile.useQuery(undefined, {
    enabled: !!session?.user?.id,
  });

  const { data: targetUser, isLoading, error } = api.user.getUserById.useQuery({
    id: resolvedParams.id,
  }, {
    enabled: !!resolvedParams.id && !!session,
  });

  // 查询用户参与的项目
  const { data: projectsData, isLoading: projectsLoading } = api.project.getUserProjects.useQuery({
    userId: resolvedParams.id,
    page: 1,
    limit: 50,
  }, {
    enabled: !!session && activeTab === "projects" && !!resolvedParams.id,
  });

  // 在所有Hooks调用之后进行条件性渲染
  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    );
  }

  if (!session) {
    redirect("/login");
  }

  const isAdmin = userProfile?.role === 'admin' || userProfile?.role === 'manager';

  if (!isAdmin) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">权限不足</h3>
          <p className="text-gray-600 mb-4">您没有访问用户管理的权限</p>
          <Link
            href="/dashboard"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
          >
            返回控制台
          </Link>
        </div>
      </AdminLayout>
    );
  }

  const updateUserMutation = api.user.updateUser.useMutation({
    onSuccess: () => {
      // 刷新数据
      window.location.reload();
    },
  });

  const handleStatusChange = async (newStatus: string) => {
    if (!targetUser) return;

    try {
      await updateUserMutation.mutateAsync({
        id: targetUser.id,
        status: newStatus as "pending" | "active" | "inactive" | "rejected",
      });
    } catch (error) {
      console.error('更新用户状态失败:', error);
    }
  };

  const handleRoleChange = async (newRole: string) => {
    if (!targetUser) return;

    try {
      await updateUserMutation.mutateAsync({
        id: targetUser.id,
        role: newRole as "admin" | "manager" | "member",
      });
    } catch (error) {
      console.error('更新用户角色失败:', error);
    }
  };

  const tabs = [
    { id: "overview", name: "概览", icon: "📊" },
    { id: "projects", name: "项目", icon: "📋" },
    { id: "shares", name: "分成", icon: "💰" },
    { id: "activity", name: "活动", icon: "📈" },
  ];

  if (isLoading) {
    return (
      <AdminLayout>
        <LoadingCard message="加载用户信息中..." />
      </AdminLayout>
    );
  }

  if (error || !targetUser) {
    return (
      <AdminLayout>
        <ErrorCard
          title="用户不存在"
          message={error?.message || "请检查用户ID或联系管理员"}
        />
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      {/* 页面标题和操作 */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <nav className="flex" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-4">
                <li>
                  <Link href="/users" className="text-gray-400 hover:text-gray-500">
                    用户中心
                  </Link>
                </li>
                <li>
                  <div className="flex items-center">
                    <svg className="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                    <Link href="/admin/users" className="ml-4 text-gray-400 hover:text-gray-500">
                      用户管理
                    </Link>
                  </div>
                </li>
                <li>
                  <div className="flex items-center">
                    <svg className="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="ml-4 text-sm font-medium text-gray-500">{targetUser.name}</span>
                  </div>
                </li>
              </ol>
            </nav>
            <div className="mt-2 flex items-center space-x-3">
              <div className="flex items-center space-x-3">
                {targetUser.avatar ? (
                  <Image
                    className="h-12 w-12 rounded-full"
                    src={targetUser.avatar}
                    alt={targetUser.name || "用户头像"}
                    width={48}
                    height={48}
                  />
                ) : (
                  <div className="h-12 w-12 rounded-full bg-gray-300 flex items-center justify-center">
                    <span className="text-lg font-medium text-gray-700">
                      {targetUser.name.charAt(0)}
                    </span>
                  </div>
                )}
                <div>
                  <h1 className="text-2xl font-semibold text-gray-900">{targetUser.name}</h1>
                  <p className="text-sm text-gray-600">{targetUser.email}</p>
                </div>
              </div>
              <div className="flex space-x-2">
                <UserStatusBadge status={targetUser.status} />
                <UserRoleBadge role={targetUser.role} />
              </div>
            </div>
          </div>
          <div className="flex space-x-3">
            <Link
              href={`/users/${targetUser.id}`}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
              用户视图
            </Link>
            <Link
              href={`/admin/users/${targetUser.id}/edit`}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              编辑用户
            </Link>
          </div>
        </div>
      </div>

      {/* 标签页导航 */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`${
                activeTab === tab.id
                  ? "border-indigo-500 text-indigo-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
            >
              <span>{tab.icon}</span>
              <span>{tab.name}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* 标签页内容 */}
      <div className="space-y-6">
        {activeTab === "overview" && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* 用户信息 */}
            <div className="lg:col-span-2 space-y-6">
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">基本信息</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">昵称</dt>
                    <dd className="mt-1 text-sm text-gray-900">{targetUser.name}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">姓名</dt>
                    <dd className="mt-1 text-sm text-gray-900">{targetUser.realname}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">邮箱</dt>
                    <dd className="mt-1 text-sm text-gray-900">{targetUser.email}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">手机号</dt>
                    <dd className="mt-1 text-sm text-gray-900">{targetUser.phone || "未设置"}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">用户等级</dt>
                    <dd className="mt-1">
                      <UserLevelBadge level={targetUser.level as "bronze" | "silver" | "gold" | "platinum" | "diamond"} />
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">订阅状态</dt>
                    <dd className="mt-1">
                      <SubscriptionStatusBadge status={targetUser.subscriptionStatus as "active" | "expired" | "cancelled" | "trial"} />
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">注册时间</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {new Date(targetUser.createdAt).toLocaleDateString('zh-CN')}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">最后登录</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {targetUser.lastLoginAt ? new Date(targetUser.lastLoginAt).toLocaleDateString('zh-CN') : "从未登录"}
                    </dd>
                  </div>
                </div>
                {targetUser.subscriptionExpiry && (
                  <div className="mt-4">
                    <SubscriptionCountdown user={targetUser as any} />
                  </div>
                )}
              </div>

              {/* 个人资料 */}
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">个人资料</h3>
                <div className="space-y-4">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">个人简介</dt>
                    <dd className="mt-1 text-sm text-gray-900">{targetUser.bio || "暂无简介"}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">教育信息</dt>
                    <dd className="mt-1 text-sm text-gray-900">{targetUser.education || "暂无教育信息"}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">技能标签</dt>
                    <dd className="mt-1">
                      {targetUser.skills && targetUser.skills.length > 0 ? (
                        <div className="flex flex-wrap gap-2">
                          {targetUser.skills.map((skill, index) => (
                            <span key={index} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              {skill}
                            </span>
                          ))}
                        </div>
                      ) : (
                        <span className="text-sm text-gray-500">暂无技能标签</span>
                      )}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">社交链接</dt>
                    <dd className="mt-1">
                      {targetUser.socialLinks && Object.keys(targetUser.socialLinks).length > 0 ? (
                        <div className="space-y-2">
                          {Object.entries(targetUser.socialLinks).map(([platform, url]) => (
                            <div key={platform} className="flex items-center space-x-2">
                              <span className="text-sm text-gray-500 capitalize">{platform}:</span>
                              <a href={url as string} target="_blank" rel="noopener noreferrer" className="text-sm text-indigo-600 hover:text-indigo-500">
                                {url as string}
                              </a>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <span className="text-sm text-gray-500">暂无社交链接</span>
                      )}
                    </dd>
                  </div>
                </div>
              </div>
            </div>

            {/* 侧边栏 */}
            <div className="space-y-6">
              {/* 快速操作 */}
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">快速操作</h3>
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">用户状态</label>
                    <select
                      value={targetUser.status}
                      onChange={(e) => handleStatusChange(e.target.value)}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    >
                      <option value="pending">待审核</option>
                      <option value="active">活跃</option>
                      <option value="inactive">非活跃</option>
                      <option value="rejected">已拒绝</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">用户角色</label>
                    <select
                      value={targetUser.role}
                      onChange={(e) => handleRoleChange(e.target.value)}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    >
                      <option value="member">成员</option>
                      <option value="manager">经理</option>
                      <option value="admin">管理员</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* 统计信息 */}
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">统计信息</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">创建项目</span>
                    <span className="text-sm font-medium text-gray-900">0</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">参与项目</span>
                    <span className="text-sm font-medium text-gray-900">0</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">分成记录</span>
                    <span className="text-sm font-medium text-gray-900">0</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">总收益</span>
                    <span className="text-sm font-medium text-gray-900">¥0</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === "projects" && (
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg leading-6 font-medium text-gray-900">用户项目</h3>
              <p className="mt-1 text-sm text-gray-500">该用户创建或参与的项目列表</p>
            </div>

            {projectsLoading ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto mb-4"></div>
                <div className="text-gray-500">加载项目列表中...</div>
              </div>
            ) : projectsData?.projects && projectsData.projects.length > 0 ? (
              <div className="divide-y divide-gray-200">
                {projectsData.projects.map((project) => (
                    <div key={project.id} className="p-6 hover:bg-gray-50">
                      <div className="flex items-start space-x-4">
                        {/* 项目Logo */}
                        <div className="flex-shrink-0">
                          {(project as Record<string, unknown>).logo ? (
                            <div className="relative w-12 h-12 rounded-lg overflow-hidden bg-gray-100 border border-gray-200">
                              <Image
                                src={buildThumbnailUrl((project as Record<string, unknown>).logo as string, 48, 48)}
                                alt={`${project.name} Logo`}
                                fill
                                className="object-cover"
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.src = buildImageUrl((project as Record<string, unknown>).logo as string);
                                }}
                              />
                            </div>
                          ) : (
                            <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center">
                              <span className="text-white text-lg font-bold">
                                {project.name.charAt(0)}
                              </span>
                            </div>
                          )}
                        </div>

                        {/* 项目信息 */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-3 mb-2">
                            <h4 className="text-lg font-medium text-gray-900 truncate">
                              {project.name}
                            </h4>
                            <span className="text-sm text-gray-500 font-mono">({project.code})</span>
                            <ProjectStatusBadge status={project.status} />
                            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                              {getProjectTypeConfig(project.type).label}
                            </span>
                          </div>

                          <p className="text-gray-600 text-sm line-clamp-2 mb-3">
                            {project.description || "暂无描述"}
                          </p>

                          <div className="flex items-center space-x-6 text-sm text-gray-500">
                            <div className="flex items-center">
                              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                              </svg>
                              创建者: {(project as Record<string, unknown>).createdBy as string || "未知"}
                            </div>
                            <div className="flex items-center">
                              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 515.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                              </svg>
                              成员: {project.memberCount}
                            </div>
                            {project.totalRevenue > 0 && (
                              <div className="flex items-center">
                                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                </svg>
                                收益: ¥{project.totalRevenue.toLocaleString()}
                              </div>
                            )}
                            <div className="flex items-center">
                              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                              </svg>
                              创建: {new Date(project.createdAt).toLocaleDateString('zh-CN')}
                            </div>
                          </div>
                        </div>

                        {/* 操作按钮 */}
                        <div className="flex-shrink-0">
                          <Link
                            href={`/projects/${project.id}`}
                            className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                          >
                            查看详情
                          </Link>
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">暂无项目</h3>
                <p className="mt-1 text-sm text-gray-500">该用户还没有创建或参与任何项目</p>
              </div>
            )}
          </div>
        )}

        {activeTab === "shares" && (
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">分成记录</h3>
            <div className="text-center py-12">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">暂无分成记录</h3>
              <p className="mt-1 text-sm text-gray-500">该用户还没有任何分成记录</p>
            </div>
          </div>
        )}

        {activeTab === "activity" && (
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">活动记录</h3>
            <div className="text-center py-12">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">暂无活动记录</h3>
              <p className="mt-1 text-sm text-gray-500">该用户还没有任何活动记录</p>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
}
