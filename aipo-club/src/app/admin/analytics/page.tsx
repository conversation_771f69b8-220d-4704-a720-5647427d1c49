"use client";


import Image from "next/image";
import { api } from "~/trpc/react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
// import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
// import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { formatCurrency, formatDate } from "~/lib/utils";
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  FolderOpen, 
  DollarSign, 
  PieChart,
  // Activity,
  Target
} from "lucide-react";

export default function AnalyticsPage() {
  // const [timeRange, setTimeRange] = useState("30");

  const { data: platformStats } = api.user.getPlatformStats.useQuery();
  const { data: projectsData } = api.project.getProjects.useQuery({ limit: 1000 });
  const projects = projectsData?.projects ?? [];

  // 计算统计数据
  const activeProjects = projects?.filter(p => p.status === "active").length ?? 0;
  const totalUsers = platformStats?.totalUsers ?? 0;
  const activeUsers = platformStats?.activeUsers ?? 0;
  const totalRevenue = projects?.reduce((sum, p) => sum + (p.totalRevenue ?? 0), 0) ?? 0;
  const monthlyRevenue = projects?.reduce((sum, p) => sum + (p.currentMonthRevenue ?? 0), 0) ?? 0;

  // 项目状态分布
  const projectStatusCounts = projects?.reduce((acc, project) => {
    acc[project.status] = (acc[project.status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>) ?? {};

  // 用户角色分布
  const userRoleCounts = platformStats?.usersByRole ?? {};

  // 收入排行榜
  const revenueRanking = projects
    ?.filter(p => (p.totalRevenue ?? 0) > 0)
    ?.sort((a, b) => (b.totalRevenue ?? 0) - (a.totalRevenue ?? 0))
    ?.slice(0, 10) ?? [];

  const STATUS_LABELS = {
    ideation: "创意阶段",
    planning: "规划中",
    development: "开发中",
    testing: "测试中",
    active: "运营中",
    suspended: "暂停",
    completed: "已完成",
    cancelled: "已取消",
  };

  const ROLE_LABELS = {
    admin: "管理员",
    manager: "经理",
    member: "成员",
  };

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold flex items-center gap-2">
          <BarChart3 className="h-8 w-8" />
          平台分析
        </h1>
        <p className="text-muted-foreground">平台运营数据和项目统计分析</p>
      </div>

      {/* 关键指标概览 */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-sm">
              <Users className="h-4 w-4" />
              用户总数
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalUsers}</div>
            <p className="text-sm text-muted-foreground">
              活跃用户 {activeUsers}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-sm">
              <FolderOpen className="h-4 w-4" />
              活跃项目
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeProjects}</div>
            <p className="text-sm text-muted-foreground">
              总项目 {projects?.length ?? 0}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-sm">
              <DollarSign className="h-4 w-4" />
              总收入
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalRevenue)}</div>
            <p className="text-sm text-muted-foreground">
              本月 {formatCurrency(monthlyRevenue)}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-sm">
              <Target className="h-4 w-4" />
              平台手续费
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(totalRevenue * 0.05)}
            </div>
            <p className="text-sm text-muted-foreground">5% 手续费率</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">数据概览</TabsTrigger>
          <TabsTrigger value="projects">项目分析</TabsTrigger>
          <TabsTrigger value="users">用户分析</TabsTrigger>
          <TabsTrigger value="revenue">收入分析</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6">
          <div className="grid gap-6 lg:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5" />
                  项目状态分布
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(projectStatusCounts).map(([status, count]) => (
                    <div key={status} className="flex items-center justify-between">
                      <span className="text-sm">
                        {STATUS_LABELS[status as keyof typeof STATUS_LABELS] || status}
                      </span>
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">{count}</span>
                        <div className="w-20 h-2 bg-gray-200 rounded-full">
                          <div
                            className="h-2 bg-blue-500 rounded-full"
                            style={{
                              width: `${(count / (projects?.length ?? 1)) * 100}%`,
                            }}
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  用户角色分布
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(userRoleCounts).map(([role, count]) => (
                    <div key={role} className="flex items-center justify-between">
                      <span className="text-sm">
                        {ROLE_LABELS[role as keyof typeof ROLE_LABELS] || role}
                      </span>
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">{count}</span>
                        <div className="w-20 h-2 bg-gray-200 rounded-full">
                          <div
                            className="h-2 bg-green-500 rounded-full"
                            style={{
                              width: `${(count / totalUsers) * 100}%`,
                            }}
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="projects" className="mt-6">
          <div className="grid gap-6">
            <Card>
              <CardHeader>
                <CardTitle>项目列表</CardTitle>
                <CardDescription>所有项目的详细信息和状态</CardDescription>
              </CardHeader>
              <CardContent>
                {projects && projects.length > 0 ? (
                  <div className="space-y-4">
                    {projects.map((project) => (
                      <div
                        key={project.id}
                        className="flex items-center justify-between p-4 border rounded-lg"
                      >
                        <div className="flex items-center gap-3">
                          {project.logo && (
                            <Image
                              src={project.logo}
                              alt={project.name}
                              width={40}
                              height={40}
                              className="h-10 w-10 rounded object-cover"
                            />
                          )}
                          <div>
                            <div className="font-medium">{project.name}</div>
                            <div className="text-sm text-muted-foreground">
                              {project.description}
                            </div>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge variant="outline">
                                {STATUS_LABELS[project.status as keyof typeof STATUS_LABELS] || project.status}
                              </Badge>
                              <Badge variant="secondary">{project.type}</Badge>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-medium">
                            收入: {formatCurrency(project.totalRevenue ?? 0)}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            本月: {formatCurrency(project.currentMonthRevenue ?? 0)}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            创建: {formatDate(project.createdAt)}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <FolderOpen className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>暂无项目数据</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="users" className="mt-6">
          <div className="grid gap-6">
            <Card>
              <CardHeader>
                <CardTitle>用户统计</CardTitle>
                <CardDescription>用户注册和活跃度统计</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="p-4 border rounded-lg text-center">
                    <div className="text-2xl font-bold text-blue-600">{totalUsers}</div>
                    <div className="text-sm text-muted-foreground">总用户数</div>
                  </div>
                  <div className="p-4 border rounded-lg text-center">
                    <div className="text-2xl font-bold text-green-600">{activeUsers}</div>
                    <div className="text-sm text-muted-foreground">活跃用户</div>
                  </div>
                  <div className="p-4 border rounded-lg text-center">
                    <div className="text-2xl font-bold text-orange-600">
                      {totalUsers > 0 ? Math.round((activeUsers / totalUsers) * 100) : 0}%
                    </div>
                    <div className="text-sm text-muted-foreground">活跃率</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="revenue" className="mt-6">
          <div className="grid gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  收入排行榜
                </CardTitle>
                <CardDescription>收入最高的项目排名</CardDescription>
              </CardHeader>
              <CardContent>
                {revenueRanking.length > 0 ? (
                  <div className="space-y-4">
                    {revenueRanking.map((project, index) => (
                      <div
                        key={project.id}
                        className="flex items-center justify-between p-4 border rounded-lg"
                      >
                        <div className="flex items-center gap-3">
                          <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 text-sm font-bold">
                            {index + 1}
                          </div>
                          {project.logo && (
                            <Image
                              src={project.logo}
                              alt={project.name}
                              width={40}
                              height={40}
                              className="h-10 w-10 rounded object-cover"
                            />
                          )}
                          <div>
                            <div className="font-medium">{project.name}</div>
                            <div className="text-sm text-muted-foreground">
                              {project.type} • {project.status}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-bold">
                            {formatCurrency(project.totalRevenue ?? 0)}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            本月: {formatCurrency(project.currentMonthRevenue ?? 0)}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <DollarSign className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>暂无收入数据</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>收入总览</CardTitle>
                <CardDescription>平台总体收入统计</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="p-4 border rounded-lg">
                    <div className="text-sm text-muted-foreground mb-1">总收入</div>
                    <div className="text-2xl font-bold">{formatCurrency(totalRevenue)}</div>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <div className="text-sm text-muted-foreground mb-1">本月收入</div>
                    <div className="text-2xl font-bold">{formatCurrency(monthlyRevenue)}</div>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <div className="text-sm text-muted-foreground mb-1">平台手续费收入</div>
                    <div className="text-2xl font-bold">{formatCurrency(totalRevenue * 0.05)}</div>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <div className="text-sm text-muted-foreground mb-1">平均项目收入</div>
                    <div className="text-2xl font-bold">
                      {formatCurrency(projects?.length ? totalRevenue / projects.length : 0)}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}