# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

AIPO俱乐部 (AIPO Club) is a full-stack web application built with the T3 Stack for digital nomad project management. It's a comprehensive platform featuring user management, project lifecycle tracking, team collaboration, and profit-sharing capabilities.

**Tech Stack**: Next.js 15 (App Router) + TypeScript + tRPC + Prisma + NextAuth.js + Tailwind CSS + MySQL 8.0 + Aliyun OSS

## Development Commands

```bash
# Development
npm run dev                    # Start development server with Turbo (http://localhost:3000)
npm run build                  # Build for production
npm run start                  # Start production server
npm run preview                # Build and start (for testing production build)

# Code Quality
npm run lint                   # Run ESLint
npm run lint:fix              # Fix ESLint issues automatically
npm run typecheck              # TypeScript type checking
npm run check                  # Run both lint and typecheck
npm run format:check           # Check Prettier formatting
npm run format:write           # Apply Prettier formatting

# Database
npm run db:push                # Push schema changes to database
npm run db:generate            # Generate Prisma client and run migrations (dev mode)
npm run db:migrate             # Deploy migrations to production
npm run db:studio              # Open Prisma Studio for database management

# Testing Scripts
node scripts/test-auth-system.js           # Test authentication system
node scripts/test-project-management.js    # Test project management features
node scripts/test-email-service.js         # Test email functionality
node scripts/test-database-models.js       # Test database models
node scripts/test-financial-system.js      # Test financial/payment features
node scripts/test-task-management.js       # Test task management
node scripts/test-api-endpoints.js         # Test all API endpoints
```

## Architecture Overview

### Core Structure
- **App Router**: All pages in `src/app/` using Next.js 15 App Router
- **API Layer**: tRPC routers in `src/server/api/routers/` provide type-safe APIs
- **Database**: Prisma ORM with MySQL, schema in `prisma/schema.prisma`
- **Auth**: NextAuth.js v5 with email/password authentication
- **File Storage**: Aliyun OSS integration for file uploads
- **Email**: SMTP service for notifications and password reset

### Key Directories
```
src/
├── app/                      # Pages (App Router)
│   ├── admin/               # Admin management pages
│   ├── dashboard/           # User dashboard
│   ├── projects/            # Project management
│   ├── profile/             # User profile
│   └── api/                 # API routes (NextAuth, upload, health)
├── server/
│   ├── api/routers/         # tRPC API implementations
│   ├── auth/                # NextAuth configuration
│   ├── db.ts                # Database connection
│   └── email.ts             # Email service
├── components/
│   ├── ui/                  # Reusable UI components
│   └── project/             # Project-specific components
└── lib/                     # Utilities (OSS, validation, etc.)
```

### Database Models (Snake Case)
- **User System**: `user`, `account`, `session`, `verification_token`, `password_reset_token`
- **Projects**: `project`, `project_member`, `project_milestone`, `project_task`
- **Finance & Profit Sharing**: `project_share`, `share_payout`, `payment_account`, `project_revenue`, `payout`, `financial_account`, `financial_transaction`, `project_budget`, `financial_report`
- **Task Management**: `project_task`, `task_dependency`, `task_comment`, `task_time_entry`, `task_attachment`
- **Cost Management**: `cost_allocation`, `cost_allocation_detail`
- **Invitations**: `invite_code`, `invite_code_usage`
- **Platform**: `platform_transaction`

### tRPC API Routers
- `user` - Authentication, profile management, user CRUD, password reset
- `project` - Project CRUD, member management, status tracking, milestones
- `share` - Profit sharing rules, payout management
- `payment` - Payment accounts, transaction processing
- `finance` - Financial tracking, budget management, revenue records
- `post` - Example/demo functionality

## Development Guidelines

### Code Conventions
- **Database**: Snake_case table/column names (e.g., `project_member`, `created_at`)
- **TypeScript**: Strict mode enabled, comprehensive type safety
- **Styling**: Tailwind CSS with component-based approach
- **File Organization**: Group by feature/domain, keep related files together

### Environment Setup
Required environment variables are defined in `src/env.js` using Zod validation:
- **Database**: `DATABASE_URL` (MySQL connection string)
- **Auth**: `AUTH_SECRET` (required in production)
- **SMTP**: `SMTP_HOST`, `SMTP_PORT`, `SMTP_SECURE`, `SMTP_USER`, `SMTP_PASS`, `SMTP_FROM_NAME`, `SMTP_FROM_EMAIL`
- **OSS**: `OSS_ACCESS_KEY_ID`, `OSS_ACCESS_KEY_SECRET`, `OSS_BUCKET`, `OSS_REGION`, `NEXT_PUBLIC_OSS_BASE_URL`
- **Stripe**: `STRIPE_SECRET_KEY`, `STRIPE_WEBHOOK_SECRET`, `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY`

### Common Development Tasks

**Adding New API Endpoints**:
1. Create/modify router in `src/server/api/routers/`
2. Register in `src/server/api/root.ts`
3. Use Zod for input validation
4. Follow existing patterns for error handling

**Database Changes**:
1. Modify `prisma/schema.prisma`
2. Run `npm run db:push` to apply changes
3. Update related tRPC procedures and types

**Adding New Pages**:
1. Create page in `src/app/` following App Router conventions
2. Use server components where possible
3. Implement proper authentication checks with NextAuth

**File Uploads**:
- Use existing OSS integration in `src/lib/aliyun-oss.ts`
- Files uploaded to `/api/upload` endpoint
- Supports images with automatic optimization

### Testing & Quality Assurance
- Always run `npm run check` before committing (lint + typecheck)
- Use `npm run db:studio` for database inspection
- Test with different user roles (admin, manager, member)
- Verify email functionality in development

### Authentication Flow
- NextAuth.js v5 with custom email/password provider
- User status: PENDING → ACTIVE/REJECTED (admin approval required)
- Roles: ADMIN, MANAGER, MEMBER with hierarchical permissions
- Password reset via secure token system with expiration
- Invite code system for user registration

### Project Features
- **11 Project Statuses**: ideation → planning → development → testing → deployment → promotion → profitable → maintenance → decline → completed/cancelled
- **Hierarchical Projects**: Support for parent-child project relationships
- **Comprehensive Task Management**: Tasks, dependencies, time tracking, comments, attachments
- **Financial Tracking**: Revenue tracking, profit sharing, payment processing, budget management
- **Advanced Reporting**: Financial reports, cost allocation, transaction history

### Build and Deployment
- **Standalone Output**: Configured for standalone deployment
- **Startup Scripts**: 
  - `npm run start` - Uses standalone server (production)
  - `npm run start:next` - Uses Next.js start (development)
  - `npm run preview` - Build and run standalone server
- **Docker Ready**: Optimized for containerized deployment

### Build Optimizations
- **Webpack Configuration**: Optimized for OSS dependencies and client-side exclusions
- **Image Optimization**: Uses Next.js Image component for performance
- **Module Exclusions**: Coffee-script and vm2 warnings suppressed
- **Type Safety**: Comprehensive TypeScript types with minimal `any` usage

### Important Notes
- **Default Port**: Development server runs on port 3000 (not 3001 as shown in some docs)
- **Snake Case Database**: All database tables and columns use snake_case naming
- **Comprehensive Testing**: 7 test scripts cover all major system components
- **Production Ready**: Includes Docker configuration, standalone build output
- **Type Safety**: Full TypeScript coverage with strict mode enabled
- **ESLint Warnings**: Remaining warnings are primarily for `any` types in form handlers (non-critical)

This codebase is production-ready with comprehensive error handling, type safety, and modern development practices.