# Since the ".env" file is gitignored, you can use the ".env.example" file to
# build a new ".env" file when you clone the repo. Keep this file up-to-date
# when you add new variables to `.env`.

# This file will be committed to version control, so make sure not to have any
# secrets in it. If you are cloning this repo, create a copy of this file named
# ".env" and populate it with your secrets.

# When adding additional environment variables, the schema in "/src/env.js"
# should be updated accordingly.

# Next Auth
# You can generate a new secret on the command line with:
# npx auth secret
# https://next-auth.js.org/configuration/options#secret
AUTH_SECRET=""

# Next Auth Discord Provider
AUTH_DISCORD_ID=""
AUTH_DISCORD_SECRET=""

# Prisma
# https://www.prisma.io/docs/reference/database-reference/connection-urls#env
# 优化的数据库连接URL，包含连接池参数
# 示例: mysql://user:password@localhost:3306/database?connection_limit=20&pool_timeout=10&socket_timeout=60
DATABASE_URL=""

# 邮件服务配置 (SMTP)
SMTP_HOST=
SMTP_PORT=
SMTP_SECURE=
SMTP_USER=
SMTP_PASS=
SMTP_FROM_NAME=
SMTP_FROM_EMAIL=

# OSS配置（阿里云OSS示例）
# 服务端配置（敏感信息，不要暴露给前端）
OSS_ACCESS_KEY_ID="your-access-key-id"
OSS_ACCESS_KEY_SECRET="your-access-key-secret"
OSS_BUCKET="your-bucket-name"
OSS_REGION="oss-cn-hangzhou"

# 前端配置（可以暴露给前端的信息）
NEXT_PUBLIC_OSS_ENDPOINT="oss-cn-hangzhou.aliyuncs.com"
NEXT_PUBLIC_OSS_BASE_URL="https://your-bucket.oss-cn-hangzhou.aliyuncs.com"
NEXT_PUBLIC_OSS_BUCKET="your-bucket-name"
NEXT_PUBLIC_OSS_REGION="oss-cn-hangzhou"

# 或者使用腾讯云COS
# COS_SECRET_ID="your-secret-id"
# COS_SECRET_KEY="your-secret-key"
# COS_BUCKET="your-bucket-name"
# COS_REGION="ap-beijing"
# NEXT_PUBLIC_COS_BASE_URL="https://your-bucket.cos.ap-beijing.myqcloud.com"

# 或者使用AWS S3
# AWS_ACCESS_KEY_ID="your-access-key-id"
# AWS_SECRET_ACCESS_KEY="your-secret-access-key"
# AWS_BUCKET="your-bucket-name"
# AWS_REGION="us-east-1"
# NEXT_PUBLIC_S3_BASE_URL="https://your-bucket.s3.amazonaws.com"
